# 变更管理系统维护文档

## 1. 简介
### 1.1 文档目的
该文档用于描述变更管理系统的维护流程。
### 1.2 软件概述
该软件用于管理变更，包括变更/封网的创建、审核、执行、关闭、查询、统计等功能。

## 2. 软件结构概述
该软件采用前后端分离架构，前端使用Vue.js进行开发，后端使用Spring Boot进行开发。
列出系统主要的模块和组件
### 2.1 前端模块

```
cec-changemanagement-frontend/
├── src/
│   ├── App.vue                     # 根组件
│   ├── main.ts                     # 主入口文件
│   ├── api/                        # API 接口定义
│   ├── assets/                     # 静态资源文件 (图片、字体、全局样式等)
│   ├── components/                 # 公共组件 (复用性高的组件)
│   ├── enums/                      # 枚举定义
│   ├── hooks/                      # 自定义 Hooks
│   ├── layouts/                    # 布局组件 (通常是页面的整体框架)
│   ├── locales/                    # 国际化相关配置
│   ├── plugins/                    # 插件配置
│   ├── router/                     # 路由配置
│   ├── settings/                   # 系统配置
│   └── views/                  # 页面组件 (业务页面)
├── public/                     # 公共静态资源
├── build/                      # 构建配置文件
├── types/                      # TypeScript 类型定义
├── package.json                # 项目依赖配置
├── vite.config.ts             # Vite 构建配置
├── tailwind.config.js         # Tailwind CSS 配置
└── .env.*                     # 环境变量配置
```

#### 2.1.1 技术栈
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI 库**: Naive UI
- **样式**: Tailwind CSS + Less
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **国际化**: Vue I18n

### 2.2 后端模块

```
changeManagement-backend/
├── changeManagement-admin/         # 启动模块 (应用入口)
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/cec/       # 主启动类和Web控制器
│   │   │   │   ├── ChangeManagementApplication.java    # Spring Boot启动类
│   │   │   │   ├── ChangeManagementServletInitializer.java # Web容器部署配置
│   │   │   │   └── web/controller/ # 通用控制器 (首页等)
│   │   │   └── resources/          # 配置文件和静态资源
├── changeManagement-common/        # 通用模块 (公共组件和工具)
│   ├── changeManagement-common-bom/        # 依赖管理BOM
│   ├── changeManagement-common-core/       # 核心工具类
│   ├── changeManagement-common-web/        # Web相关配置
│   ├── changeManagement-common-mybatis/    # MyBatis增强配置
│   ├── changeManagement-common-redis/      # Redis缓存配置
│   ├── changeManagement-common-satoken/    # 权限认证配置
│   ├── changeManagement-common-security/   # 安全相关配置
│   ├── changeManagement-common-log/        # 日志记录配置
│   ├── changeManagement-common-excel/      # Excel导入导出
│   ├── changeManagement-common-oss/        # 对象存储配置
│   ├── changeManagement-common-mail/       # 邮件发送配置
│   ├── changeManagement-common-doc/        # API文档配置
│   ├── changeManagement-common-json/       # JSON处理配置
│   ├── changeManagement-common-encrypt/    # 数据加密配置
│   ├── changeManagement-common-sensitive/  # 敏感数据处理
│   ├── changeManagement-common-translation/ # 数据翻译配置
│   ├── changeManagement-common-tenant/     # 多租户配置
│   ├── changeManagement-common-idempotent/ # 幂等性配置
│   ├── changeManagement-common-ratelimiter/ # 限流配置
│   ├── changeManagement-common-social/     # 第三方登录配置
│   └── pom.xml                     # 通用模块依赖管理
├── changeManagement-modules/       # 业务模块 (核心业务逻辑)
│   ├── changeManagement-system/    # 系统管理模块
│   ├── changeManagement-business/  # 业务逻辑模块
├── script/                         # 部署脚本和数据库文件
│   ├── bin/                        # 启动停止脚本
│   │   └── change-management-admin.sh  # 应用启停脚本
│   └── sql/                        # 数据库脚本
│       ├── change-management.sql   # 主数据库脚本
│       ├── dbdiagram.io.sql        # 数据库设计图
│       └── update.sql              # 数据库更新脚本
├── pom.xml                         # 主Maven配置文件
```

#### 2.2.1 技术栈
- **框架**: Spring Boot 3.4.4 + Java 17
- **Web服务器**: Undertow (高性能Web服务器)
- **数据库**: MySQL 8.0 + MyBatis Plus 3.5.11
- **缓存**: Redis + Redisson 3.45.1
- **权限认证**: Sa-Token 1.40.0 (JWT)
- **数据源**: Dynamic DataSource 4.3.1 (多数据源)
- **对象存储**: AWS S3 SDK 2.28.22 (兼容MinIO)
- **API文档**: SpringDoc OpenAPI 2.8.5
- **Excel处理**: EasyExcel 4.0.3
- **邮件**: Spring Boot Mail
- **工具库**: Hutool 5.8.35
- **对象映射**: MapStruct Plus 1.4.6
- **分布式锁**: Lock4j 2.2.7
- **加密**: BouncyCastle 1.76
- **IP定位**: ip2region 2.7.0
- **构建工具**: Maven 3.x

#### 2.2.2 核心特性
- **微服务架构**: 采用Maven多模块设计，模块间职责清晰
- **多环境配置**: 支持dev/uat/prod多环境配置
- **权限控制**: 基于Sa-Token的RBAC权限模型
- **数据安全**: 支持数据加密、敏感信息脱敏
- **接口加密**: 支持API请求/响应加密
- **缓存策略**: Redis分布式缓存，支持集群模式
- **文件存储**: 支持本地存储和对象存储(MinIO/S3)
- **API文档**: 自动生成Swagger文档
- **数据导入导出**: 支持Excel批量操作
- **邮件通知**: 支持HTML邮件模板
- **国际化**: 支持多语言国际化
- **防重提交**: 接口幂等性保护
- **限流控制**: 支持接口限流
- **日志审计**: 完整的操作日志记录

#### 2.2.3 模块说明

**启动模块 (changeManagement-admin)**
- 应用程序入口，包含主启动类
- 环境配置文件管理
- Web容器配置
- 通用控制器

**通用模块 (changeManagement-common)**
- 核心工具类和常量定义
- 统一异常处理和响应格式
- 数据库、缓存、安全等基础配置
- 公共组件和中间件集成

**系统模块 (changeManagement-system)**
- 用户、角色、权限管理
- 菜单和路由管理
- 系统参数配置
- 操作日志和登录日志


**业务模块 (changeManagement-business)**
- 变更管理：变更申请、审核、执行流程
- 封网管理：封网申请、审批、执行
- 应用管理：应用信息维护和分类管理
- 配置管理：系统配置参数管理
- 仪表板：数据统计和可视化展示
- 邮件服务：业务邮件通知

### 2.3 数据库
详见数据字典

## 3. 运维职责
#### 3.1 运维目标
确保应用正常运行，提供稳定、可靠、安全的服务。
#### 3.2 软硬件要求

##### 3.2.1 硬件要求

**应用服务器 (changeManagement-backend)**
- **CPU**: 至少8核处理器，推荐16核以上
- **内存**: 至少16GB运行内存，推荐32GB以上
- **磁盘**: 至少100GB硬盘存储空间，推荐SSD

**数据库服务器 (MySQL)**
- **CPU**: 至少8核处理器，推荐16核以上
- **内存**: 至少32GB运行内存，推荐64GB以上
- **磁盘**: 至少500GB硬盘存储空间，强烈推荐SSD

**缓存服务器 (Redis)**
- **CPU**: 至少4核处理器，推荐8核以上
- **内存**: 至少16GB运行内存，推荐32GB以上
- **磁盘**: 至少100GB硬盘存储空间，推荐SSD

**对象存储服务器 (MinIO)**
- **CPU**: 至少8核处理器，推荐16核以上
- **内存**: 至少32GB运行内存，推荐64GB以上
- **磁盘**: 至少1TB硬盘存储空间，推荐多块SSD组RAID

**负载均衡服务器 (Nginx)**
- **CPU**: 至少4核处理器，推荐8核以上
- **内存**: 至少8GB运行内存，推荐16GB以上
- **磁盘**: 至少100GB硬盘存储空间

**监控服务器 (Spring Boot Admin + SnailJob)**
- **CPU**: 至少4核处理器，推荐8核以上
- **内存**: 至少16GB运行内存，推荐32GB以上
- **磁盘**: 至少200GB硬盘存储空间

**前端服务器 (Vue.js)**
- **CPU**: 至少4核处理器
- **内存**: 至少8GB运行内存
- **磁盘**: 至少50GB硬盘存储空间

**注意事项**：
- 考虑运行在正式生产环境，所有服务器CPU建议均不要低于4核处理器
- 内存配置应考虑并发用户数和数据量增长
- 磁盘空间应预留足够的增长空间，建议至少保留30%空闲空间
- 生产环境建议配置双机热备或集群部署

##### 3.2.2 软件要求

**操作系统**
- **推荐**: Ubuntu 20.04 LTS 或 Ubuntu 22.04 LTS
- **备选**: CentOS 7/8, Red Hat Enterprise Linux 7/8
- **最低要求**: 64位操作系统，内核版本3.10以上

**Java运行环境**
- **版本**: OpenJDK 17 或 Oracle JDK 17

**数据库软件**
- **MySQL**: 8.0.x (推荐8.0.35以上)
- **最低要求**: MySQL 5.7.x
- **字符集**: UTF8MB4
- **存储引擎**: InnoDB

**缓存软件**
- **Redis**: 6.x 或 7.x (推荐7.0以上)
- **最低要求**: Redis 5.x
- **部署模式**: 支持单机、主从、集群模式

**Web服务器**
- **Nginx**: 1.18.x 或更高版本
- **最低要求**: Nginx 1.16.x
- **模块**: 需要支持upstream、ssl、gzip等模块

**对象存储**
- **MinIO**: RELEASE.2023-xx-xx 或更高版本
- **备选**: AWS S3、阿里云OSS、腾讯云COS
- **协议**: 兼容S3 API

**构建工具**
- **Maven**: 3.6.x 或更高版本
- **Node.js**: 16.x 或 18.x (前端构建)
- **npm/yarn**: 最新稳定版本

**SSL证书**
- **类型**: 支持TLS 1.2及以上
- **推荐**: 通配符证书或EV证书
- **更新**: 定期更新，避免过期

**网络要求**
- **带宽**: 至少100Mbps，推荐1Gbps
- **延迟**: 内网延迟小于1ms
- **防火墙**: 开放必要端口 (8080, 3306, 6379, 9000等)
- **DNS**: 配置内网DNS解析

**安全要求**
- **防火墙**: 启用并正确配置
- **SELinux/AppArmor**: 根据安全策略配置
- **用户权限**: 使用非root用户运行应用
- **日志审计**: 启用系统和应用日志
- **备份策略**: 定期备份数据库和配置文件

##### 3.2.3 环境配置要求

**开发环境**
- **CPU**: 至少4核处理器
- **内存**: 至少8GB运行内存
- **磁盘**: 至少100GB硬盘存储空间
- **操作系统**: Ubuntu 20.04 LTS 或 Windows 10/11
- **用途**: 代码开发、单元测试、功能调试

**测试环境 (UAT)**
- **CPU**: 至少8核处理器
- **内存**: 至少32GB运行内存
- **磁盘**: 至少100GB硬盘存储空间
- **操作系统**: Ubuntu 20.04 LTS
- **用途**: 集成测试、性能测试、用户验收测试

**生产环境 (PROD)**
- **CPU**: 至少16核处理器
- **内存**: 至少32GB运行内存
- **磁盘**: 至少1TB硬盘存储空间
- **操作系统**: Ubuntu 20.04 LTS
- **用途**: 正式业务运行环境
- **高可用**: 双机热备或集群部署

**灾备环境**
- **配置**: 与生产环境相同
- **用途**: 灾难恢复、数据备份
- **同步**: 实时或定时数据同步

##### 3.2.4 网络端口要求

**应用服务端口**
- **8080**: Spring Boot应用主端口
- **443**: 前端应用端口

**数据库端口**
- **3306**: MySQL数据库端口
- **6379**: Redis缓存端口

**Web服务端口**
- **80**: HTTP端口
- **443**: HTTPS端口
- **9000**: MinIO对象存储端口

**注意事项**：
- 生产环境建议关闭不必要的端口
- 使用防火墙限制端口访问权限
- 定期检查端口安全性
- 监控端口使用情况

3.3 运维流程

#### 3.3.1 部署前准备
列出部署前需要完成的检查项：
- 保代码通过测试
- 确保备份最新

#### 3.3.2 部署步骤
****该步骤请配合产品部署文档使用！****

## 4. 服务日志分析

后端日志路径，安装包下logs/sys-console.log
uat: /home/<USER>/changeManagement-server/logs/，通过tail 或者vi命令实时或精准查找方式查询错误日志的信息






