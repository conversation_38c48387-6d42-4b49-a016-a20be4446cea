# 变更管理系统网络拓扑图

## 网络拓扑结构

```
Internet
    │
    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    DMZ 区域 (对外服务)                           │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │                  Nginx (Host Network)                       ││
│  │                 Port: 80, 443                              ││
│  │  ┌─────────────────────────────────────────────────────────┐││
│  │  │              SSL Termination                            │││
│  │  │         /docker/nginx/cert/*                           │││
│  │  └─────────────────────────────────────────────────────────┘││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
    │
    ▼ (Load Balance)
┌─────────────────────────────────────────────────────────────────┐
│                   应用服务区域 (Host Network)                    │
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ ruoyi-server1   │  │ ruoyi-server2   │  │ ruoyi-serverN   │  │
│  │   Port: 8080    │  │   Port: 8081    │  │   Port: 808N    │  │
│  │                 │  │                 │  │                 │  │
│  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │
│  │ │Spring Boot  │ │  │ │Spring Boot  │ │  │ │Spring Boot  │ │  │
│  │ │Application  │ │  │ │Application  │ │  │ │Application  │ │  │
│  │ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│           │                     │                     │         │
└─────────────────────────────────────────────────────────────────┘
            │                     │                     │
            ▼                     ▼                     ▼
┌─────────────────────────────────────────────────────────────────┐
│                    管理监控区域 (Host Network)                   │
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐                      │
│  │monitor-admin    │  │snailjob-server  │                      │
│  │  Port: 9090     │  │Port: 8800,17888 │                      │
│  │                 │  │                 │                      │
│  │ ┌─────────────┐ │  │ ┌─────────────┐ │                      │
│  │ │Spring Boot  │ │  │ │分布式任务    │ │                      │
│  │ │Admin        │ │  │ │调度平台      │ │                      │
│  │ └─────────────┘ │  │ └─────────────┘ │                      │
│  └─────────────────┘  └─────────────────┘                      │
└─────────────────────────────────────────────────────────────────┘
            │                     │
            ▼                     ▼
┌─────────────────────────────────────────────────────────────────┐
│                    第三方服务区域                                │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │                   SmartFlow 审批流                          ││
│  │                 (外部第三方服务)                            ││
│  │                                                             ││
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        ││
│  │  │Flow Engine  │  │Process Mgmt │  │Task Manager │        ││
│  │  └─────────────┘  └─────────────┘  └─────────────┘        ││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────────┘
            │
            ▼ (API调用)
┌─────────────────────────────────────────────────────────────────┐
│                   数据存储区域 (Host Network)                    │
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   MySQL 双主     │  │  Redis 集群     │  │  MinIO 集群     │  │
│  │   Port: 3306    │  │  Port: 6379     │  │Port: 9000,9001  │  │
│  │                 │  │                 │  │                 │  │
│  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │
│  │ │   Master1   │◄┼──┼►│   Node1     │ │  │ │   Node1     │ │  │
│  │ │             │ │  │ │             │ │  │ │             │ │  │
│  │ │   Master2   │ │  │ │   Node2     │ │  │ │   Node2     │ │  │
│  │ │             │ │  │ │             │ │  │ │             │ │  │
│  │ │ (主主复制)   │ │  │ │   Node3     │ │  │ │   Node3     │ │  │
│  │ └─────────────┘ │  │ │ (分片集群)   │ │  │ │ (分布式存储) │ │  │
│  └─────────────────┘  │ └─────────────┘ │  │ └─────────────┘ │  │
│                       └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## 网络配置详情

### 端口映射表
```
┌─────────────────┬──────────┬─────────────────────────────────┐
│     服务名称     │   端口   │            功能描述              │
├─────────────────┼──────────┼─────────────────────────────────┤
│ nginx-web       │ 80, 443  │ HTTP/HTTPS 入口，SSL终结        │
│ ruoyi-server1   │ 8080     │ 主应用服务实例1                 │
│ ruoyi-server2   │ 8081     │ 主应用服务实例2                 │
│ monitor-admin   │ 9090     │ Spring Boot Admin监控           │
│ snailjob-server │8800,17888│ 分布式任务调度                  │
│ redis           │ 6379     │ 缓存服务                        │
│ mysql           │ 3306     │ 主数据库                        │
│ minio           │9000,9001 │ 对象存储服务                    │
└─────────────────┴──────────┴─────────────────────────────────┘
```

### 网络通信流
```
1. 用户请求流:
   Browser → Nginx(80/443) → ruoyi-server(8080/8081)

2. 数据访问流:
   ruoyi-server → MySQL(3306) [主数据]
   ruoyi-server → Redis(6379)  [缓存数据]
   ruoyi-server → MinIO(9000)  [文件存储]

3. 审批流程流:
   ruoyi-server → SmartFlow API [审批引擎]

4. 监控管理流:
   monitor-admin(9090) → ruoyi-server [健康检查]
   snailjob-server(8800) → ruoyi-server [任务调度]

5. 集群内通信:
   MySQL Master1 ↔ MySQL Master2 [主主同步]
   Redis Node1 ↔ Node2 ↔ Node3 [集群通信]
   MinIO Node1 ↔ Node2 ↔ Node3 [分布式存储]
```

### 安全边界
```
┌─────────────────────────────────────────────────────────────────┐
│ 防火墙规则                                                       │
├─────────────────────────────────────────────────────────────────┤
│ 外网 → DMZ: 80, 443 (HTTPS Only)                               │
│ DMZ → APP: 8080, 8081                                          │
│ APP → DB: 3306, 6379, 9000                                     │
│ APP → 第三方: SmartFlow API (HTTPS)                             │
│ 管理网 → Monitor: 9090, 8800, 17888                            │
└─────────────────────────────────────────────────────────────────┘