# Excalidraw AI 架构图生成提示词

## 整体架构图提示词
```
Create a system architecture diagram for a Change Management System with these components:

Frontend: Vue3 + TypeScript (port 80/443)
Load Balancer: Nginx 
Application Layer: 
- ruoyi-server1 (port 8080)
- ruoyi-server2 (port 8081)
- ruoyi-serverN (port 808N)

Monitoring:
- monitor-admin (port 9090)
- snailjob-server (port 8800, 17888)

Third-party Services:
- SmartFlow (approval workflow engine)

Data Layer:
- MySQL Master-Master (port 3306)
- Redis Cluster (port 6379) 
- MinIO Cluster (port 9000, 9001)

Show the connections and data flow between components.
```

## 网络拓扑图提示词
```
Draw a network topology diagram showing:

DMZ Zone:
- Nginx Load Balancer (80, 443)

Application Zone:
- Multiple Spring Boot servers (8080, 8081, 808N)
- Host network mode

Management Zone:
- Spring Boot Admin (9090)
- SnailJob Scheduler (8800, 17888)

Data Zone:
- MySQL dual-master setup (3306)
- Redis cluster 3 nodes (6379)
- MinIO cluster 3 nodes (9000, 9001)

External Services:
- SmartFlow API

Show network boundaries and security zones.
```

## 业务流程图提示词
```
Create a business process flow diagram for:

Change Management Process:
1. User submits change request
2. System validation
3. SmartFlow approval workflow
4. Implementation scheduling
5. Execution and monitoring
6. Completion verification

Network Freeze Process:
1. Freeze request submission
2. Impact assessment
3. Approval workflow
4. Freeze implementation
5. Monitoring and alerts
6. Freeze release

Show decision points and parallel processes.
```