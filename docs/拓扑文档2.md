# 变更管理系统部署架构文档

<div align="center">

![系统架构](https://img.shields.io/badge/架构-高可用集群-blue)
![版本](https://img.shields.io/badge/版本-v1.0-green)
![状态](https://img.shields.io/badge/状态-生产环境-red)

</div>

---

## 📋 目录

- [1. 文档概述](#1-文档概述)
- [2. 系统架构](#2-系统架构)
- [3. 基础设施](#3-基础设施)
- [4. 服务组件](#4-服务组件)
- [5. 网络配置](#5-网络配置)
- [6. 运维指南](#6-运维指南)

---

## 1. 文档概述

### 1.1 📝 编写目的

本文档详细描述变更管理系统的部署架构，包括：
- 🏗️ 系统整体架构设计
- 🖥️ 硬件资源配置
- 🔧 软件组件部署
- 🌐 网络拓扑结构

旨在为运维人员和开发人员提供清晰的系统架构视图，促进团队协作，提升运维效率。

### 1.2 🎯 适用范围

**目标读者：**
- 🔧 运维工程师
- 💻 开发工程师
- 🏗️ 系统架构师
- 📊 项目管理人员

### 1.3 📚 术语定义

<table>
<tr>
<th width="15%">术语</th>
<th width="25%">全称</th>
<th width="60%">说明</th>
</tr>
<tr>
<td><strong>Docker</strong></td>
<td>容器化平台</td>
<td>开源的应用容器引擎，用于应用程序的开发、交付和运行</td>
</tr>
<tr>
<td><strong>Redis</strong></td>
<td>Remote Dictionary Server</td>
<td>高性能的key-value数据库，用作缓存和会话存储</td>
</tr>
<tr>
<td><strong>MinIO</strong></td>
<td>对象存储服务</td>
<td>高性能、S3兼容的分布式对象存储系统</td>
</tr>
<tr>
<td><strong>Nginx</strong></td>
<td>Web服务器</td>
<td>高性能的HTTP服务器和反向代理服务器</td>
</tr>
<tr>
<td><strong>Keepalived</strong></td>
<td>高可用服务</td>
<td>基于VRRP协议的高可用解决方案</td>
</tr>
<tr>
<td><strong>MySQL</strong></td>
<td>关系型数据库</td>
<td>开源的关系型数据库管理系统</td>
</tr>
<tr>
<td><strong>SmartFlow</strong></td>
<td>工作流引擎</td>
<td>基于Camunda的业务流程管理引擎</td>
</tr>
</table>

---

## 2. 系统架构

### 2.1 🏗️ 整体架构图

```mermaid
graph TB
    subgraph "负载均衡层"
        VIP[VIP: ************]
        N1[Nginx-97:8081]
        N2[Nginx-98:8081]
    end

    subgraph "应用层"
        APP1[Backend-97:8080]
        APP2[Backend-98:8080]
        SF[SmartFlow-98:38684]
    end

    subgraph "数据层"
        subgraph "Redis集群"
            R1[Redis-97:7379/8379/9379]
            R2[Redis-98:7379/8379/9379]
        end

        subgraph "MySQL集群"
            M1[MySQL-97:4306]
            M2[MySQL-98:4307]
        end

        subgraph "对象存储"
            MIN1[MinIO-97:9090]
            MIN2[MinIO-98:9090]
        end
    end

    VIP --> N1
    VIP --> N2
    N1 --> APP1
    N2 --> APP2
    APP1 --> R1
    APP2 --> R2
    APP1 --> M1
    APP2 --> M2
    APP1 --> MIN1
    APP2 --> MIN2
    APP1 --> SF
    APP2 --> SF
```

### 2.2 💻 基础设施配置

#### 2.2.1 硬件资源

<table>
<tr>
<th width="20%">服务器IP</th>
<th width="25%">操作系统</th>
<th width="15%">CPU</th>
<th width="15%">内存</th>
<th width="15%">存储</th>
<th width="10%">角色</th>
</tr>
<tr>
<td><code>************</code></td>
<td>Ubuntu 24.04.2 LTS</td>
<td>12核</td>
<td>32GB</td>
<td>500GB</td>
<td>主节点</td>
</tr>
<tr>
<td><code>************</code></td>
<td>Ubuntu 24.04.2 LTS</td>
<td>12核</td>
<td>32GB</td>
<td>500GB</td>
<td>从节点</td>
</tr>
</table>

#### 2.2.2 网络配置

<table>
<tr>
<th width="25%">网络类型</th>
<th width="25%">IP地址</th>
<th width="50%">说明</th>
</tr>
<tr>
<td><strong>虚拟IP (VIP)</strong></td>
<td><code>************</code></td>
<td>Nginx负载均衡器的浮动IP地址</td>
</tr>
<tr>
<td><strong>主服务器</strong></td>
<td><code>************</code></td>
<td>承载主要服务组件的物理服务器</td>
</tr>
<tr>
<td><strong>从服务器</strong></td>
<td><code>************</code></td>
<td>承载备份服务组件的物理服务器</td>
</tr>
</table>

---

## 3. 基础设施

### 3.1 🔄 负载均衡层

#### Nginx反向代理

<table>
<tr>
<th width="15%">组件</th>
<th width="15%">版本</th>
<th width="20%">部署方式</th>
<th width="20%">配置</th>
<th width="30%">说明</th>
</tr>
<tr>
<td rowspan="2"><strong>Nginx</strong></td>
<td rowspan="2">1.24.0-2ubuntu7.4</td>
<td rowspan="2">APT安装</td>
<td>VIP: ************<br/>配置目录: /etc/nginx/</td>
<td>高可用负载均衡器</td>
</tr>
</table>

<table>
<tr>
<th width="20%">服务器</th>
<th width="15%">端口</th>
<th width="15%">角色</th>
<th width="25%">状态</th>
<th width="25%">备注</th>
</tr>
<tr>
<td><code>************</code></td>
<td>8081</td>
<td>🟢 主节点</td>
<td>Active</td>
<td>主要流量处理</td>
</tr>
<tr>
<td><code>************</code></td>
<td>8081</td>
<td>🟡 备节点</td>
<td>Standby</td>
<td>故障转移备份</td>
</tr>
</table>

---

## 4. 服务组件

### 4.1 🗄️ 数据存储层

#### 4.1.1 Redis分片集群

> **架构特点：** 3主3从分片集群，提供高可用缓存服务

<table>
<tr>
<th colspan="4" style="background-color: #f0f8ff;">📊 集群概览</th>
</tr>
<tr>
<td><strong>版本</strong></td>
<td>v6.2.19</td>
<td><strong>部署方式</strong></td>
<td>Docker容器</td>
</tr>
<tr>
<td><strong>集群模式</strong></td>
<td>分片集群</td>
<td><strong>节点数量</strong></td>
<td>6个节点 (3主3从)</td>
</tr>
</table>

**配置信息：**
```yaml
# Redis集群连接配置
spring:
  redis:
    cluster:
      nodes:
        - ************:7379  # 主节点1
        - ************:8379  # 主节点2
        - ************:9379  # 主节点3
        - ************:7379  # 从节点1
        - ************:8379  # 从节点2
        - ************:9379  # 从节点3
```

**节点分布：**
<table>
<tr>
<th width="20%">服务器</th>
<th width="15%">端口</th>
<th width="20%">角色</th>
<th width="20%">分片</th>
<th width="25%">状态</th>
</tr>
<tr>
<td rowspan="3"><code>************</code></td>
<td>7379</td>
<td>🔴 Master</td>
<td>Shard-1</td>
<td>✅ Running</td>
</tr>
<tr>
<td>8379</td>
<td>🔴 Master</td>
<td>Shard-2</td>
<td>✅ Running</td>
</tr>
<tr>
<td>9379</td>
<td>🔴 Master</td>
<td>Shard-3</td>
<td>✅ Running</td>
</tr>
<tr>
<td rowspan="3"><code>************</code></td>
<td>7379</td>
<td>🔵 Slave</td>
<td>Shard-1</td>
<td>✅ Running</td>
</tr>
<tr>
<td>8379</td>
<td>🔵 Slave</td>
<td>Shard-2</td>
<td>✅ Running</td>
</tr>
<tr>
<td>9379</td>
<td>🔵 Slave</td>
<td>Shard-3</td>
<td>✅ Running</td>
</tr>
</table>

#### 4.1.2 MinIO对象存储集群

> **架构特点：** 分布式对象存储，S3兼容API，支持高可用和数据冗余

<table>
<tr>
<th colspan="4" style="background-color: #f0f8ff;">📦 存储概览</th>
</tr>
<tr>
<td><strong>版本</strong></td>
<td>RELEASE.2024-01-11T07-46-16Z</td>
<td><strong>部署方式</strong></td>
<td>Docker容器</td>
</tr>
<tr>
<td><strong>集群模式</strong></td>
<td>分布式集群</td>
<td><strong>API兼容</strong></td>
<td>Amazon S3</td>
</tr>
</table>

**节点配置：**
<table>
<tr>
<th width="20%">服务器</th>
<th width="15%">API端口</th>
<th width="15%">控制台端口</th>
<th width="20%">角色</th>
<th width="30%">访问地址</th>
</tr>
<tr>
<td><code>************</code></td>
<td>9090</td>
<td>19090</td>
<td>🟢 存储节点1</td>
<td>http://************:19090</td>
</tr>
<tr>
<td><code>************</code></td>
<td>9090</td>
<td>19090</td>
<td>🟢 存储节点2</td>
<td>http://************:19090</td>
</tr>
</table>

#### 4.1.3 MySQL数据库集群

> **架构特点：** 双主架构，提供高可用的关系型数据库服务

<table>
<tr>
<th colspan="4" style="background-color: #f0f8ff;">🗃️ 数据库概览</th>
</tr>
<tr>
<td><strong>版本</strong></td>
<td>8.0.40</td>
<td><strong>架构模式</strong></td>
<td>双主双从</td>
</tr>
<tr>
<td><strong>复制方式</strong></td>
<td>异步复制</td>
<td><strong>故障转移</strong></td>
<td>自动</td>
</tr>
</table>

**实例配置：**
<table>
<tr>
<th width="20%">服务器</th>
<th width="15%">端口</th>
<th width="20%">角色</th>
<th width="20%">状态</th>
<th width="25%">连接字符串</th>
</tr>
<tr>
<td><code>************</code></td>
<td>4306</td>
<td>🔴 主节点1</td>
<td>✅ Active</td>
<td>mysql://************:4306</td>
</tr>
<tr>
<td><code>************</code></td>
<td>4307</td>
<td>🔴 主节点2</td>
<td>✅ Active</td>
<td>mysql://************:4307</td>
</tr>
</table>

### 4.2 🚀 应用服务层

#### 4.2.1 changeManagement-backend核心应用

> **架构特点：** Spring Boot微服务架构，双节点部署，支持负载均衡

<table>
<tr>
<th colspan="4" style="background-color: #f0f8ff;">🎯 应用概览</th>
</tr>
<tr>
<td><strong>框架</strong></td>
<td>Spring Boot 3.x</td>
<td><strong>部署方式</strong></td>
<td>JAR包部署</td>
</tr>
<tr>
<td><strong>架构模式</strong></td>
<td>微服务架构</td>
<td><strong>负载均衡</strong></td>
<td>Nginx反向代理</td>
</tr>
</table>

**服务实例：**
<table>
<tr>
<th width="20%">服务器</th>
<th width="15%">端口</th>
<th width="25%">访问地址</th>
<th width="15%">角色</th>
<th width="25%">健康检查</th>
</tr>
<tr>
<td><code>************</code></td>
<td>8080</td>
<td>http://************:8080</td>
<td>🟢 主实例</td>
<td>/actuator/health</td>
</tr>
<tr>
<td><code>************</code></td>
<td>8080</td>
<td>http://************:8080</td>
<td>🟢 备实例</td>
<td>/actuator/health</td>
</tr>
</table>

#### 4.2.2 SmartFlow工作流引擎

> **架构特点：** 基于Camunda的工作流引擎，提供业务流程管理能力

<table>
<tr>
<th colspan="4" style="background-color: #f0f8ff;">⚙️ 工作流概览</th>
</tr>
<tr>
<td><strong>引擎</strong></td>
<td>Camunda BPM</td>
<td><strong>部署方式</strong></td>
<td>独立部署</td>
</tr>
<tr>
<td><strong>协议</strong></td>
<td>BPMN 2.0</td>
<td><strong>API</strong></td>
<td>REST API</td>
</tr>
</table>

**服务配置：**
<table>
<tr>
<th width="20%">服务器</th>
<th width="15%">端口</th>
<th width="30%">访问地址</th>
<th width="15%">角色</th>
<th width="20%">管理界面</th>
</tr>
<tr>
<td><code>************</code></td>
<td>38684</td>
<td>http://************:38684</td>
<td>🔧 工作流引擎</td>
<td>/camunda</td>
</tr>
</table>

---

## 5. 网络配置

### 5.1 🌐 端口分配表

<table>
<tr>
<th width="15%">服务</th>
<th width="15%">服务器</th>
<th width="15%">端口</th>
<th width="20%">协议</th>
<th width="35%">用途说明</th>
</tr>
<tr>
<td rowspan="2"><strong>Nginx</strong></td>
<td>************</td>
<td>8081</td>
<td>HTTP</td>
<td>负载均衡器主节点</td>
</tr>
<tr>
<td>************</td>
<td>8081</td>
<td>HTTP</td>
<td>负载均衡器备节点</td>
</tr>
<tr>
<td rowspan="2"><strong>Backend</strong></td>
<td>************</td>
<td>8080</td>
<td>HTTP</td>
<td>变更管理后端服务</td>
</tr>
<tr>
<td>************</td>
<td>8080</td>
<td>HTTP</td>
<td>变更管理后端服务</td>
</tr>
<tr>
<td rowspan="6"><strong>Redis</strong></td>
<td rowspan="3">************</td>
<td>7379</td>
<td>TCP</td>
<td>Redis主节点1</td>
</tr>
<tr>
<td>8379</td>
<td>TCP</td>
<td>Redis主节点2</td>
</tr>
<tr>
<td>9379</td>
<td>TCP</td>
<td>Redis主节点3</td>
</tr>
<tr>
<td rowspan="3">************</td>
<td>7379</td>
<td>TCP</td>
<td>Redis从节点1</td>
</tr>
<tr>
<td>8379</td>
<td>TCP</td>
<td>Redis从节点2</td>
</tr>
<tr>
<td>9379</td>
<td>TCP</td>
<td>Redis从节点3</td>
</tr>
<tr>
<td rowspan="4"><strong>MinIO</strong></td>
<td rowspan="2">************</td>
<td>9090</td>
<td>HTTP</td>
<td>对象存储API服务</td>
</tr>
<tr>
<td>19090</td>
<td>HTTP</td>
<td>管理控制台</td>
</tr>
<tr>
<td rowspan="2">************</td>
<td>9090</td>
<td>HTTP</td>
<td>对象存储API服务</td>
</tr>
<tr>
<td>19090</td>
<td>HTTP</td>
<td>管理控制台</td>
</tr>
<tr>
<td rowspan="2"><strong>MySQL</strong></td>
<td>************</td>
<td>4306</td>
<td>TCP</td>
<td>数据库主节点1</td>
</tr>
<tr>
<td>************</td>
<td>4307</td>
<td>TCP</td>
<td>数据库主节点2</td>
</tr>
<tr>
<td><strong>SmartFlow</strong></td>
<td>************</td>
<td>38684</td>
<td>HTTP</td>
<td>工作流引擎服务</td>
</tr>
</table>

---

## 6. 运维指南

### 6.1 🔍 监控检查

#### 服务健康检查命令

```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查Redis集群状态
redis-cli -c -h ************ -p 7379 cluster nodes

# 检查MySQL状态
mysqladmin -h ************ -P 4306 -u root -p status

# 检查MinIO状态
curl http://************:9090/minio/health/live

# 检查应用状态
curl http://************:8080/actuator/health
```

### 6.2 📊 性能指标

<table>
<tr>
<th width="20%">组件</th>
<th width="20%">关键指标</th>
<th width="20%">正常范围</th>
<th width="40%">监控命令</th>
</tr>
<tr>
<td><strong>CPU使用率</strong></td>
<td>系统负载</td>
<td>&lt; 80%</td>
<td><code>top</code>, <code>htop</code></td>
</tr>
<tr>
<td><strong>内存使用率</strong></td>
<td>可用内存</td>
<td>&gt; 20%</td>
<td><code>free -h</code></td>
</tr>
<tr>
<td><strong>磁盘使用率</strong></td>
<td>可用空间</td>
<td>&gt; 20%</td>
<td><code>df -h</code></td>
</tr>
<tr>
<td><strong>网络连接</strong></td>
<td>连接数</td>
<td>正常范围</td>
<td><code>netstat -an</code></td>
</tr>
</table>

### 6.3 🚨 故障处理

#### 常见问题及解决方案

1. **服务无法启动**
   - 检查端口占用：`netstat -tulpn | grep :端口号`
   - 检查配置文件语法
   - 查看服务日志

2. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证网络连通性
   - 确认用户权限

3. **Redis集群异常**
   - 检查集群节点状态
   - 验证网络分区
   - 重新平衡分片

---

<div align="center">

**📝 文档版本：** v1.0 | **📅 更新时间：** 2025-01-31 | **👥 维护团队：** 运维组

</div>

