# 拓扑文档

## 第一章 引言

### 1.1 编写目的

此文档描述涉及变更管理系统环境部署的软件系统进行说明，包括系统的架构、网络等。使运维人员和开发人员对各环境形成清晰一致的理解，便于人员之间的沟通和协作，从而提升运维团队的整体效率。

### 1.2 适用范围

此文档阅读对象包括运维工程师，开发工程师。

### 1.3 名称术语

|     名词     |                          解释                          |
|:----------:|:----------------------------------------------------:|
|   docker   | 一个开源的平台用于开发、交付和运行应用程序。它能够在Windows，macOS，Linux计算机上运行。 |
|   redis    |                      Redis应用缓存。                      |
|   minIO    |                  一种高性能、S3 兼容的对象存储。                   |
|   nginx    |                  在本平台提供负载均衡、web服务。                   |
| keepalived |                      检测死连接的机制服务                      |
|   mysql    |                       双主节点数据库                        |
| SmartFlow  |                   基于camunda的工作流引擎                    |

## 第二章 部署架构

### 2.1 系统架构图

![img.png](img.png)

### 2.2 系统硬件配置

| ip           |        服务器         |             配置              |
|--------------|:------------------:|:---------------------------:|
| ************ | Ubuntu 24.04.2 LTS | CPU: 12核/内存: 32G/磁盘容量: 500G |
| ************ | Ubuntu 24.04.2 LTS | CPU: 12核/内存: 32G/磁盘容量: 500G |    

### 2.3 系统软件配置

#### 2.3.1 Redis分片集群
- **版本**: v6.2.19
- **部署方式**: Docker
- **集群分布**:
  - 主：************
  - 从：************
- **配置信息**:
  ```yaml
  # redis集群:
  dbAddress: [ ************:7379, ************:8379, ************:9379, ************:7379, ************:8379, ************:9379 ]
  ```

<table>
<tr>
<th>服务器</th>
<th>端口</th>
<th>角色</th>
</tr>
<tr>
<td rowspan="3">************</td>
<td>7379</td>
<td>主节点1</td>
</tr>
<tr>
<td>8379</td>
<td>主节点2</td>
</tr>
<tr>
<td>9379</td>
<td>主节点3</td>
</tr>
<tr>
<td rowspan="3">************</td>
<td>7379</td>
<td>从节点1</td>
</tr>
<tr>
<td>8379</td>
<td>从节点2</td>
</tr>
<tr>
<td>9379</td>
<td>从节点3</td>
</tr>
</table>

#### 2.3.2 MinIO集群
- **版本**: RELEASE.2024-01-11T07-46-16Z
- **部署方式**: Docker
- **集群分布**: 10.180.89.[97/98]
- **端口**: 9090, 19090

<table>
<tr>
<th>服务器</th>
<th>服务端口</th>
<th>管理端口</th>
<th>角色</th>
</tr>
<tr>
<td>************</td>
<td>9090</td>
<td>19090</td>
<td>节点1</td>
</tr>
<tr>
<td>************</td>
<td>9090</td>
<td>19090</td>
<td>节点2</td>
</tr>
</table>

#### 2.3.3 Nginx负载均衡
- **版本**: 1.24.0-2ubuntu7.4
- **部署方式**: apt安装
- **VIP**: ************
- **配置文件目录**: /etc/nginx/

<table>
<tr>
<th>IP地址</th>
<th>实例</th>
<th>端口映射</th>
<th>角色</th>
</tr>
<tr>
<td>************</td>
<td>nginx</td>
<td>8081</td>
<td>主</td>
</tr>
<tr>
<td>************</td>
<td>nginx</td>
<td>8081</td>
<td>备</td>
</tr>
</table>

#### 2.3.4 MySQL数据库
- **版本**: 8.0.40
- **部署架构**: 双主双从
- **实例信息**:

<table>
<tr>
<th>服务器</th>
<th>端口</th>
<th>角色</th>
</tr>
<tr>
<td>************</td>
<td>4306</td>
<td>主节点1</td>
</tr>
<tr>
<td>************</td>
<td>4307</td>
<td>主节点2</td>
</tr>
</table>

#### 2.3.5 SmartFlow工作流引擎
- **部署方式**: Docker/独立部署
- **访问地址**: http://************:38684
- **部署服务器**: ************

<table>
<tr>
<th>服务器</th>
<th>端口</th>
<th>访问地址</th>
<th>角色</th>
</tr>
<tr>
<td>************</td>
<td>38684</td>
<td>http://************:38684</td>
<td>工作流引擎</td>
</tr>
</table>

#### 2.3.6 changeManagement-backend应用
- **部署方式**: Spring Boot应用
- **部署架构**: 双节点部署
- **端口**: 8080

<table>
<tr>
<th>服务器</th>
<th>端口</th>
<th>访问地址</th>
<th>角色</th>
</tr>
<tr>
<td>************</td>
<td>8080</td>
<td>http://************:8080</td>
<td>应用节点1</td>
</tr>
<tr>
<td>************</td>
<td>8080</td>
<td>http://************:8080</td>
<td>应用节点2</td>
</tr>
</table>

