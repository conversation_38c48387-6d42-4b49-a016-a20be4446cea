# 变更管理系统部署架构文档

## 目录

- [1. 文档概述](#1-文档概述)
- [2. 系统架构](#2-系统架构)
- [3. 基础设施](#3-基础设施)
- [4. 服务组件](#4-服务组件)
- [5. 网络配置](#5-网络配置)
- [6. 运维指南](#6-运维指南)

## 1. 文档概述

### 1.1 编写目的

本文档详细描述变更管理系统的部署架构，包括：
- 系统整体架构设计
- 硬件资源配置
- 软件组件部署
- 网络拓扑结构

旨在为运维人员和开发人员提供清晰的系统架构视图，促进团队协作，提升运维效率。

### 1.2 适用范围

**目标读者：**
- 运维工程师
- 开发工程师
- 系统架构师
- 项目管理人员

### 1.3 术语定义

| 术语 | 全称 | 说明 |
|:---:|:---:|:---|
| **Docker** | 容器化平台 | 开源的应用容器引擎，用于应用程序的开发、交付和运行 |
| **Redis** | Remote Dictionary Server | 高性能的key-value数据库，用作缓存和会话存储 |
| **MinIO** | 对象存储服务 | 高性能、S3兼容的分布式对象存储系统 |
| **Nginx** | Web服务器 | 高性能的HTTP服务器和反向代理服务器 |
| **Keepalived** | 高可用服务 | 基于VRRP协议的高可用解决方案 |
| **MySQL** | 关系型数据库 | 开源的关系型数据库管理系统 |
| **SmartFlow** | 工作流引擎 | 基于Camunda的业务流程管理引擎 |

## 2. 系统架构

### 2.1 整体架构图

系统采用三层架构设计：

![img.png](img.png)

**负载均衡层：**
- VIP: ************
- Nginx-97:443 (主节点)
- Nginx-98:443 (备节点)

**应用层：**
- Backend-97:8080 (应用节点1)
- Backend-98:8080 (应用节点2)
- SmartFlow-98:38684 (工作流引擎)

**数据层：**
- Redis集群 (3主3从)
- MySQL集群 (双主架构)
- MinIO对象存储集群

### 2.2 基础设施配置

#### 2.2.1 硬件资源

| 服务器IP | 操作系统 | CPU | 内存 | 存储 | 角色 |
|:---:|:---:|:---:|:---:|:---:|:---:|
| ************ | Ubuntu 24.04.2 LTS | 12核 | 32GB | 500GB | 主节点 |
| ************ | Ubuntu 24.04.2 LTS | 12核 | 32GB | 500GB | 从节点 |

#### 2.2.2 网络配置

| 网络类型 | IP地址 | 说明 |
|:---:|:---:|:---|
| **虚拟IP (VIP)** | ************ | Nginx负载均衡器的浮动IP地址 |
| **主服务器** | ************ | 承载主要服务组件的物理服务器 |
| **从服务器** | ************ | 承载备份服务组件的物理服务器 |

## 3. 基础设施

### 3.1 负载均衡层

#### Nginx反向代理

**组件信息：**

| 组件 | 版本 | 部署方式 | 配置 | 说明 |
|:---:|:---:|:---:|:---:|:---|
| **Nginx** | 1.24.0-2ubuntu7.4 | APT安装 | VIP: ************<br/>配置目录: /etc/nginx/ | 高可用负载均衡器 |

**节点配置：**

| 服务器 | 端口  | 角色 | 状态 | 备注 |
|:---:|:---:|:---:|:---:|:---|
| ************ | 443 | 主节点 | Active | 主要流量处理 |
| ************ | 443 | 备节点 | Standby | 故障转移备份 |

## 4. 服务组件

### 4.1 数据存储层

#### 4.1.1 Redis分片集群

**架构特点：** 3主3从分片集群，提供高可用缓存服务

**集群概览：**

| 项目 | 值 | 项目 | 值 |
|:---:|:---:|:---:|:---:|
| **版本** | v6.2.19 | **部署方式** | Docker容器 |
| **集群模式** | 分片集群 | **节点数量** | 6个节点 (3主3从) |

**配置信息：**
```yaml
# Redis集群连接配置
spring:
  redis:
    cluster:
      nodes:
        - ************:7379  # 主节点1
        - ************:8379  # 主节点2
        - ************:9379  # 主节点3
        - ************:7379  # 从节点1
        - ************:8379  # 从节点2
        - ************:9379  # 从节点3
```

**节点分布：**

| 服务器 | 端口 | 角色 | 分片 | 状态 |
|:---:|:---:|:---:|:---:|:---:|
| ************ | 7379 | Master | Shard-1 | Running |
| ************ | 8379 | Master | Shard-2 | Running |
| ************ | 9379 | Master | Shard-3 | Running |
| ************ | 7379 | Slave | Shard-1 | Running |
| ************ | 8379 | Slave | Shard-2 | Running |
| ************ | 9379 | Slave | Shard-3 | Running |

#### 4.1.2 MinIO对象存储集群

**架构特点：** 分布式对象存储，S3兼容API，支持高可用和数据冗余

**存储概览：**

| 项目 | 值 | 项目 | 值 |
|:---:|:---:|:---:|:---:|
| **版本** | RELEASE.2024-01-11T07-46-16Z | **部署方式** | Docker容器 |
| **集群模式** | 分布式集群 | **API兼容** | Amazon S3 |

**节点配置：**

| 服务器 | API端口 | 控制台端口 | 角色 | 访问地址 |
|:---:|:---:|:---:|:---:|:---|
| ************ | 9090 | 19090 | 存储节点1 | http://************:19090 |
| ************ | 9090 | 19090 | 存储节点2 | http://************:19090 |

#### 4.1.3 MySQL数据库集群

**架构特点：** 双主架构，提供高可用的关系型数据库服务

**数据库概览：**

| 项目 | 值 | 项目 | 值 |
|:---:|:---:|:---:|:---:|
| **版本** | 8.0.40 | **部署方式** | Docker容器 |
| **架构模式** | 双主双从 | **复制方式** | 异步复制 |

**实例配置：**

| 服务器 | 端口 | 角色 | 状态 | 连接字符串 |
|:---:|:---:|:---:|:---:|:---|
| ************ | 4306 | 主节点1 | Active | mysql://************:4306 |
| ************ | 4307 | 主节点2 | Active | mysql://************:4307 |

### 4.2 应用服务层

#### 4.2.1 changeManagement-backend核心应用

**架构特点：** Spring Boot微服务架构，双节点部署，支持负载均衡

**应用概览：**

| 项目 | 值 | 项目 | 值 |
|:---:|:---:|:---:|:---:|
| **框架** | Spring Boot 3.x | **部署方式** | JAR包部署 |
| **架构模式** | 微服务架构 | **负载均衡** | Nginx反向代理 |

**服务实例：**

| 服务器 | 端口 | 访问地址 | 角色 |
|:---:|:---:|:---|:---:|
| ************ | 8080 | http://************:8080 | 主实例 |
| ************ | 8080 | http://************:8080 | 备实例 |

#### 4.2.2 SmartFlow工作流引擎

**架构特点：** 基于Camunda的工作流引擎，提供业务流程管理能力

**工作流概览：**

| 项目 | 值 | 项目 | 值 |
|:---:|:---:|:---:|:---:|
| **引擎** | Camunda BPM | **部署方式** | 独立部署 |
| **协议** | BPMN 2.0 | **API** | REST API |

**服务配置：**

| 服务器 | 端口 | 访问地址 | 角色 |
|:---:|:---:|:---|:---:|
| ************ | 38684 | http://************:38684 | 工作流引擎 |

## 5. 网络配置

### 5.1 端口分配表

| 服务 | 服务器 | 端口 | 协议 | 用途说明 |
|:---:|:---:|:---:|:---:|:---|
| **Nginx** | ************ | 8081 | HTTP | 负载均衡器主节点 |
| **Nginx** | ************ | 8081 | HTTP | 负载均衡器备节点 |
| **Backend** | ************ | 8080 | HTTP | 变更管理后端服务 |
| **Backend** | ************ | 8080 | HTTP | 变更管理后端服务 |
| **Redis** | ************ | 7379 | TCP | Redis主节点1 |
| **Redis** | ************ | 8379 | TCP | Redis主节点2 |
| **Redis** | ************ | 9379 | TCP | Redis主节点3 |
| **Redis** | ************ | 7379 | TCP | Redis从节点1 |
| **Redis** | ************ | 8379 | TCP | Redis从节点2 |
| **Redis** | ************ | 9379 | TCP | Redis从节点3 |
| **MinIO** | ************ | 9090 | HTTP | 对象存储API服务 |
| **MinIO** | ************ | 19090 | HTTP | 管理控制台 |
| **MinIO** | ************ | 9090 | HTTP | 对象存储API服务 |
| **MinIO** | ************ | 19090 | HTTP | 管理控制台 |
| **MySQL** | ************ | 4306 | TCP | 数据库主节点1 |
| **MySQL** | ************ | 4307 | TCP | 数据库主节点2 |
| **SmartFlow** | ************ | 38684 | HTTP | 工作流引擎服务 |

## 6. 运维指南

更详细内容参阅部署文档

### 6.1 监控检查

#### 服务健康检查命令

```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查Docker容器状态
docker ps -a

# 检查Redis集群状态（Docker方式）
docker exec -it redis-master-1 redis-cli -c cluster nodes
# 或者直接连接
redis-cli -c -h ************ -p 7379 cluster nodes

# 检查MySQL状态（Docker方式）
docker exec -ti mysql mysql -uapp -p'B24Lx4WEp5$KWz!5' -h************ -P4306
# 或者直接连接
mysqladmin -h ************ -P 4306 -u root -p status

# 检查MinIO容器状态
docker exec -it minio-1 mc admin info local
# 或者健康检查
curl http://************:9090/minio/health/live

# 检查应用状态
curl http://************:8080/actuator/health

# 检查SmartFlow状态
curl http://************:38684/engine-rest/engine
```

### 6.2 性能指标

| 组件 | 关键指标 | 正常范围 | 监控命令 |
|:---:|:---:|:---:|:---|
| **CPU使用率** | 系统负载 | < 80% | `top`, `htop` |
| **内存使用率** | 可用内存 | > 20% | `free -h` |
| **磁盘使用率** | 可用空间 | > 20% | `df -h` |
| **网络连接** | 连接数 | 正常范围 | `netstat -an` |

### 6.3 故障处理

#### 常见问题及解决方案

**1. 服务无法启动**
- 检查端口占用：`netstat -tulpn | grep :端口号`
- 检查配置文件语法
- 查看服务日志

**2. 数据库连接失败**
- 检查MySQL服务状态
- 验证网络连通性
- 确认用户权限

**3. Redis集群异常**
- 检查集群节点状态
- 验证网络分区
- 重新平衡分片

**4. Docker容器异常**
- 检查容器状态：`docker ps -a`
- 查看容器日志：`docker logs <container_name>`
- 重启容器：`docker restart <container_name>`
- 进入容器调试：`docker exec -it <container_name> /bin/bash`

### 6.4 备份策略

#### 数据备份

**MySQL数据库备份：**
```bash
# Docker方式备份
docker exec mysql-master-1 mysqldump -u root -p --all-databases > backup_$(date +%Y%m%d).sql

# 或者直接连接备份
mysqldump -h ************ -P 4306 -u backup_user -p --all-databases > backup_$(date +%Y%m%d).sql
```

**Redis数据备份：**
```bash
# Docker方式备份
docker exec redis-master-1 redis-cli BGSAVE

# Redis持久化配置
save 900 1
save 300 10
save 60 10000
```

**MinIO数据备份：**
- 配置跨区域复制
- 定期数据同步验证

### 6.5 安全配置

#### 网络安全

**防火墙规则：**
- 仅开放必要端口
- 限制管理端口访问
- 配置IP白名单

**SSL/TLS配置：**
- Nginx配置SSL证书
- 数据库连接加密
- API接口HTTPS访问

#### 访问控制

**用户权限管理：**
- 最小权限原则
- 定期权限审计
- 强密码策略

---

**文档版本：** v1.0
**更新时间：** 2025-01-31
**维护团队：** 运维组
