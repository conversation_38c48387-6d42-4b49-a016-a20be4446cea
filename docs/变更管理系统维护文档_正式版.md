# 变更管理系统维护文档

**文档版本**: v1.0  
**发布日期**: 2025年1月  
**编制单位**: 中电信数智科技有限公司  
**适用范围**: 变更管理系统运维团队  

---

## 目录

1. [文档概述](#1-文档概述)
2. [系统架构](#2-系统架构)
3. [运维要求](#3-运维要求)
4. [部署运维](#4-部署运维)
5. [监控告警](#5-监控告警)
6. [故障处理](#6-故障处理)
7. [备份恢复](#7-备份恢复)
8. [安全管理](#8-安全管理)
9. [附录](#9-附录)

---

## 1. 文档概述

### 1.1 文档目的

本文档旨在为变更管理系统的运维人员提供完整的系统维护指南，确保系统稳定、安全、高效运行。

### 1.2 系统简介

变更管理系统是一套基于Spring Boot + Vue.js的企业级变更流程管理平台，主要功能包括：

- **变更管理**: 变更申请、审核、执行、关闭全流程管理
- **封网管理**: 网络封锁申请、审批、执行流程
- **应用管理**: 应用信息维护和分类管理
- **配置管理**: 系统配置参数统一管理
- **监控告警**: 实时监控和告警通知
- **权限管理**: 基于RBAC的细粒度权限控制

### 1.3 文档适用范围

- 系统运维工程师
- 数据库管理员
- 网络管理员
- 安全管理员
- 技术支持人员

---

## 2. 系统架构

### 2.1 整体架构

系统采用前后端分离的微服务架构，具备高可用、高性能、高安全性特点。

```
┌─────────────────────────────────────────────────────────────┐
│                        负载均衡层                              │
│                    Nginx (VIP: ************)                │
└─────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
┌───────────────▼─────┐ ┌───────▼─────┐ ┌──────▼──────┐
│   前端服务 (Vue.js)   │ │  后端服务集群   │ │   工作流引擎   │
│   Port: 443         │ │  Port: 8080   │ │ SmartFlow   │
└─────────────────────┘ └───────────────┘ └─────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
┌───────────────▼─────┐ ┌───────▼─────┐ ┌──────▼──────┐
│   MySQL 双主集群     │ │  Redis 集群   │ │ MinIO 集群  │
│   Port: 3306       │ │  Port: 6379   │ │ Port: 9000  │
└─────────────────────┘ └───────────────┘ └─────────────┘
```

### 2.2 前端架构

```
cec-changemanagement-frontend/
├── src/
│   ├── App.vue                     # 根组件
│   ├── main.ts                     # 应用入口
│   ├── api/                        # API接口定义
│   ├── assets/                     # 静态资源
│   ├── components/                 # 公共组件
│   ├── enums/                      # 枚举定义
│   ├── hooks/                      # 自定义Hooks
│   ├── layouts/                    # 布局组件
│   ├── locales/                    # 国际化配置
│   ├── plugins/                    # 插件配置
│   ├── router/                     # 路由配置
│   ├── settings/                   # 系统配置
│   ├── stores/                     # 状态管理
│   ├── styles/                     # 样式文件
│   ├── utils/                      # 工具函数
│   └── views/                      # 页面组件
├── public/                         # 公共资源
├── build/                          # 构建配置
├── types/                          # 类型定义
├── package.json                    # 依赖配置
├── vite.config.ts                  # 构建配置
├── tailwind.config.js              # 样式配置
└── .env.*                          # 环境变量
```

**技术栈**:
- **框架**: Vue 3.x + TypeScript
- **构建工具**: Vite 4.x
- **UI组件**: Naive UI
- **样式**: Tailwind CSS + Less
- **状态管理**: Pinia
- **路由**: Vue Router 4.x
- **国际化**: Vue I18n

### 2.3 后端架构

```
changeManagement-backend/
├── changeManagement-admin/         # 启动模块
│   ├── src/main/
│   │   ├── java/com/cec/
│   │   │   ├── ChangeManagementApplication.java    # 启动类
│   │   │   ├── ChangeManagementServletInitializer.java # Web配置
│   │   │   └── web/controller/     # 通用控制器
│   │   └── resources/
│   │       ├── application*.yml    # 配置文件
│   │       ├── banner.txt          # 启动横幅
│   │       ├── logback-plus.xml    # 日志配置
│   │       └── i18n/               # 国际化资源
├── changeManagement-common/        # 通用模块
│   ├── changeManagement-common-bom/        # 依赖管理
│   ├── changeManagement-common-core/       # 核心工具
│   ├── changeManagement-common-web/        # Web配置
│   ├── changeManagement-common-mybatis/    # 数据库配置
│   ├── changeManagement-common-redis/      # 缓存配置
│   ├── changeManagement-common-satoken/    # 权限配置
│   ├── changeManagement-common-security/   # 安全配置
│   ├── changeManagement-common-log/        # 日志配置
│   ├── changeManagement-common-excel/      # Excel处理
│   ├── changeManagement-common-oss/        # 对象存储
│   ├── changeManagement-common-mail/       # 邮件服务
│   ├── changeManagement-common-doc/        # API文档
│   ├── changeManagement-common-json/       # JSON处理
│   ├── changeManagement-common-encrypt/    # 数据加密
│   ├── changeManagement-common-sensitive/  # 敏感数据
│   ├── changeManagement-common-translation/ # 数据翻译
│   ├── changeManagement-common-tenant/     # 多租户
│   ├── changeManagement-common-idempotent/ # 幂等性
│   ├── changeManagement-common-ratelimiter/ # 限流
│   └── changeManagement-common-social/     # 第三方登录
├── changeManagement-modules/       # 业务模块
│   ├── changeManagement-system/    # 系统管理
│   └── changeManagement-business/  # 业务逻辑
├── script/                         # 部署脚本
│   ├── bin/                        # 启停脚本
│   └── sql/                        # 数据库脚本
└── pom.xml                         # Maven配置
```

**技术栈**:
- **框架**: Spring Boot 3.4.4 + Java 17
- **Web服务器**: Undertow (高性能)
- **数据库**: MySQL 8.0 + MyBatis Plus 3.5.11
- **缓存**: Redis 7.x + Redisson 3.45.1
- **权限认证**: Sa-Token 1.40.0 (JWT)
- **数据源**: Dynamic DataSource 4.3.1
- **对象存储**: AWS S3 SDK 2.28.22 (兼容MinIO)
- **任务调度**: SnailJob 1.4.0
- **监控**: Spring Boot Admin 3.4.5
- **API文档**: SpringDoc OpenAPI 2.8.5
- **Excel处理**: EasyExcel 4.0.3
- **工具库**: Hutool 5.8.35
- **对象映射**: MapStruct Plus 1.4.6
- **分布式锁**: Lock4j 2.2.7
- **加密**: BouncyCastle 1.76
- **构建工具**: Maven 3.x

### 2.4 数据库设计

系统采用MySQL 8.0作为主数据库，支持双主架构，详细表结构请参考数据字典文档。

**核心表结构**:
- `cm_change_*`: 变更管理相关表
- `cm_network_freeze_*`: 封网管理相关表
- `cm_application_manage`: 应用管理表
- `sys_*`: 系统管理相关表

---

## 3. 运维要求

### 3.1 运维目标

- **可用性**: 系统可用率 ≥ 99.9%
- **性能**: 响应时间 ≤ 5秒，并发用户 ≥ 50
- **安全性**: 数据安全、访问控制、审计日志
- **稳定性**: 7×24小时稳定运行
- **可扩展性**: 支持水平扩展和垂直扩展

### 3.2 硬件要求

#### 3.2.1 生产环境配置

| 服务类型 | CPU | 内存 | 磁盘 |
|---------|-----|------|------|
| **应用服务器** | 16核+ | 32GB+ | 500GB SSD |
| **数据库服务器** | 16核+ | 64GB+ | 1TB SSD |
| **缓存服务器** | 8核+ | 32GB+ | 200GB SSD |
| **对象存储服务器** | 16核+ | 64GB+ | 2TB SSD |

#### 3.2.2 测试环境配置

| 服务类型 | CPU | 内存 | 磁盘 |
|---------|-----|------|------|
| **应用服务器** | 8核 | 16GB | 200GB SSD |
| **数据库服务器** | 8核 | 32GB | 500GB SSD |
| **缓存服务器** | 4核 | 16GB | 100GB SSD |
| **对象存储服务器** | 8核 | 32GB | 1TB SSD |

#### 3.2.3 开发环境配置

| 服务类型 | CPU | 内存 | 磁盘 |
|---------|-----|------|------|
| **开发服务器** | 4核 | 8GB | 200GB |

**注意事项**:
- 所有服务器CPU不低于4核处理器
- 内存配置需考虑并发用户数和数据增长
- 磁盘空间预留30%以上空闲空间

### 3.3 软件要求

#### 3.3.1 操作系统

- **推荐**: Ubuntu 20.04 LTS / Ubuntu 22.04 LTS
- **备选**: CentOS 7/8, RHEL 7/8
- **要求**: 64位系统，内核版本 ≥ 3.10

#### 3.3.2 基础软件

| 软件类型 | 版本要求          | 说明 |
|---------|---------------|------|
| **Java** | OpenJDK 17+   | 推荐Liberica JDK |
| **MySQL** | 8.0.35+       | 字符集UTF8MB4，InnoDB引擎 |
| **Redis** | 6.0/7.0+      | 支持集群模式 |
| **Nginx** | 1.18+         | 支持upstream、ssl、gzip模块 |
| **MinIO** | RELEASE.2023+ | 兼容S3 API |
| **Maven** | 3.6+          | 构建工具 |
| **Node.js** | 18.x          | 前端构建 |

#### 3.3.3 网络端口

| 端口 | 服务 | 说明 |
|------|------|------|
| 80 | HTTP | Web服务 |
| 443 | HTTPS | 安全Web服务 |
| 8080 | Spring Boot | 应用服务 |
| 3306 | MySQL | 数据库服务 |
| 6379 | Redis | 缓存服务 |
| 9000 | MinIO | 对象存储 |
| 38684 | SmartFlow | 工作流引擎 |

### 3.4 安全要求

#### 3.4.1 网络安全

- 配置防火墙，仅开放必要端口
- 使用VPN或专线访问内网服务
- 定期更新SSL证书
- 启用HTTPS强制跳转

#### 3.4.2 系统安全

- 定期更新系统补丁
- 配置日志审计

#### 3.4.3 数据安全

- 数据库连接加密
- 敏感数据脱敏存储
- 定期备份验证
- 访问权限最小化

---

## 4. 部署运维

### 4.1 部署前准备

#### 4.1.1 环境检查清单

- 服务器硬件配置符合要求
- 操作系统版本和补丁更新
- 基础软件安装和配置
- 网络连通性测试
- 防火墙规则配置
- DNS解析配置
- 时间同步配置
- 用户权限配置

#### 4.1.2 软件安装清单

- Java 17运行环境
- MySQL 8.0数据库
- Redis 6.x缓存
- Nginx Web服务器
- MinIO对象存储

### 4.2 部署步骤

**参见部署文档**

## 5. 故障处理

### 5.1 常见故障

#### 5.1.1 应用无法启动

**症状**: 应用启动失败，端口8080无响应

**排查步骤**:
1. 检查Java进程: `ps aux | grep java`
2. 查看启动日志: `tail -f logs/sys-console.log`
3. 检查端口占用: `netstat -tlnp | grep 8080`
4. 检查配置文件: `vim application-prod.yml`

**解决方案**:
```bash
# 杀死占用端口的进程
sudo kill -9 $(lsof -t -i:8080)

# 检查数据库连接
mysql -u app -p -h localhost

# 重启应用
./change-management-admin.sh restart
```

#### 5.1.2 数据库连接异常

**症状**: 数据库连接超时或拒绝连接

**排查步骤**:
1. 检查MySQL服务: `systemctl status mysql`
2. 检查连接数: `SHOW PROCESSLIST;`
3. 测试连接: `mysql -u cmuser -p -h localhost`

**解决方案**:
```sql
-- 增加最大连接数
SET GLOBAL max_connections = 1000;

-- 重启MySQL
sudo systemctl restart mysql
```

### 5.2 性能优化

#### 5.2.1 JVM调优

```bash
# 生产环境JVM参数
JAVA_OPTS="-server -Xms4g -Xmx8g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

#### 5.2.2 数据库优化

```sql
-- 慢查询优化
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 连接池优化
SET GLOBAL max_connections = 1000;
```

---

## 6. 备份恢复

### 6.1 备份策略

#### 6.1.1 数据库备份

```bash
#!/bin/bash
# 全量备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"

mysqldump -u backup_user -p${BACKUP_PASSWORD} \
  --single-transaction \
  --routines \
  --triggers \
  changemanagement > ${BACKUP_DIR}/full_backup_${DATE}.sql

gzip ${BACKUP_DIR}/full_backup_${DATE}.sql
find ${BACKUP_DIR} -name "full_backup_*.sql.gz" -mtime +7 -delete
```

#### 6.1.2 配置备份

```bash
#!/bin/bash
# 配置文件备份
DATE=$(date +%Y%m%d)
tar -czf /backup/config/app_config_${DATE}.tar.gz \
  /opt/changemanagement/*.yml \
  /etc/nginx/sites-available/
```

### 6.2 恢复流程

```bash
# 1. 停止应用
./change-management-admin.sh stop

# 2. 恢复数据库
mysql -u root -p changemanagement < /backup/mysql/full_backup_latest.sql

# 3. 恢复配置
tar -xzf /backup/config/app_config_latest.tar.gz -C /

# 4. 启动应用
./change-management-admin.sh start
```

---

## 7. 安全管理

### 7.1 访问控制

#### 7.1.1 防火墙配置

```bash
# 基本防火墙规则
sudo ufw enable
sudo ufw default deny incoming
sudo ufw allow from ***********/24 to any port 22
sudo ufw allow 80,443
sudo ufw allow from 10.0.0.0/8 to any port 8080,3306,6379
```

#### 7.1.2 数据库安全

```sql
-- 创建只读用户
CREATE USER 'readonly'@'%' IDENTIFIED BY 'readonly_password';
GRANT SELECT ON changemanagement.* TO 'readonly'@'%';

-- 限制root远程访问
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1');
FLUSH PRIVILEGES;
```

### 7.2 数据加密

- HTTPS/TLS 1.3传输加密
- 数据库连接SSL加密
- 敏感字段AES加密
- 密码BCrypt哈希

### 7.3 安全审计

- 启用MySQL审计日志
- 应用操作日志记录
- 定期安全扫描
- 访问日志分析

---

## 8. 附录

### 8.1 Nginx配置

```nginx
server {

    listen       443 ssl;
    server_name  changemgtportal-uat.citictel-cpc.com;
    
    # 配置SSL证书
    ssl_certificate     ssl/new.crt;
    ssl_certificate_key ssl/changemgtportal-uat.citictel-cpc.com.key;
    
    # listen       443;
    # server_name  ************;
    
    add_header 'Access-Control-Allow-Origin' *;
    add_header 'Access-Control-Allow-Credentials' 'true';
    add_header 'Access-Control-Allow-Methods' *;
    add_header 'Access-Control-Allow-Headers' *;
    
    location / {              
        root /home/<USER>/changeManagement-ui/dist/;  
            index index.html;
            try_files $uri $uri/ /index.html;  
        #autoindex on;
    }

	location /api {
 		proxy_pass http://127.0.0.1:8080;
	    proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header Host $host;
		proxy_set_header X-Forwarded-Proto $scheme;
	}	

    location /minio/ {
        proxy_pass http://************:9090/;
    }
}

```

### 8.2 常用命令

```bash
# 应用管理
./change-management-admin.sh start|stop|restart|status

# 日志查看
tail -f logs/sys-console.log
grep "ERROR" logs/sys-*.log

# 性能监控
top -p $(pgrep -f changeManagement)
jstat -gc $(pgrep -f changeManagement)

# 数据库管理
mysql -u root -p
SHOW PROCESSLIST;
EXPLAIN SELECT * FROM table_name;
```

