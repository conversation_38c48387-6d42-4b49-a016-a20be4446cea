# 变更管理系统架构图

## 整体架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        前端层 (Vue3 + TS)                        │
├─────────────────────────────────────────────────────────────────┤
│                     Nginx 负载均衡 (80/443)                      │
├─────────────────────────────────────────────────────────────────┤
│                        应用服务层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  ruoyi-server1  │  │  ruoyi-server2  │  │      ...        │  │
│  │    (8080)       │  │    (8081)       │  │                 │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        监控管理层                                │
│  ┌─────────────────┐  ┌─────────────────┐                      │
│  │ monitor-admin   │  │ snailjob-server │                      │
│  │    (9090)       │  │   (8800/17888)  │                      │
│  └─────────────────┘  └─────────────────┘                      │
├─────────────────────────────────────────────────────────────────┤
│                      第三方服务层                                │
│  ┌─────────────────┐                                            │
│  │   SmartFlow     │  (审批流引擎)                              │
│  │   审批流服务      │                                            │
│  └─────────────────┘                                            │
├─────────────────────────────────────────────────────────────────┤
│                        基础设施层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   MySQL 双主     │  │  Redis 集群     │  │  MinIO 集群     │  │
│  │    (3306)       │  │    (6379)       │  │  (9000/9001)    │  │
│  │                 │  │                 │  │                 │  │
│  │  ┌───┐   ┌───┐  │  │ ┌───┐ ┌───┐ ┌───┐│  │ ┌───┐ ┌───┐ ┌───┐│  │
│  │  │M-1│<->│M-2│  │  │ │R-1│ │R-2│ │R-3││  │ │S-1│ │S-2│ │S-3││  │
│  │  └───┘   └───┘  │  │ └───┘ └───┘ └───┘│  │ └───┘ └───┘ └───┘│  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## 核心模块架构

```
changeManagement-backend
├── changeManagement-admin          # 启动模块
├── changeManagement-common         # 通用模块
├── changeManagement-modules        # 业务模块
│   ├── changeManagement-system     # 系统管理
│   └── changeManagement-business   # 业务逻辑
│       ├── 变更管理 (cm_change_*)
│       ├── 封网管理 (cm_network_freeze_*)
│       └── 应用管理 (cm_application_manage)
```

## 数据流向

1. **用户请求** → Nginx → 应用服务集群
2. **业务数据** → MySQL双主 (主从同步)
3. **缓存数据** → Redis集群 (分片存储)
4. **文件存储** → MinIO集群 (分布式存储)
5. **审批流程** → SmartFlow (第三方审批引擎)
6. **系统监控** → Monitor Admin + SnailJob

## 关键特性

- **高可用**: 应用服务集群 + MySQL双主 + Redis集群
- **负载均衡**: Nginx upstream 配置
- **文件存储**: MinIO分布式对象存储
- **流程引擎**: 集成SmartFlow审批流
- **任务调度**: SnailJob分布式任务
- **监控告警**: Spring Boot Admin监控