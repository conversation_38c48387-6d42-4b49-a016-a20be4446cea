package com.cec.test;

import cn.hutool.extra.mail.MailAccount;
import com.cec.business.domain.NetworkFreezeInfo;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.mapper.NetworkFreezeInfoMapper;
import com.cec.business.service.EmailService;
import com.cec.common.core.constant.GlobalConstants;
import com.cec.common.redis.utils.RedisUtils;
import com.cec.common.mail.utils.MailUtils;
import com.cec.system.domain.vo.SysOssVo;
import com.cec.system.service.ISysOssService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮件服务测试类
 */
@SpringBootTest
public class EmailServiceTest {

    private static final Logger log = LoggerFactory.getLogger(EmailServiceTest.class);

    @Autowired
    private EmailService emailService;
    @Autowired
    private NetworkFreezeInfoMapper networkFreezeInfoMapper;
    @Autowired
    private ISysOssService ossService;

    /**
     * 测试前检查邮件账户配置
     */
    @BeforeEach
    public void checkMailConfig() {
        MailAccount account = MailUtils.getMailAccount();
        log.info("当前邮件配置信息：");
        log.info("发件人：{}", account.getFrom());
        log.info("SMTP服务器：{}", account.getHost());
        log.info("SMTP端口：{}", account.getPort());
        log.info("邮箱用户：{}", account.getUser());
        log.info("是否启用身份验证：{}", account.isAuth());
        log.info("是否启用SSL：{}", account.isSslEnable());
    }

    /**
     * 测试直接使用MailUtils发送邮件
     */
    @Test
    @DisplayName("测试直接使用MailUtils发送邮件")
    public void testMailUtilsDirectly() {
        try {
            // 设置收件人、主题和内容
            String to = "<EMAIL>";
            String subject = "测试邮件********";
            String content = "这是一封测试邮件<br><br>请勿回复这是一封测试邮件<br><br>请勿回复这是一封测试邮件<br><br>请勿回复这是一封测试邮件<br><br>请勿回复";

            // 直接使用MailUtils发送
            String messageId = MailUtils.sendHtml(to, subject, content);
            log.info("邮件发送结果：{}", (messageId != null ? "成功，消息ID: " + messageId : "失败"));
        } catch (Exception e) {
            log.error("邮件发送失败：{}", e.getMessage(), e);
        }
    }

    @Test
    @DisplayName("测试发送邮件到QQ邮箱")
    public void testSendToQQEmail() {
        // 创建占位符替换映射
        Map<String, String> placeholderMap = new HashMap<>();
        placeholderMap.put("Emergency/Scheduled", "Scheduled");
        placeholderMap.put("SystemName", "CEC Change Management System");
        placeholderMap.put("StartDate", "2025-06-03 16:00");
        placeholderMap.put("EndDate", "2025-06-03 18:00");
        placeholderMap.put("ChangeNumber", "CM********001");
        placeholderMap.put("TeamName", "CEC IT Team");

        // 仅测试替换占位符
        Map<String, String> result = emailService.processEmailTemplate(
            ChangeStageEnum.IMPLEMENTING.name(), placeholderMap);

        log.info("邮件将发送到: <EMAIL>");
        log.info("邮件主题: {}", result.get("subject"));
        log.info("邮件内容:\n{}", result.get("content"));

        // 验证所有占位符都已被替换
        validatePlaceholders(result.get("subject"), result.get("content"), placeholderMap);

        // 发送邮件到指定QQ邮箱
        try {
            String messageId = emailService.sendTemplateEmail(
                ChangeStageEnum.IMPLEMENTING.name(), placeholderMap, "<EMAIL>", null, null);
            log.info("邮件发送结果：{}", (messageId != null ? "成功，消息ID: " + messageId : "失败"));
        } catch (Exception e) {
            log.error("邮件发送失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 验证所有占位符都已被替换
     *
     * @param subject        替换后的邮件主题
     * @param content        替换后的邮件内容
     * @param placeholderMap 占位符替换映射
     */
    private void validatePlaceholders(String subject, String content, Map<String, String> placeholderMap) {
        for (String key : placeholderMap.keySet()) {
            String placeholder = "{$" + key + "}";
            assert !subject.contains(placeholder) : "邮件主题中存在未替换的占位符: " + placeholder;
            assert !content.contains(placeholder) : "邮件内容中存在未替换的占位符: " + placeholder;
        }
        log.info("所有占位符替换成功！");
    }

    @Test
    @DisplayName("测试封网邮件")
    public void testSendNetworkFreeze() {
        // 创建占位符替换映射
        NetworkFreezeInfo info = networkFreezeInfoMapper.selectById(1930104623572725761L);
        String messageId = MailUtils.sendHtml("<EMAIL>", "封网通知", info.getEmailContent());
    }

    /**
     * 测试带附件的邮件发送
     */
    @Test
    @DisplayName("测试带附件的邮件发送")
    public void testSendEmailWithAttachment() {
        try {
            // 设置收件人、主题和内容
            String to = "<EMAIL>";
            String subject = "测试带附件邮件";
            String content = "这是一封带附件的测试邮件<br><br>请查看附件内容<br><br>请勿回复";

            // 从Minio获取附件
            List<Long> ossIds = List.of(1932371368947503106L, 1932018301534007297L);
            List<SysOssVo> ossList = ossService.listByIds(ossIds);

            log.info("找到附件数量: {}", ossList.size());

            // 准备临时文件作为附件
            List<java.io.File> tempFiles = new ArrayList<>();

            int fileIndex = 1;
            for (SysOssVo oss : ossList) {
                log.info("处理附件: {}, ossId: {}", oss.getOriginalName(), oss.getOssId());

                // 获取附件内容
                MockHttpServletResponse response = new MockHttpServletResponse();
                ossService.download(oss.getOssId(), response);

                // 提取附件内容并创建临时文件
                byte[] fileContent = response.getContentAsByteArray();
                if (fileContent.length > 0) {
                    try {
                        // 创建符合要求的临时文件名 - 使用固定前缀"att"加上文件索引
                        String tmpPrefix = "att" + fileIndex;

                        // 创建临时文件
                        java.io.File tempFile = java.io.File.createTempFile(tmpPrefix, ".tmp");
                        tempFile.deleteOnExit(); // 确保JVM退出时删除临时文件

                        // 写入内容
                        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(tempFile)) {
                            fos.write(fileContent);
                        }

                        // 使用源文件名创建最终文件
                        java.io.File finalFile = new java.io.File(tempFile.getParent(), oss.getOriginalName());
                        if (finalFile.exists()) {
                            finalFile.delete(); // 如果文件已存在，先删除
                        }
                        tempFile.renameTo(finalFile);

                        tempFiles.add(finalFile);
                        log.info("成功创建附件文件: {}, 大小: {} 字节", finalFile.getAbsolutePath(), fileContent.length);
                        fileIndex++;
                    } catch (Exception e) {
                        log.error("创建临时文件失败: {}", e.getMessage(), e);
                    }
                } else {
                    log.error("未能获取附件内容: {}", oss.getOriginalName());
                }
            }

            // 发送带附件的邮件
            String messageId = null;
            if (!tempFiles.isEmpty()) {
                // 使用MailUtils发送带附件的邮件
                messageId = MailUtils.sendHtml(to, subject, content, tempFiles.toArray(new java.io.File[0]));
            } else {
                log.info("没有有效的附件，发送普通邮件");
                messageId = MailUtils.sendHtml(to, subject, content);
            }

            log.info("带附件邮件发送结果：{}", (messageId != null ? "成功，消息ID: " + messageId : "失败"));

            // 输出附件信息
            log.info("邮件附件数量: {}", tempFiles.size());
            for (java.io.File file : tempFiles) {
                log.info("- {}", file.getName());
            }

            // 清理临时文件（虽然有deleteOnExit，但显式清理更好）
            for (java.io.File file : tempFiles) {
                try {
                    file.delete();
                } catch (Exception e) {
                    // 忽略清理错误
                }
            }
        } catch (Exception e) {
            log.error("带附件邮件发送失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 测试使用OSS IDs发送带附件的邮件
     */
    @Test
    @DisplayName("测试使用OSS IDs发送带附件的邮件")
    public void testSendEmailWithOssAttachments() {
        try {
            // 设置收件人、主题和内容
            String to = "<EMAIL>";
            String cc = ""; // 可以设置为null或空字符串
            String subject = "测试OSS附件邮件";
            String content = "这是一封通过sendEmailWithOssAttachments方法发送的测试邮件<br><br>附件从OSS直接获取<br><br>请勿回复";
            boolean isHtml = true;

            // 准备OSS IDs（使用真实存在的OSS ID）
            List<Long> ossIds = List.of(1932371368947503106L, 1932018301534007297L);

            log.info("开始测试sendEmailWithOssAttachments方法");
            log.info("收件人: {}", to);
            log.info("抄送人: {}", cc);
            log.info("主题: {}", subject);
            log.info("OSS附件ID: {}", ossIds);

            // 先获取OSS对象信息，确认它们存在
            List<SysOssVo> ossList = ossService.listByIds(ossIds);
            for (SysOssVo oss : ossList) {
                log.info("OSS文件信息: ID={}, 原始文件名={}, 服务={}",
                    oss.getOssId(), oss.getOriginalName(), oss.getService());
            }

            // 调用被测试的方法
            String messageId = emailService.sendEmailWithOssAttachments(to, cc, subject, content, isHtml, ossIds);

            // 验证结果
            log.info("邮件发送结果：{}", (messageId != null ? "成功，消息ID: " + messageId : "失败"));

            // 断言邮件发送成功
            assert messageId != null && !messageId.isEmpty() : "邮件发送失败，未返回消息ID";

        } catch (Exception e) {
            log.error("使用OSS附件发送邮件失败：{}", e.getMessage(), e);

            // 输出更详细的错误信息
            Throwable cause = e.getCause();
            while (cause != null) {
                log.error("错误原因: {}", cause.getMessage());
                cause = cause.getCause();
            }

            // 测试失败
            assert false : "测试过程中发生异常: " + e.getMessage();
        }
    }

    /**
     * 测试IMPLEMENTING阶段邮件去重功能
     */
    @Test
    @DisplayName("测试IMPLEMENTING阶段邮件去重")
    public void testImplementingEmailDeduplication() {
        try {
            log.info("开始测试IMPLEMENTING阶段邮件去重功能");

            Long testChangeInfoId = 1L; // 使用测试用的变更ID

            // 清理可能存在的Redis缓存
            String deduplicationKey = GlobalConstants.EMAIL_DEDUPLICATION_KEY + "implementing:" + testChangeInfoId;
            RedisUtils.deleteObject(deduplicationKey);

            // 第一次调用 - 应该正常发送邮件
            log.info("第一次调用sendEmailByChangeInfoId，应该正常发送邮件");
            boolean firstCallSuccess = false;
            try {
                emailService.sendEmailByChangeInfoId(ChangeStageEnum.IMPLEMENTING, testChangeInfoId);
                log.info("第一次调用完成，邮件发送成功");
                firstCallSuccess = true;
            } catch (Exception e) {
                log.warn("第一次调用出现异常（可能是因为测试数据不存在）: {}", e.getMessage());
            }

            // 检查Redis中是否设置了去重标记
            Object cacheValue = RedisUtils.getCacheObject(deduplicationKey);
            if (firstCallSuccess) {
                if (cacheValue != null) {
                    log.info("✓ 邮件发送成功，Redis去重标记设置成功: {}", cacheValue);
                } else {
                    log.error("✗ 邮件发送成功但Redis去重标记未设置，这是一个bug");
                }
            } else {
                if (cacheValue == null) {
                    log.info("✓ 邮件发送失败，Redis去重标记未设置，这是正确的行为");
                } else {
                    log.error("✗ 邮件发送失败但Redis去重标记被设置了，这是一个bug");
                }
            }

            // 第二次调用 - 只有在第一次成功的情况下才应该被去重拦截
            if (firstCallSuccess && cacheValue != null) {
                log.info("第二次调用sendEmailByChangeInfoId，应该被去重拦截");
                try {
                    emailService.sendEmailByChangeInfoId(ChangeStageEnum.IMPLEMENTING, testChangeInfoId);
                    log.info("第二次调用完成，应该被去重拦截而不会真正发送邮件");
                } catch (Exception e) {
                    log.warn("第二次调用出现异常: {}", e.getMessage());
                }
            } else {
                log.info("跳过第二次调用测试，因为第一次调用未成功或去重标记未设置");
            }

            // 清理测试数据
            RedisUtils.deleteObject(deduplicationKey);
            log.info("测试完成，已清理Redis缓存");

        } catch (Exception e) {
            log.error("测试IMPLEMENTING阶段邮件去重功能时发生异常", e);

            // 打印详细的异常信息
            Throwable cause = e.getCause();
            while (cause != null) {
                log.error("错误原因: {}", cause.getMessage());
                cause = cause.getCause();
            }

            // 测试失败
            assert false : "测试过程中发生异常: " + e.getMessage();
        }
    }

    /**
     * 测试OVER_7_DAYS阶段邮件去重功能
     */
    @Test
    @DisplayName("测试OVER_7_DAYS阶段邮件去重")
    public void testOver7DaysEmailDeduplication() {
        try {
            log.info("开始测试OVER_7_DAYS阶段邮件去重功能");

            Long testChangeInfoId = 1L; // 使用测试用的变更ID

            // 清理可能存在的Redis缓存
            String deduplicationKey = GlobalConstants.EMAIL_DEDUPLICATION_KEY + "over7days:" + testChangeInfoId;
            RedisUtils.deleteObject(deduplicationKey);

            // 第一次调用 - 应该正常发送邮件
            log.info("第一次调用sendEmailByChangeInfoId，应该正常发送邮件");
            boolean firstCallSuccess = false;
            try {
                emailService.sendEmailByChangeInfoId(ChangeStageEnum.OVER_7_DAYS, testChangeInfoId);
                log.info("第一次调用完成，邮件发送成功");
                firstCallSuccess = true;
            } catch (Exception e) {
                log.warn("第一次调用出现异常（可能是因为测试数据不存在）: {}", e.getMessage());
            }

            // 检查Redis中是否设置了去重标记
            Object cacheValue = RedisUtils.getCacheObject(deduplicationKey);
            if (firstCallSuccess) {
                if (cacheValue != null) {
                    log.info("✓ 邮件发送成功，Redis去重标记设置成功: {}", cacheValue);
                } else {
                    log.error("✗ 邮件发送成功但Redis去重标记未设置，这是一个bug");
                }
            } else {
                if (cacheValue == null) {
                    log.info("✓ 邮件发送失败，Redis去重标记未设置，这是正确的行为");
                } else {
                    log.error("✗ 邮件发送失败但Redis去重标记被设置了，这是一个bug");
                }
            }

            // 第二次调用 - 只有在第一次成功的情况下才应该被去重拦截
            if (firstCallSuccess && cacheValue != null) {
                log.info("第二次调用sendEmailByChangeInfoId，应该被去重拦截");
                try {
                    emailService.sendEmailByChangeInfoId(ChangeStageEnum.OVER_7_DAYS, testChangeInfoId);
                    log.info("第二次调用完成，应该被去重拦截而不会真正发送邮件");
                } catch (Exception e) {
                    log.warn("第二次调用出现异常: {}", e.getMessage());
                }
            } else {
                log.info("跳过第二次调用测试，因为第一次调用未成功或去重标记未设置");
            }

            // 检查Redis key的TTL（应该是7天）
            if (cacheValue != null) {
                Long ttl = RedisUtils.getTimeToLive(deduplicationKey);
                if (ttl != null && ttl > 0) {
                    long days = ttl / (24 * 60 * 60);
                    log.info("✓ Redis去重标记TTL设置正确，剩余时间: {} 秒 (约 {} 天)", ttl, days);
                    if (days <= 7 && days >= 6) { // 允许一些时间误差
                        log.info("✓ TTL设置符合预期（7天）");
                    } else {
                        log.warn("⚠ TTL设置可能不符合预期，期望7天，实际约{}天", days);
                    }
                } else {
                    log.warn("⚠ 无法获取Redis key的TTL信息");
                }
            }

            // 清理测试数据
            RedisUtils.deleteObject(deduplicationKey);
            log.info("测试完成，清理Redis缓存");

        } catch (Exception e) {
            log.error("测试OVER_7_DAYS阶段邮件去重功能时发生异常", e);

            // 打印详细的异常信息
            Throwable cause = e.getCause();
            while (cause != null) {
                log.error("错误原因: {}", cause.getMessage());
                cause = cause.getCause();
            }

            // 测试失败
            assert false : "测试过程中发生异常: " + e.getMessage();
        }
    }
}
