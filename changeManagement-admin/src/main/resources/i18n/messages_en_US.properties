#错误消息
not.null=* Required fill in
user.jcaptcha.error=Captcha error
user.jcaptcha.expire=Captcha invalid
user.not.exists=Sorry, your account: {0} does not exist
user.password.not.match=User does not exist/Password error
user.password.retry.limit.count=Password input error {0} times
user.password.retry.limit.exceed=Password input error {0} times, account locked for {1} minutes
user.password.delete=Sorry, your account：{0} has been deleted
user.blocked=Sorry, your account: {0} has been disabled. Please contact the administrator
role.blocked=Role disabled，please contact administrators
user.logout.success=Exit successful
length.not.valid=The length must be between {min} and {max} characters
user.username.not.blank=Username cannot be blank
user.username.not.valid=* 2 to 20 chinese characters, letters, numbers or underscores, and must start with a non number
user.username.length.valid=Account length must be between {min} and {max} characters
user.password.not.blank=Password cannot be empty
user.password.length.valid=Password length must be between {min} and {max} characters
user.password.not.valid=* 5-50 characters
user.password.rule.invalid=Password does not meet requirements. Please enter a password with 8-20 characters including uppercase, lowercase, numbers and special symbols.
user.email.not.valid=Mailbox format error
user.email.not.blank=Mailbox cannot be blank
user.phonenumber.not.blank=Phone number cannot be blank
user.mobile.phone.number.not.valid=Phone number format error
user.login.success=Login successful
user.register.success=Register successful
user.register.save.error=Failed to save user {0}, The registered account already exists
user.register.error=Register failed, please contact system administrator
user.notfound=Please login again
user.forcelogout=The administrator is forced to exit，please login again
user.unknown.error=Unknown error, please login again
auth.grant.type.error=Auth grant type error
auth.grant.type.blocked=Auth grant type disabled
auth.grant.type.not.blank=Auth grant type cannot be blank
auth.clientid.not.blank=Auth clientid cannot be blank
##文件上传消息
upload.exceed.maxSize=The uploaded file size exceeds the limit file size！<br/>the maximum allowed file size is：{0}MB！
upload.filename.exceed.length=The maximum length of uploaded file name is {0} characters
##权限
no.permission=You do not have permission to the data，please contact your administrator to add permissions [{0}]
no.create.permission=You do not have permission to create data，please contact your administrator to add permissions [{0}]
no.update.permission=You do not have permission to modify data，please contact your administrator to add permissions [{0}]
no.delete.permission=You do not have permission to delete data，please contact your administrator to add permissions [{0}]
no.export.permission=You do not have permission to export data，please contact your administrator to add permissions [{0}]
no.view.permission=You do not have permission to view data，please contact your administrator to add permissions [{0}]
repeat.submit.message=Repeat submit is not allowed, please try again later
rate.limiter.message=Visit too frequently, please try again later
sms.code.not.blank=Sms code cannot be blank
sms.code.retry.limit.count=Sms code input error {0} times
sms.code.retry.limit.exceed=Sms code input error {0} times, account locked for {1} minutes
email.code.not.blank=Email code cannot be blank
email.code.retry.limit.count=Email code input error {0} times
email.code.retry.limit.exceed=Email code input error {0} times, account locked for {1} minutes
xcx.code.not.blank=Mini program [code] cannot be blank
social.source.not.blank=Social login platform [source] cannot be blank
social.code.not.blank=Social login platform [code] cannot be blank
social.state.not.blank=Social login platform [state] cannot be blank
##租户
tenant.number.not.blank=Tenant number cannot be blank
tenant.not.exists=Sorry, your tenant does not exist. Please contact the administrator
tenant.blocked=Sorry, your tenant is disabled. Please contact the administrator
tenant.expired=Sorry, your tenant has expired. Please contact the administrator.

id.not.null=ID cannot be empty

##Configuration Information
conf.type.not.null=Type cannot be empty
conf.name.not.null=Name cannot be empty
conf.name.size=Name length cannot exceed 100 characters
conf.status.not.null=Status cannot be empty
conf.description.size=Description length cannot exceed 200 characters

##Application Management
app.name.not.blank=Application name cannot be empty
app.name.size=Application name length cannot exceed 100 characters
app.business.owner.id.not.blank=BusinessOwnerId cannot be empty
app.business.owner.not.blank=BusinessOwner cannot be empty
app.team.leader.id.not.blank=TeamLeaderId cannot be empty
app.team.leader.not.blank=TeamLeader cannot be empty
app.team.id.not.null=Team_ID cannot be empty
app.team.name.not.blank=Team_Name cannot be empty
app.category.id.not.null=Category ID cannot be empty
app.category.not.blank=Category cannot be empty

##Change Information
entity.id=ID
entity.title=Title
entity.requesterId=Requester ID
entity.requesterName=Requester
entity.teamId=Team ID
entity.teamName=Change Group
entity.isUrgentChange=Emergency Change
entity.changeType=Change Type
entity.isNetworkFreezeChange=Network Freeze
entity.categoryIds=Category IDs
entity.categoryNameList=Category
entity.applicationId=Application/System ID
entity.applicationName=Application/System
entity.locationId=SiteId
entity.locationName=Site
entity.src=Associated SCR/MSPRequest/Other URL
entity.priority=Priority
entity.planTimeStart=Scheduled Start
entity.planTimeEnd=Scheduled End
entity.temporaryUrl=Alternative access
entity.changeReason=Justification
entity.changeDescription=Change Description
entity.fileUrl=Attachment
entity.affectedDescription=Impact
entity.affectedTime=Affected Duration
entity.affectedUser=Affected User
entity.affectedApplicationIds=Affected System IDs
entity.affectedApplicationName=Affected System
entity.affectedDeviceIds=Affected Device IDs
entity.affectedDeviceName=Affected Equipment
entity.notificationEmail=Notification Email To
entity.notificationCcEmail=Notification Email CC
entity.serviceDeliveryTeamIds=Service Delivery Team IDs
entity.serviceDeliveryTeamName=Service Delivery Team
entity.teamLeaderIds=Team Leader IDs
entity.teamLeaderName=Team Leader
entity.changeApproverIds=Change Approver IDs
entity.changeApproverName=Team Manager
entity.applicationOwnerIds=System Owner IDs
entity.applicationOwner=System Owner
entity.changeImplementerIds=Change Implementer IDs
entity.changeImplementerName=Implementer
entity.changeVerifierIds=Change Verifier IDs
entity.changeVerifierName=Reviewer
entity.changeOwnerIds=Change Owner IDs
entity.changeOwnerName=Change Owner
entity.deptLeaderIds=Department Leader IDs
entity.deptLeaderName=Dept Head
entity.urgentChangeInspectorIds=Urgent Change Inspector IDs
entity.urgentChangeInspector=Emergency Reviewer
entity.systemsAffectedNo=No. of Systems Affected
entity.importantUsers=Important Users/Customers
entity.fallback=Fallback/Back Out
entity.complexity=Complexity/Experience
entity.riskLevel=Risk Level
entity.riskScore=Risk Score
entity.requestDoc=Requirements Document
entity.requestDocFileIds=Requirements Document Attachment IDs
entity.testDoc=Test Document
entity.testDocFileIds=Test Document Attachment IDs
entity.codeReview=Code Review
entity.codeReviewFileIds=Code Review Attachment IDs
entity.rollOutPlan=Roll Out Plan
entity.backoutPlan=Backout Plan
entity.checkList=Checklist
entity.reviewCheckList=Review Check List
entity.emailContent=Email Content

# 时间单位
common.time.hour=h
common.time.minute=m
common.time.second=s

# Excel表头国际化
excel.header.changeCode=Change Number
excel.header.title=Title
excel.header.stage=Stage
excel.header.processorName=Current Processor
excel.header.teamName=Change Group
excel.header.isUrgentChange=Emergency Change
excel.header.isNetworkFreezeChange=Network Freeze
excel.header.frozen=Network Freeze
excel.header.priority=Priority
excel.header.createTime=Create Time
excel.header.requesterName=Requester
excel.header.planTimeStart=Scheduled Start
excel.header.planTimeEnd=Scheduled End
excel.header.affectedUser=Affected User
excel.header.locationName=Location
excel.header.changeType=Change Type
excel.header.systemsAffectedNo=Affected System
excel.header.importantUsers=Important Users
excel.header.fallback=Fallback
excel.header.complexity=Complexity
excel.header.riskLevel=Risk Level
excel.header.riskScore=Risk Score
excel.header.processTime=The change takes time
excel.sheet.changeList=Change List

# Network Freeze Export
excel.header.freezeCode=Network Freeze No
excel.header.freezeTitle=Network Freeze Title
excel.header.freezeStage=Status
excel.header.freezeId=Freeze ID
excel.header.level=Network Freeze Level
excel.header.freezeApps=Prohibited Action
excel.header.periodAndArea=Frozen Period and Area

# 变更阶段枚举国际化
enum.change.stage.DRAFT=Draft
enum.change.stage.SUBMITTED=Planning
enum.change.stage.PENDING_APPROVAL=Pending Approval
enum.change.stage.APPROVED=Approved
enum.change.stage.IMPLEMENTING=Implementing
enum.change.stage.COMPLETED=Completed
enum.change.stage.REJECTED=Rejected
enum.change.stage.ROLLED_BACK=Rolled Back
enum.change.stage.CANCELLED=Cancelled
enum.change.stage.PENDING_VERIFICATION=Pending Verification
enum.change.stage.DELAYED=Delayed
enum.change.stage.OVER_7_DAYS=Over 7 Days

# 是否选项枚举国际化
enum.whether.YES=Yes
enum.whether.NO=No

# 优先级枚举国际化
enum.level.HIGH=High
enum.level.MEDIUM=Medium
enum.level.LOW=Low

# 受影响系统个数枚举国际化
enum.systems.affected.NONE=None (1 point)
enum.systems.affected.ONE_SYSTEM=One system(2 point)
enum.systems.affected.TWO_SYSTEMS=Two systems(3 point)
enum.systems.affected.THREE_OR_MORE_SYSTEMS=Three or more systems(4 point)

# 受影响重要用户枚举国际化
enum.important.users.NONE=None (1 point)
enum.important.users.INCONVENIENT_TO_CUSTOMER=Inconvenient to customer(2 point)
enum.important.users.OUTAGE_TO_ONE_CUSTOMER=Outage to one customer(3 point)
enum.important.users.OUTAGE_TO_TWO_OR_MORE_CUSTOMERS=Outage to two or more customers(4 point)

# 回退/回滚枚举国际化
enum.fallback.NO_BACK_OUT=No back out(1 point)
enum.fallback.BACK_OUT_TESTED=Back out and tested(2 point)
enum.fallback.BACK_OUT_UNTESTED=Back out without tested(5 point)
enum.fallback.FALLBACK_NOT_POSSIBLE=Fallback is not possible(10 point)

# 变更复杂性/实施经验枚举国际化
enum.complexity.STANDARD_WITH_GOOD_EXP=Standard technologies with good experience
enum.complexity.STANDARD_WITH_LESS_EXP=Standard technologies with less experience
enum.complexity.NEW_WITH_VENDOR_SUPPORT=New technologies with vendor support
enum.complexity.NEW_WITHOUT_VENDOR_SUPPORT=New technologies without vendor support

# Risk Level
enum.risk.level.LOW=Low
enum.risk.level.MEDIUM=Medium
enum.risk.level.HIGH=High
enum.risk.level.UNACCEPTABLE=Unacceptable

# Network Freeze Level
enum.network.freeze.level.LEVEL_ONE=Level One
enum.network.freeze.level.LEVEL_TWO=Level Two
enum.network.freeze.level.LEVEL_THREE=Level Three

# Network Freeze Stage
enum.network.freeze.stage.DRAFT=Draft
enum.network.freeze.stage.SUBMITTED=Planning
enum.network.freeze.stage.PUBLISHED=Published
enum.network.freeze.stage.REJECTED=Rejected

# 上传信息

# Network Freeze Location and Time
network.freeze.location=Location
network.freeze.start.time=Start Time
network.freeze.end.time=End Time

# Network Freeze Area Conflict
network.freeze.area.conflict=Area {0} during time period from {1} to {2} conflicts with existing network freeze application (Code: {3}), please adjust

# Different config type duplicate name tips
conf.name.duplicate.classification=Classification name already exists
conf.name.duplicate.network.blocking.area=Network blocking area name already exists
conf.name.duplicate.change.location=Change location name already exists
conf.name.duplicate.affected.device=Affected device name already exists
conf.name.duplicate.maintenance.kind=Maintenance kind name already exists

# User entity validation
user.id.not.null=User ID cannot be null
user.staffid.not.null=Account ID cannot be null
user.staffid.length.valid=Account ID length cannot exceed {max} characters
user.username.xss.invalid=Account name cannot contain script characters

# User operation result messages
user.add.fail.username.exist=Failed to add user {0}, the login account already exists
user.add.fail.email.exist=Failed to add user {0}, the email already exists
user.edit.fail.username.exist=Failed to modify user {0}, the login account already exists
user.edit.fail.email.exist=Failed to modify user {0}, the email account already exists
user.staffid.rule.invalid=Account ID does not comply with rules

# User import related
import.result=Processing result: Uploaded {0} accounts, successfully imported {1}, failed {2}
import.fail.staffId=Failed StaffID: {0}, Reason: {1}

# Role related messages
role.name.not.blank=Role name cannot be empty
role.name.length=Role name length cannot exceed {max} characters
role.key.not.blank=Role permission string cannot be empty
role.key.length=Permission string length cannot exceed {max} characters
role.sort.not.null=Display order cannot be empty
role.description.length=Description cannot exceed {max} characters

# Department related messages
dept.name.not.blank=Team name cannot be empty
dept.name.length=Team name length cannot exceed {max} characters
dept.category.length=Team category code length cannot exceed {max} characters
dept.order.not.null=Display order cannot be empty
dept.phone.length=Phone number length cannot exceed {max} characters
dept.email.format=Email format is incorrect
dept.email.length=Email length cannot exceed {max} characters
dept.description.length=Team description cannot exceed {max} characters
dept.email.sign.length=Email signature cannot exceed {max} characters

# Group operation related messages
dept.name.duplicate=Failed to add group {0}, group name already exists
dept.update.name.duplicate=Failed to modify group {0}, group name already exists
dept.update.self=Failed to modify group {0}, parent group cannot be itself
dept.has.child=There are sub-groups, deletion not allowed
dept.has.user=Group has users, deletion not allowed
dept.has.post=Group has positions, deletion not allowed
dept.init.not.allow.delete=Initialization group, deletion not allowed
dept.no.permission=No permission to access group data!

# Network freeze approval permission
network.freeze.no.permission.approval=No permission to approve {0}

# Change approval permission
change.no.permission.approval=No permission to approve {0}
