--- # 监控中心配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: false
  url: http://localhost:9090/admin
  instance:
    service-host-type: IP
    metadata:
      username: ${spring.boot.admin.client.username}
      userpassword: ${spring.boot.admin.client.password}
  username: @monitor.username@
  password: @monitor.password@

#前端域名（用于邮件超链接生成等）
host: https://changemgtportal-uat.citictel-cpc.com

--- # 文件上传配置（忽略不采用本地存储方式）
file:
  # 文件存储路径 (Windows环境配置示例)
  path: /tmp/uploadPath
  # 文件访问前缀
  prefix: /tmp/uploadPath

--- # OSS配置
oss:
  # 是否启用配置文件配置
  enabled: true
  # 默认配置Key
  default-config: minio
  # OSS配置项集合
  configs:
    # minio配置
    minio:
      # 访问站点
      endpoint: ************:9090
      # 自定义域名（为空则使用endpoint）
      domain: https://changemgtportal-uat.citictel-cpc.com/minio
      # 前缀
      prefix:
      # 访问密钥
      accessKey: MYqzAkU5Tfa9blFSUAW2
      # 密钥
      secretKey: dbOHyEBXAFHNDLRnF8crBUZUZBHrDzz61t9NEDBL
      # 存储空间名
      bucketName: change-management
      # 存储区域
      region:
      # 是否使用https（Y=是,N=否） 以endpoint进行s3连接
      isHttps: N
      # 桶权限类型(0=private私有, 1=public公共读写, 2=custom自定义-公共读私有写)
      accessPolicy: 1

--- # 认证配置
authing:
  # 是否启用
  enabled: true
  # 应用ID
  appId: 6874d45018a40d3c2de33392
  # 应用密钥
  appSecret: 9936d312d2335ad5797f2695f24d6198
  # 应用域名
  appHost: https://itssso-uat.citictel-cpc.com/6874d45018a40d3c2de33392
  # 回调地址
  redirectUri: https://changemgtportal-uat.citictel-cpc.com/api/authing/callback
  # 前端重定向URL（SSO登录成功后重定向到前端的地址）
  frontend-redirect-url: https://changemgtportal-uat.citictel-cpc.com/auth

--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: jdbc:mysql://************:4306/cm?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&autoReconnect=true&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true
          username: app
          password: B24Lx4WEp5$KWz!5
#        # 从库数据源
#        slave:
#          lazy: true
#          type: ${spring.datasource.type}
#          driverClassName: com.mysql.cj.jdbc.Driver
#          url: **********************************************************************************************************************************************************************************************************************************************************
#          username:
#          password:
#        oracle:
#          type: ${spring.datasource.type}
#          driverClassName: oracle.jdbc.OracleDriver
#          url: *************************************
#          username: ROOT
#          password: root
#        postgres:
#          type: ${spring.datasource.type}
#          driverClassName: org.postgresql.Driver
#          url: ******************************************************************************************************************************************
#          username: root
#          password: root
#        sqlserver:
#          type: ${spring.datasource.type}
#          driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          url: *******************************************************************************************************************
#          username: SA
#          password: root
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 多久检查一次连接的活性
        keepaliveTime: 30000

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring.data:
  redis:
    cluster:
      nodes:
        - ************:7379
        - ************:8379
        - ************:9379
        - ************:7379
        - ************:8379
        - ************:9379
      # 最大重定向次数
      maxRedirects: 3
    # 地址
    # host: ***********
    # 端口，默认为6379
    # port: 6379
    # 数据库索引
    # database: 2
    # redis 密码必须配置
    password: wmHOoDLZYQT1nXp3NnK9
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl.enabled: false

# redisson 配置
redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 集群配置
  clusterServersConfig:
    # 客户端名称 不能用中文
    clientName: changeManagement
    # 集群节点地址
    nodeAddresses:
      - "redis://************:7379"
      - "redis://************:8379"
      - "redis://************:9379"
      - "redis://************:7379"
      - "redis://************:8379"
      - "redis://************:9379"
    # 密码
    password: wmHOoDLZYQT1nXp3NnK9
    # 最小空闲连接数
    idleConnectionTimeout: 10000
    # 连接池大小
    masterConnectionPoolSize: 32
    slaveConnectionPoolSize: 32
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
    # 重试间隔时间，单位：毫秒
    retryInterval: 1500

--- # mail 邮件发送
mail:
  enabled: true
  host: zmta04cn.china-entercom.net
  port: 25
  # 是否需要用户名密码验证
  auth: false
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass:
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: false
  # 使用SSL安全连接
  sslEnable: false
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0
  # 是否启用固定收件人（用于测试环境）
  useFixedRecipients: false
  # 固定收件人配置
  fixedTo: <EMAIL>
  fixedCc: <EMAIL>

# smartflow
cec-smartflow:
  smartFlowDomain: http://************:38684
  smartFlowAccount: sysAdmin
  smartFlowPwd: qdsadf#@fsdfs
  appSecret: g2rcN8j0di!s
  # 是否开启全量同步
  syncSmartFlowAll: true
  param:
    # 调用smartflow接口使用默认参数
    deptId: 7
    deptName: Innovative Research & Development and Information Technology
    formKey: changeManagement
    # 同步账号
    bizLine: 1
    externalOrgId: 7
    externalRegionId: 0
    region: China-Beijing

