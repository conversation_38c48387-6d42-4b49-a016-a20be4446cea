package com.cec.web.service.impl;

import cn.authing.sdk.java.client.AuthenticationClient;
import cn.authing.sdk.java.dto.GetProfileDto;
import cn.authing.sdk.java.dto.UserSingleRespDto;
import cn.authing.sdk.java.dto.authentication.IOidcParams;
import cn.authing.sdk.java.dto.authentication.OIDCTokenResponse;
import cn.authing.sdk.java.model.AuthenticationClientOptions;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cec.common.core.domain.R;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.utils.ServletUtils;
import org.apache.commons.lang3.StringUtils;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.SysUser;
import com.cec.system.domain.vo.SysClientVo;
import com.cec.system.domain.vo.SysUserVo;
import com.cec.system.mapper.SysUserMapper;
import com.cec.system.service.ISysClientService;
import com.cec.web.domain.vo.LoginVo;
import com.cec.web.service.SysLoginService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.ParseException;

/**
 * Authing认证策略实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component("authingAuthStrategy")
public class AuthingAuthStrategy {

    private final SysLoginService loginService;
    private final SysUserMapper userMapper;
    private final ISysClientService clientService;

    @Value("${authing.enabled:false}")
    private boolean enabled;

    @Value("${authing.appId}")
    private String appId;

    @Value("${authing.appSecret}")
    private String appSecret;

    @Value("${authing.appHost}")
    private String appHost;

    @Value("${authing.redirectUri}")
    private String redirectUri;

    /**
     * 登录处理
     *
     * @param code  授权码
     * @param state 状态
     * @return 登录结果
     */
    public R<LoginVo> login(String code, String state) {
        if (!enabled) {
            return R.fail("Authing SSO 未启用");
        }

        try {
            // 创建认证客户端
            AuthenticationClientOptions options = new AuthenticationClientOptions();
            options.setAppId(appId);
            options.setAppSecret(appSecret);
            options.setAppHost(appHost);
            options.setRedirectUri(redirectUri);

            AuthenticationClient authenticationClient = new AuthenticationClient(options);
            //  获取token
            OIDCTokenResponse respDto = authenticationClient.getAccessTokenByCode(code);

            // 获取用户信息
            UserSingleRespDto profile = authenticationClient.getProfile(new GetProfileDto());
            log.info("profile: {}", JSONUtil.toJsonPrettyStr(profile));
            if (profile == null || profile.getData() == null) {
                log.error("SSO无法获取用户信息");
                return R.fail("无法获取用户信息");
            }

            // 获取用户名
            String username = getUsername(profile);
            if (StringUtils.isBlank(username)) {
                return R.fail("用户名不能为空");
            }

            // 根据用户名查找用户
            SysUserVo user = userMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, username));
            if (user == null) {
                log.error("用户不存在，请联系管理员");
                return R.fail("用户不存在，请联系管理员");
            }

            // 记录登录信息
            loginService.recordLoginInfo(user.getUserId(), ServletUtils.getClientIP());

            // 生成token
            LoginUser loginUser = loginService.buildLoginUser(user);
            SysClientVo client = clientService.queryByClientId("e5cd7e4891bf95d1d19206ce24a7b32e");
            SaLoginModel model = new SaLoginModel();
            model.setDevice(client.getDeviceType());
            // 自定义分配 不同用户体系 不同 token 授权时间 不设置默认走全局 yml 配置
            // 例如: 后台用户30分钟过期 app用户1天过期
            model.setTimeout(client.getTimeout());
            model.setActiveTimeout(client.getActiveTimeout());
            model.setExtra(LoginHelper.CLIENT_KEY, client.getClientId());

            // 登录
            LoginHelper.login(loginUser, model);

            // 构建登录返回信息
            LoginVo loginVo = new LoginVo();
            loginVo.setAccessToken(StpUtil.getTokenValue());
            loginVo.setExpireIn(StpUtil.getTokenTimeout());
            loginVo.setClientId(appId);

            return R.ok(loginVo);
        } catch (Exception e) {
            log.error("Authing登录异常", e);
            return R.fail("登录异常，请稍后再试");
        }
    }

    /**
     * 从Authing用户信息中获取用户名
     *
     * @param profile 用户信息
     * @return 用户名
     */
    private String getUsername(UserSingleRespDto profile) {
        return profile.getData().getUsername();
    }

    /**
     * 获取授权URL
     *
     * @return 授权URL
     */
    public String getAuthorizeUrl() throws IOException, ParseException {
        if (!enabled) {
            return null;
        }

        AuthenticationClientOptions options = new AuthenticationClientOptions();
        options.setAppId(appId);
        options.setAppSecret(appSecret);
        options.setAppHost(appHost);
        options.setRedirectUri(redirectUri);

        AuthenticationClient authenticationClient = new AuthenticationClient(options);
        IOidcParams iOidcParams = new IOidcParams();
        iOidcParams.setRedirectUri(redirectUri);
        return authenticationClient.buildAuthorizeUrl(iOidcParams);
    }
}
