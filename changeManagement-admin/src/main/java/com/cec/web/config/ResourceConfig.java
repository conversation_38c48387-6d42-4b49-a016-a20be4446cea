package com.cec.web.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

/**
 * 通用配置
 */
@Configuration
public class ResourceConfig implements WebMvcConfigurer {

    @Value("${file.prefix}")
    private String filePrefix;

    @Value("${file.path}")
    private String filePath;

    /**
     * 静态资源映射
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        /** 上传文件映射访问 */
        String pathPattern = filePrefix + "/**";
        // 确保路径格式正确
        if (!filePath.endsWith(File.separator)) {
            filePath += File.separator;
        }
        registry.addResourceHandler(pathPattern)
                .addResourceLocations("file:" + filePath);
    }
}
