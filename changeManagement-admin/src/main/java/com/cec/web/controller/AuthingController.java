package com.cec.web.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.cec.common.core.domain.R;
import com.cec.common.social.config.properties.SocialProperties;
import com.cec.system.service.ISysClientService;
import com.cec.system.service.ISysConfigService;
import com.cec.system.service.ISysSocialService;
import com.cec.system.service.ISysTenantService;
import com.cec.web.domain.vo.LoginVo;
import com.cec.web.service.SysLoginService;
import com.cec.web.service.SysRegisterService;
import com.cec.web.service.impl.AuthingAuthStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ScheduledExecutorService;

/**
 * authing测试
 *
 * <AUTHOR> Li
 */
@Slf4j
@SaIgnore
@RequiredArgsConstructor
@RestController
@RequestMapping("/authing")
public class AuthingController {

    private final SocialProperties socialProperties;
    private final SysLoginService loginService;
    private final SysRegisterService registerService;
    private final ISysConfigService configService;
    private final ISysTenantService tenantService;
    private final ISysSocialService socialUserService;
    private final ISysClientService clientService;
    private final ScheduledExecutorService scheduledExecutorService;
    private final AuthingAuthStrategy authingAuthStrategy;

    @Value("${authing.frontend-redirect-url:http://localhost:8000}")
    private String frontendRedirectUrl;

    /**
     * sso请求重定向地址
     *
     * @return 结果
     */
//    @GetMapping("/login")
//    public ResponseEntity<Void> login() {
//        try {
//            String authorizeUrl = authingAuthStrategy.getAuthorizeUrl();
//            if (authorizeUrl == null) {
//                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).build();
//            }
//
//            log.info("生成的授权URL: {}", authorizeUrl);
//            // 对URL进行编码处理
//            String encodedUrl = encodeUrl(authorizeUrl);
//            log.info("生成的授权URL: {}", encodedUrl);
//
//            return ResponseEntity.status(HttpStatus.FOUND)
//                .location(URI.create(encodedUrl))
//                .build();
//        } catch (Exception e) {
//            log.error("Authing授权URL生成失败", e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//        }
//    }
    @GetMapping("/login")
    public R<String> login() {
        try {
            String authorizeUrl = authingAuthStrategy.getAuthorizeUrl();

            log.info("生成的授权URL: {}", authorizeUrl);
            // 对URL进行编码处理
            String encodedUrl = encodeUrl(authorizeUrl);
            log.info("生成的授权URL: {}", encodedUrl);

            return R.ok(encodedUrl);
        } catch (Exception e) {
            log.error("Authing授权URL生成失败", e);
            throw new RuntimeException("Authing授权URL生成失败");
        }
    }

    /**
     * 前端回调绑定授权
     *
     * @param code  Code
     * @param state State
     */
    @GetMapping("/callback")
    public ResponseEntity<Void> callback(@RequestParam String code, @RequestParam String state) {
        try {
            // 调用认证策略处理登录
            R<LoginVo> result = authingAuthStrategy.login(code, state);
            log.info("Authing登录处理结果 - code: {}, msg: {}", result.getCode(), result.getMsg());

            // 构建重定向URL，将token和其他信息作为参数传给前端
            StringBuilder redirectUrlBuilder = new StringBuilder(frontendRedirectUrl);

            if (result.getCode() == 200 && result.getData() != null) {
                // 登录成功，将token等信息添加到重定向URL
                LoginVo loginVo = result.getData();
                redirectUrlBuilder.append("?token=").append(loginVo.getAccessToken())
                    .append("&expire_in=").append(loginVo.getExpireIn())
                    .append("&client_id=").append(loginVo.getClientId())
                    .append("&login_success=true");
                log.info("Authing登录成功，准备重定向到前端");
            } else {
                // 登录失败
                redirectUrlBuilder.append("?login_success=false")
                    .append("&error_msg=").append(URLEncoder.encode(result.getMsg(), StandardCharsets.UTF_8));
                log.warn("Authing登录失败: {}", result.getMsg());
            }

            // 执行重定向
            String redirectUrl = redirectUrlBuilder.toString();
            log.info("重定向到前端: {}", redirectUrl);
            return ResponseEntity.status(HttpStatus.FOUND)
                .location(URI.create(redirectUrl))
                .build();
        } catch (Exception e) {
            log.error("Authing回调处理失败", e);
            // 发生异常时也重定向到前端，但带上错误信息
            String redirectUrl = frontendRedirectUrl + "?login_success=false&error_msg=" +
                URLEncoder.encode("认证失败，请稍后再试", StandardCharsets.UTF_8);
            return ResponseEntity.status(HttpStatus.FOUND)
                .location(URI.create(redirectUrl))
                .build();
        }
    }

    /**
     * 对URL进行编码处理，确保特殊字符被正确编码
     *
     * @param url 原始URL
     * @return 编码后的URL
     */
    private String encodeUrl(String url) {
        if (url == null || url.isEmpty()) {
            return url;
        }

        // 分割URL为基础部分和查询参数
        String baseUrl;
        String queryParams;

        int queryIndex = url.indexOf('?');
        if (queryIndex < 0) {
            return url;
        }

        baseUrl = url.substring(0, queryIndex);
        queryParams = url.substring(queryIndex + 1);

        // 处理查询参数
        StringBuilder encodedUrl = new StringBuilder(baseUrl).append('?');
        String[] params = queryParams.split("&");

        for (int i = 0; i < params.length; i++) {
            String param = params[i];
            int equalsIndex = param.indexOf('=');

            if (equalsIndex > 0) {
                String key = param.substring(0, equalsIndex);
                String value = param.substring(equalsIndex + 1);

                // 编码值部分
                String encodedValue = URLEncoder.encode(value, StandardCharsets.UTF_8)
                    .replace("+", "%20"); // 空格在URL中应为%20而不是+

                encodedUrl.append(key).append('=').append(encodedValue);
            } else {
                encodedUrl.append(param);
            }

            if (i < params.length - 1) {
                encodedUrl.append('&');
            }
        }

        return encodedUrl.toString();
    }
}
