<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cec</groupId>
        <artifactId>changeManagement-backend</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>changeManagement-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

<!--        &lt;!&ndash; mp支持的数据库均支持 只需要增加对应的jdbc依赖即可 &ndash;&gt;-->
<!--        &lt;!&ndash; Oracle &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.oracle.database.jdbc</groupId>-->
<!--            <artifactId>ojdbc8</artifactId>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; 兼容oracle低版本 &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.oracle.database.nls</groupId>-->
<!--            <artifactId>orai18n</artifactId>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; PostgreSql &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.postgresql</groupId>-->
<!--            <artifactId>postgresql</artifactId>-->
<!--        </dependency>-->
<!--        &lt;!&ndash; SqlServer &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.microsoft.sqlserver</groupId>-->
<!--            <artifactId>mssql-jdbc</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-social</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-business</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-generator</artifactId>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- skywalking 整合 logback -->
<!--        <dependency>-->
<!--            <groupId>org.apache.skywalking</groupId>-->
<!--            <artifactId>apm-toolkit-logback-1.x</artifactId>-->
<!--            <version>${与你的agent探针版本保持一致}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.skywalking</groupId>-->
<!--            <artifactId>apm-toolkit-trace</artifactId>-->
<!--            <version>${与你的agent探针版本保持一致}</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>cn.authing</groupId>
            <artifactId>authing-java-sdk</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
