package com.cec.business.domain;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.cec.system.domain.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 变更申请记录对象 cm_change_item
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cm_change_item", autoResultMap = true)
public class ChangeItem extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 变更ID
     */
    private Long changeId;

    /**
     * 变更编号
     */
    private String changeCode;

    /**
     * 变更标题
     */
    private String title;

    /**
     * 阶段
     * 1-草稿 2-已提交 3-待审批 4-已审批 5-实施中 6-已完成 7-已拒绝 8-已回滚 9-已取消 10-待验证
     */
    private ChangeStageEnum stage;

    /**
     * 当前处理人
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<UserVo> processorList;

    /**
     * 变更组ID
     */
    private Long teamId;

    /**
     * 变更组名
     */
    private String teamName;

    /**
     * 是否紧急变更（1-是 2-否）
     */
    private WhetherEnum isUrgentChange;

    /**
     * 优先级(1-高 2-中 3-低)
     */
    private LevelEnum priority;

    /**
     * 流程实例id
     */
    private String instanceId;

    /**
     * smartFlow Variables
     */
    private String variables;

    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 申请人ID
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private UserVo requester;

    /**
     * 是否封网变更（1-是 2-否）
     */
    private WhetherEnum isNetworkFreezeChange;

    /**
     * 地点id
     */
    private Long locationId;

    /**
     * 地点名
     */
    private String locationName;
}
