package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 批量查询任务下一节点响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowTaskNextBatchListResp extends BaseResp implements Serializable {

    /**
     * 响应数据
     */
    private List<FlowTaskNextBatchResult> data;

    @Data
    public static class FlowTaskNextBatchResult {
        /**
         * 任务ID
         */
        private Long id;
        /**
         * 节点列表
         */
        private List<FlowTaskNode> nodeList;
    }

    @Data
    public static class FlowTaskNode {
        /**
         * 候选人列表
         */
        private List<Candidate> candidateList;
        /**
         * 会签类型 0：会签 1：或签
         */
        private Integer countersignType;
        /**
         * 节点id
         */
        private String id;
        /**
         * 节点名称
         */
        private String name;
        /**
         * 下个执行步骤列表
         */
        private List<FlowTaskNode> next;
        /**
         * 是否自选 0：否， 1：是
         */
        private Integer selfSelect;
    }

    @Data
    public static class Candidate implements Serializable {
        /**
         * 审批人id
         */
        private String assigneeId;
        /**
         * 审批人部门id
         */
        private String assigneeOrgId;
        /**
         * 审批人名称
         */
        private String assigneeName;
        /**
         * 审批人部门名称
         */
        private String assigneeOrgName;
    }
} 