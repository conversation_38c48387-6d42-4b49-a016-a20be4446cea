//package com.cec.business.controller;
//
//import java.util.List;
//
//import lombok.RequiredArgsConstructor;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.constraints.*;
//import cn.dev33.satoken.annotation.SaCheckPermission;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.validation.annotation.Validated;
//import com.cec.common.idempotent.annotation.RepeatSubmit;
//import com.cec.common.log.annotation.Log;
//import com.cec.common.web.core.BaseController;
//import com.cec.common.mybatis.core.page.PageQuery;
//import com.cec.common.core.domain.R;
//import com.cec.common.core.validate.AddGroup;
//import com.cec.common.core.validate.EditGroup;
//import com.cec.common.log.enums.BusinessType;
//import com.cec.common.excel.utils.ExcelUtil;
//import com.cec.business.domain.vo.NetworkFreezeRecordVo;
//import com.cec.business.domain.bo.NetworkFreezeRecordBo;
//import com.cec.business.service.INetworkFreezeRecordService;
//import com.cec.common.mybatis.core.page.TableDataInfo;
//
///**
// * 封网申请记录流水
// *
// * <AUTHOR>
// * @date 2025-05-13
// */
//@Validated
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/business/networkFreezeRecord")
//public class NetworkFreezeRecordController extends BaseController {
//
//    private final INetworkFreezeRecordService networkFreezeRecordService;
//
//    /**
//     * 查询封网申请记录流水列表
//     */
//    @SaCheckPermission("business:networkFreezeRecord:list")
//    @GetMapping("/list")
//    public TableDataInfo<NetworkFreezeRecordVo> list(NetworkFreezeRecordBo bo, PageQuery pageQuery) {
//        return networkFreezeRecordService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出封网申请记录流水列表
//     */
//    @SaCheckPermission("business:networkFreezeRecord:export")
//    @Log(title = "封网申请记录流水", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(NetworkFreezeRecordBo bo, HttpServletResponse response) {
//        List<NetworkFreezeRecordVo> list = networkFreezeRecordService.queryList(bo);
//        ExcelUtil.exportExcel(list, "封网申请记录流水", NetworkFreezeRecordVo.class, response);
//    }
//
//    /**
//     * 获取封网申请记录流水详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("business:networkFreezeRecord:query")
//    @GetMapping("/{id}")
//    public R<NetworkFreezeRecordVo> getInfo(@NotNull(message = "主键不能为空")
//                                     @PathVariable Long id) {
//        return R.ok(networkFreezeRecordService.queryById(id));
//    }
//
//    /**
//     * 新增封网申请记录流水
//     */
//    @SaCheckPermission("business:networkFreezeRecord:add")
//    @Log(title = "封网申请记录流水", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody NetworkFreezeRecordBo bo) {
//        return toAjax(networkFreezeRecordService.insertByBo(bo));
//    }
//
//    /**
//     * 修改封网申请记录流水
//     */
//    @SaCheckPermission("business:networkFreezeRecord:edit")
//    @Log(title = "封网申请记录流水", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody NetworkFreezeRecordBo bo) {
//        return toAjax(networkFreezeRecordService.updateByBo(bo));
//    }
//
//    /**
//     * 删除封网申请记录流水
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("business:networkFreezeRecord:remove")
//    @Log(title = "封网申请记录流水", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//                          @PathVariable Long[] ids) {
//        return toAjax(networkFreezeRecordService.deleteWithValidByIds(List.of(ids), true));
//    }
//}
