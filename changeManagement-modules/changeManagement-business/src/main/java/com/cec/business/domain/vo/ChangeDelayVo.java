package com.cec.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cec.business.domain.ChangeDelay;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 变更延期记录视图对象 cm_change_delay
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ChangeDelay.class)
public class ChangeDelayVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 变更id
     */
    @ExcelProperty(value = "变更id")
    private Long changeId;

    /**
     * 新结束时间
     */
    @ExcelProperty(value = "新结束时间")
    private Date scheduleEndTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 延期文件
     */
    @ExcelProperty(value = "延期文件")
    private List<String> delayFiles;

}
