package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/***
 * 回退任务
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowTaskRejectReq extends BaseReq implements Serializable {
    /**
     * 处理人id	true	string
     */
    @NotEmpty(message = "处理人id不能为空")
    private String assigneeId;
    /**
     * 处理人部门id	true	string
     */
    @NotEmpty(message = "处理人部门id不能为空")
    private String assigneeOrgId;
    /**
     * 任务id	true	integer
     */
    @NotNull(message = "任务id不能为空")
    private Long id;
    /**
     * 审批意见
     */
    private String comment;
    /**
     * 流程自定义表单数据
     */
    private String formData;

    @Override
    public String method() {
        return "/openapi/flow/task/reject";
    }
}
