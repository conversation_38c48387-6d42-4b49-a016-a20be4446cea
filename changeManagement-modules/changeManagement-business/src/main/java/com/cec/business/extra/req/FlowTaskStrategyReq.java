package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/***
 * 查询任务处理策略
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowTaskStrategyReq extends BaseReq implements Serializable {
    /**
     * 表单唯一key
     */
    @NotEmpty(message = "表单唯一key不能为空")
    private String formKey;
    /**
     * 处理人id
     */
    private String assigneeId;
    /**
     * 处理人部门id
     */
    private String assigneeOrgId;
    /**
     * 任务id
     */
    private String id;

    @Override
    public String method() {
        return "/openapi/flow/task/strategy";
    }
}
