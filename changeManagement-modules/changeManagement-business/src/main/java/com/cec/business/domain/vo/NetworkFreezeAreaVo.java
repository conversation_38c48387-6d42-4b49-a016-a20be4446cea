package com.cec.business.domain.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.cec.business.domain.NetworkFreezeArea;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 封网申请对象 cm_network_freeze_info
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@AutoMapper(target = NetworkFreezeArea.class)
public class NetworkFreezeAreaVo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 封网ID
     */
    private Long freezeId;

    /**
     * 封网地区ID
     */
    private List<String> areaId;

    /**
     * 封网地区名称
     */
    private List<String> areaName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
