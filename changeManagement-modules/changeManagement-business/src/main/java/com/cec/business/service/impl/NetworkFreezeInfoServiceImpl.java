package com.cec.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.business.domain.NetworkFreezeArea;
import com.cec.business.domain.NetworkFreezeInfo;
import com.cec.business.domain.NetworkFreezeItem;
import com.cec.business.domain.NetworkFreezeRecord;
import com.cec.business.domain.bo.NetworkFreezeInfoBo;
import com.cec.business.domain.bo.NetworkFreezeItemBo;
import com.cec.business.domain.bo.NetworkFreezeRecordBo;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.business.domain.vo.FlowNodeVo;
import com.cec.business.domain.vo.NetworkFreezeInfoVo;
import com.cec.business.domain.vo.NetworkFreezeItemVo;
import com.cec.business.domain.vo.NetworkFreezeRecordVo;
import com.cec.business.domain.vo.PeriodAndAreaVo;
import com.cec.business.extra.SmartFlowUtils;
import com.cec.business.extra.req.FlowInstanceStartReq;
import com.cec.business.extra.req.FlowTaskApproveReq;
import com.cec.business.extra.req.FlowTaskCurrentReq;
import com.cec.business.extra.req.FlowTaskNextListReq;
import com.cec.business.extra.req.FlowTaskRejectReq;
import com.cec.business.extra.req.FlowTaskStrategyReq;
import com.cec.business.extra.resp.FlowInstanceBaseResp;
import com.cec.business.extra.resp.FlowInstanceResp;
import com.cec.business.extra.resp.FlowInstanceStartResp;
import com.cec.business.extra.resp.FlowTaskCurrentResp;
import com.cec.business.extra.resp.FlowTaskNextListResp;
import com.cec.business.extra.resp.FlowTaskStrategyResp;
import com.cec.business.mapper.NetworkFreezeAreaMapper;
import com.cec.business.mapper.NetworkFreezeInfoMapper;
import com.cec.business.mapper.NetworkFreezeItemMapper;
import com.cec.business.mapper.NetworkFreezeRecordMapper;
import com.cec.business.service.EmailService;
import com.cec.business.service.INetworkFreezeService;
import com.cec.common.core.config.CecSmartFlowConfig;
import com.cec.common.core.constant.SystemConstants;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.utils.ObjectUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.json.utils.JsonUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.redis.utils.SequenceUtils;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.SysUser;
import com.cec.system.domain.vo.SysUserVo;
import com.cec.system.domain.vo.UserVo;
import com.cec.system.mapper.SysRoleMapper;
import com.cec.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 封网申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class NetworkFreezeInfoServiceImpl implements INetworkFreezeService {

    private final NetworkFreezeInfoMapper networkFreezeInfoMapper;
    private final NetworkFreezeItemMapper networkFreezeItemMapper;
    private final NetworkFreezeRecordMapper networkFreezeRecordMapper;
    private final SmartFlowUtils smartFlowUtils;
    private final NetworkFreezeAreaMapper networkFreezeAreaMapper;
    private final SysRoleMapper sysRoleMapper;
    private final SysUserMapper sysUserMapper;
    private final EmailService emailService;
    private final CecSmartFlowConfig cecSmartFlowConfig;

    // 添加常量定义流程状态码
    private final static Integer SUCCESS_CODE = 200;

    // 邮件发送重试参数
    private static final int EMAIL_MAX_RETRIES = 3;
    private static final int EMAIL_RETRY_DELAY_MS = 2000; // 2秒

    /**
     * 查询封网申请
     *
     * @param id 主键
     * @return 封网申请
     */
    @Override
    public NetworkFreezeInfoVo queryById(Long id) {
        NetworkFreezeInfo networkFreezeInfo = networkFreezeInfoMapper.selectById(id);
        if (ObjectUtils.isNull(networkFreezeInfo)) {
            return null;
        }

        List<NetworkFreezeArea> networkFreezeAreas = networkFreezeAreaMapper.selectList(new LambdaQueryWrapper<NetworkFreezeArea>().eq(NetworkFreezeArea::getFreezeId, id));
        NetworkFreezeInfoVo vo = BeanUtil.copyProperties(networkFreezeInfo, NetworkFreezeInfoVo.class, "periodAndArea", "notificationEmailTo", "notificationEmailCc");
        vo.setPeriodAndArea(networkFreezeAreas);
        vo.setNotificationEmailTo(JsonUtils.parseArray(networkFreezeInfo.getNotificationEmailTo(), String.class));
        vo.setNotificationEmailCc(JsonUtils.parseArray(networkFreezeInfo.getNotificationEmailCc(), String.class));
        NetworkFreezeItemVo itemVo = networkFreezeItemMapper.selectVoOne(new LambdaQueryWrapper<NetworkFreezeItem>().eq(NetworkFreezeItem::getFreezeId, id));
        Optional.ofNullable(itemVo).ifPresent(x -> {
            vo.setFreezeCode(x.getFreezeCode());
            vo.setStage(x.getStage());
        });
        return vo;
    }

    /**
     * 分页查询封网申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 封网申请分页列表
     */
    @Override
    public TableDataInfo<NetworkFreezeItemVo> queryPageList(NetworkFreezeItemBo bo, PageQuery pageQuery) {
        // 确定要返回的字段
        List<String> code = bo.getSelectFiledNameList() != null && !bo.getSelectFiledNameList().isEmpty()
            ? bo.getSelectFiledNameList()
            : List.of("freezeCode", "title", "level", "stage", "processorList", "createTime");

        // 查询数据
        LambdaQueryWrapper<NetworkFreezeItem> lqw = buildQueryWrapper(bo);
        String orderByColumn = pageQuery.getOrderByColumn();
        if (StringUtils.isBlank(orderByColumn)) {
            lqw.orderByDesc(NetworkFreezeItem::getId);
        }
        Page<NetworkFreezeItemVo> result = networkFreezeItemMapper.selectVoPage(pageQuery.build(), lqw);
        List<NetworkFreezeItemVo> records = result.getRecords();

        if (records != null && !records.isEmpty()) {
            // 判断是否需要查询额外字段
            boolean needExtraFields1 = code.contains("freezeApps") || code.contains("periodAndArea");

            // 如果需要额外字段，批量查询相关信息
            Map<Long, NetworkFreezeInfo> infoMap = needExtraFields1
                ? networkFreezeInfoMapper.selectBatchIds(
                    records.stream().map(NetworkFreezeItemVo::getFreezeId).toList())
                .stream().collect(java.util.stream.Collectors.toMap(
                    NetworkFreezeInfo::getId,
                    info -> info,
                    (k1, k2) -> k1))
                : Collections.emptyMap();

            boolean needExtraFields2 = code.contains("periodAndArea");
            Map<Long, List<NetworkFreezeArea>> areaMap = needExtraFields2
                ? networkFreezeAreaMapper.selectList(new LambdaQueryWrapper<NetworkFreezeArea>().in(NetworkFreezeArea::getFreezeId, records.stream().map(NetworkFreezeItemVo::getFreezeId).toList()))
                .stream().collect(java.util.stream.Collectors.groupingBy(NetworkFreezeArea::getFreezeId))
                : Collections.emptyMap();


            // 过滤记录中的字段
            result.setRecords(records.stream()
                .map(item -> {
                    // 创建新对象
                    NetworkFreezeItemVo filteredItem = new NetworkFreezeItemVo();

                    // 总是保留ID字段
                    filteredItem.setId(item.getId());
                    filteredItem.setFreezeId(item.getFreezeId());

                    // 有选择地设置基本字段
                    if (code.contains("freezeCode")) filteredItem.setFreezeCode(item.getFreezeCode());
                    if (code.contains("title")) filteredItem.setTitle(item.getTitle());
                    if (code.contains("level")) filteredItem.setLevel(item.getLevel());
                    if (code.contains("stage")) filteredItem.setStage(item.getStage());
                    if (code.contains("processorList"))
                        filteredItem.setProcessorList(item.getProcessorList());
                    if (code.contains("createTime")) filteredItem.setCreateTime(item.getCreateTime());

                    // 处理需要额外查询的字段
                    Optional.ofNullable(infoMap.get(item.getFreezeId())).ifPresent(info -> {
                        if (code.contains("periodAndArea")) {
                            filteredItem.setPeriodAndArea(areaMap.get(info.getId()));
                        }
                        if (code.contains("freezeApps") && StringUtils.isNotBlank(info.getFreezeApps())) {
                            filteredItem.setFreezeApps(info.getFreezeApps());
                        }
                    });

                    return filteredItem;
                }).toList());
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的封网申请列表
     *
     * @param bo 查询条件
     * @return 封网申请列表
     */
    @Override
    public List<NetworkFreezeInfoVo> queryList(NetworkFreezeInfoBo bo) {
//        LambdaQueryWrapper<NetworkFreezeInfo> lqw = buildQueryWrapper(bo);
//        return networkFreezeInfoMapper.selectVoList(lqw);
        return null;
    }

    private LambdaQueryWrapper<NetworkFreezeItem> buildQueryWrapper(NetworkFreezeItemBo bo) {

        List<Long> freezeIds = null;
        boolean hasAreaOrTimeFilter = StringUtils.isNotBlank(bo.getAreaId())
            || StringUtils.isNotBlank(bo.getFreezeTimeStart())
            || StringUtils.isNotBlank(bo.getFreezeTimeEnd());

        if (hasAreaOrTimeFilter) {
            LambdaQueryWrapper<NetworkFreezeArea> lqw = Wrappers.lambdaQuery();
            lqw.like(StringUtils.isNotBlank(bo.getAreaId()), NetworkFreezeArea::getAreaId, bo.getAreaId());

            // 修复时间范围重叠查询逻辑
            if (StringUtils.isNotBlank(bo.getFreezeTimeStart()) || StringUtils.isNotBlank(bo.getFreezeTimeEnd())) {
                lqw.and(wrapper -> {
                    // 情况1：数据的开始时间在查询范围内
                    if (StringUtils.isNotBlank(bo.getFreezeTimeStart()) && StringUtils.isNotBlank(bo.getFreezeTimeEnd())) {
                        wrapper.between(NetworkFreezeArea::getStartTime, bo.getFreezeTimeStart(), bo.getFreezeTimeEnd())
                            // 情况2：数据的结束时间在查询范围内
                            .or().between(NetworkFreezeArea::getEndTime, bo.getFreezeTimeStart(), bo.getFreezeTimeEnd())
                            // 情况3：数据的时间范围完全包含查询范围（开始时间早于查询开始，结束时间晚于查询结束）
                            .or(w -> w.le(NetworkFreezeArea::getStartTime, bo.getFreezeTimeStart()).ge(NetworkFreezeArea::getEndTime, bo.getFreezeTimeEnd()));
                    } else if (StringUtils.isNotBlank(bo.getFreezeTimeStart())) {
                        // 只有开始时间：数据的结束时间 >= 查询开始时间
                        wrapper.ge(NetworkFreezeArea::getEndTime, bo.getFreezeTimeStart());
                    } else if (StringUtils.isNotBlank(bo.getFreezeTimeEnd())) {
                        // 只有结束时间：数据的开始时间 <= 查询结束时间
                        wrapper.le(NetworkFreezeArea::getStartTime, bo.getFreezeTimeEnd());
                    }
                });
            }

            List<NetworkFreezeArea> networkFreezeAreas = networkFreezeAreaMapper.selectList(lqw);
            freezeIds = networkFreezeAreas.stream().map(NetworkFreezeArea::getFreezeId).distinct().toList();

            // 如果使用了区域或时间过滤条件但没有找到匹配的记录，强制返回空结果
            if (CollUtil.isEmpty(freezeIds)) {
                LambdaQueryWrapper<NetworkFreezeItem> emptyLqw = Wrappers.lambdaQuery();
                emptyLqw.eq(NetworkFreezeItem::getId, -1L);
                return emptyLqw;
            }
        }

        LambdaQueryWrapper<NetworkFreezeItem> lqw = Wrappers.lambdaQuery();
        //lqw.orderByDesc(NetworkFreezeItem::getId);
        lqw.in(CollUtil.isNotEmpty(freezeIds), NetworkFreezeItem::getFreezeId, freezeIds);
        lqw.like(StringUtils.isNotBlank(bo.getFreezeCode()), NetworkFreezeItem::getFreezeCode, bo.getFreezeCode());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), NetworkFreezeItem::getTitle, bo.getTitle());
        lqw.apply(StringUtils.isNotBlank(bo.getProcessorName()), "JSON_SEARCH(processor_list, 'one', CONCAT('%', {0}, '%'), NULL, '$[*].staffName') IS NOT NULL", bo.getProcessorName());
        lqw.in(CollUtil.isNotEmpty(bo.getLevelList()), NetworkFreezeItem::getLevel, bo.getLevelList());
        lqw.in(CollUtil.isNotEmpty(bo.getStageList()), NetworkFreezeItem::getStage, bo.getStageList());
        lqw.ge(StringUtils.isNotBlank(bo.getCreateTimeStart()), NetworkFreezeItem::getCreateTime, bo.getCreateTimeStart());
        lqw.le(StringUtils.isNotBlank(bo.getCreateTimeEnd()), NetworkFreezeItem::getCreateTime, bo.getCreateTimeEnd());
        return lqw;
    }

    /**
     * 新增封网申请
     *
     * @param bo 封网申请
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByBo(NetworkFreezeInfoBo bo) {

        LoginUser user = LoginHelper.getLoginUser();
        ArrayList<NetworkFreezeRecord> records = new ArrayList<>();

        // 转换并保存基本信息
        NetworkFreezeInfo networkFreezeInfo = BeanUtil.copyProperties(bo, NetworkFreezeInfo.class,
            "periodAndArea", "notificationEmailTo", "notificationEmailCc");
        networkFreezeInfo.setNotificationEmailTo(JsonUtils.toJsonString(bo.getNotificationEmailTo()));
        networkFreezeInfo.setNotificationEmailCc(JsonUtils.toJsonString(bo.getNotificationEmailCc()));

        if (!NetworkFreezeStageEnum.DRAFT.equals(bo.getStage())) {
            checkPeriodAndArea(bo.getPeriodAndArea());
        }

        // 保存插入结果
        networkFreezeInfo.setRequester(BeanUtil.copyProperties(user.getUserVo(), UserVo.class));
        int infoResult = networkFreezeInfoMapper.insert(networkFreezeInfo);
        if (infoResult <= 0) {
            throw new ServiceException("保存封网申请基本信息失败");
        }

        // 初始化最早开始时间和最晚结束时间
        Date earliestStartTime = null;
        Date latestEndTime = null;
        Date now = new Date();

        for (PeriodAndAreaVo areaVo : bo.getPeriodAndArea()) {

            if (!NetworkFreezeStageEnum.DRAFT.equals(bo.getStage())) {
                if (areaVo.getEndTime().before(now)) {
                    throw new ServiceException("封网时间不能早于当前时间");
                }
            }

            // 处理最早开始时间
            if (earliestStartTime == null || areaVo.getStartTime().before(earliestStartTime)) {
                earliestStartTime = areaVo.getStartTime();
            }

            // 处理最晚结束时间
            if (latestEndTime == null || areaVo.getEndTime().after(latestEndTime)) {
                latestEndTime = areaVo.getEndTime();
            }
            NetworkFreezeArea networkFreezeArea = new NetworkFreezeArea();
            networkFreezeArea.setFreezeId(networkFreezeInfo.getId());
            networkFreezeArea.setAreaId(areaVo.getAreaId());
            networkFreezeArea.setAreaName(areaVo.getAreaName());
            networkFreezeArea.setAreaNameEn(areaVo.getAreaNameEn());
            networkFreezeArea.setStartTime(areaVo.getStartTime());
            networkFreezeArea.setEndTime(areaVo.getEndTime());
            networkFreezeAreaMapper.insert(networkFreezeArea);
        }


        // 创建并设置关联项
        NetworkFreezeItem networkFreezeItem = BeanUtil.copyProperties(bo, NetworkFreezeItem.class, "id");
        networkFreezeItem.setFreezeId(networkFreezeInfo.getId());
        networkFreezeItem.setStage(bo.getStage());
        networkFreezeItem.setFreezeStartTime(earliestStartTime);
        networkFreezeItem.setFreezeEndTime(latestEndTime);

        if (!NetworkFreezeStageEnum.DRAFT.equals(bo.getStage()) && StringUtils.isBlank(networkFreezeItem.getFreezeCode())) {
            networkFreezeItem.setFreezeCode(generateFreezeCode());
        }

        // 如果是已提交状态，调用流程引擎
        if (NetworkFreezeStageEnum.SUBMITTED.equals(bo.getStage())) {
            try {
                // 处理流程并更新信息
                String instanceId = processWorkflow(networkFreezeInfo, networkFreezeItem, user, records);
                networkFreezeItem.setInstanceId(instanceId);
            } catch (Exception e) {
                throw new ServiceException("调用流程引擎失败: " + e.getMessage());
            }
        }

        int itemResult = networkFreezeItemMapper.insert(networkFreezeItem);
        if (itemResult <= 0) {
            throw new ServiceException("保存封网申请项信息失败");
        }

        if (!CollUtil.isEmpty(records)) {
            // 保存处理记录
            saveProcessRecords(networkFreezeItem, records);
            records.parallelStream()
                .filter(record -> ProcessorStatusEnum.PENDING.equals(record.getProcessorStatus()))
                .forEach(record -> sendEmailAsync(record.getId(), record.getFreezeId(), record.getProcessor().getUserId()));
        }

        return networkFreezeInfo.getId();
    }

    private void checkPeriodAndArea(List<PeriodAndAreaVo> periodAndArea) {
        for (PeriodAndAreaVo vo : periodAndArea) {
            // 第一步：先查询已发布或已提交状态的NetworkFreezeItem
            List<NetworkFreezeItem> items = networkFreezeItemMapper.selectList(
                new LambdaQueryWrapper<NetworkFreezeItem>()
                    .in(NetworkFreezeItem::getStage, List.of(NetworkFreezeStageEnum.PUBLISHED, NetworkFreezeStageEnum.SUBMITTED))
            );

            if (CollUtil.isEmpty(items)) {
                continue; // 没有已发布或已提交的记录，直接继续
            }

            // 获取这些记录的freezeId列表
            List<Long> freezeIds = items.stream().map(NetworkFreezeItem::getFreezeId).toList();

            // 第二步：查询可能存在时间重叠的NetworkFreezeArea
            List<NetworkFreezeArea> potentialConflicts = networkFreezeAreaMapper.selectList(
                new LambdaQueryWrapper<NetworkFreezeArea>()
                    // 只查询已获取的freezeId
                    .in(NetworkFreezeArea::getFreezeId, freezeIds)
                    // 检查时间是否重叠
                    .and(wrapper -> wrapper
                        .between(NetworkFreezeArea::getStartTime, vo.getStartTime(), vo.getEndTime())
                        .or().between(NetworkFreezeArea::getEndTime, vo.getStartTime(), vo.getEndTime())
                        .or(w -> w.le(NetworkFreezeArea::getStartTime, vo.getStartTime())
                            .ge(NetworkFreezeArea::getEndTime, vo.getEndTime()))
                    )
            );

            // 在Java端检查区域ID是否有重叠
            for (NetworkFreezeArea area : potentialConflicts) {
                if (CollUtil.containsAny(vo.getAreaId(), area.getAreaId())) {
                    NetworkFreezeItem item = networkFreezeItemMapper.selectOne(
                        new LambdaQueryWrapper<NetworkFreezeItem>()
                            .eq(NetworkFreezeItem::getFreezeId, area.getFreezeId())
                    );
                    String conflictCode = item != null ? item.getFreezeCode() : "";

                    throw new ServiceException(MessageUtils.message("network.freeze.area.conflict",
                        vo.getAreaName(),
                        DateUtil.formatDateTime(vo.getStartTime()),
                        DateUtil.formatDateTime(vo.getEndTime()),
                        conflictCode), 600);
                }
            }
        }
    }

    /**
     * 处理工作流程
     *
     * @param info    封网信息
     * @param item    封网项
     * @param user    当前用户
     * @param records 处理记录列表
     * @return 流程实例ID
     */
    private String processWorkflow(NetworkFreezeInfo info, NetworkFreezeItem item, LoginUser user, List<NetworkFreezeRecord> records) {
        try {
            // 构建变量
            TreeMap<String, Object> variables = new TreeMap<>();
            //部门负责人
            QueryWrapper<SysUser> wrapper = Wrappers.query();
            wrapper.eq("u.del_flag", SystemConstants.NORMAL)
                .eq("r.role_id", 1933022110981050369L);
            Page<SysUserVo> page = sysUserMapper.selectAllocatedList(Page.of(1, 100), wrapper);
            if (CollUtil.isNotEmpty(page.getRecords())) {
                String s = page.getRecords().stream().map(SysUserVo::getUserId).map(String::valueOf).collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(s)) {
                    variables.put("deptHead", s);
                } else {
                    throw new ServiceException("未配置PMO DeptHead人员");
                }
            }

            // 1、获取下一个处理节点信息
            FlowTaskNextListResp flowTaskNextListResp = getNextTaskList(user, variables);

            // 创建处理记录和节点列表
            ArrayList<String> processorIdList = new ArrayList<>();
            ArrayList<String> processorNameList = new ArrayList<>();

            // 创建启动记录
            NetworkFreezeRecord startRecord = createStartRecord(info, item, user);
            records.add(startRecord);

            // 处理下一节点
            List<FlowInstanceStartResp.FlowInstanceNode> nodeList = processNextNodes(
                flowTaskNextListResp, info, item, user, records, processorIdList, processorNameList, startRecord);

            // 启动流程
            String instanceId = startFlowInstance(info, item, user, nodeList, variables);

            // 更新封网项信息
            updateItemWithProcessors(item, instanceId, processorIdList, processorNameList, variables);

            return instanceId;
        } catch (Exception e) {
            throw new ServiceException("调用流程引擎失败: " + e.getMessage());
        }
    }

    /**
     * 创建启动记录
     */
    private NetworkFreezeRecord createStartRecord(NetworkFreezeInfo info, NetworkFreezeItem item, LoginUser user) {
        NetworkFreezeRecord startRecord = new NetworkFreezeRecord();
        startRecord.setFreezeId(info.getId());
        startRecord.setFreezeCode(item.getFreezeCode());
        startRecord.setTitle(info.getTitle());
        startRecord.setProcessorStatus(ProcessorStatusEnum.STARTED);
        UserVo userVo = BeanUtil.copyProperties(user.getUserVo(), UserVo.class);
        startRecord.setProcessor(userVo);
        startRecord.setStage(NetworkFreezeStageEnum.SUBMITTED);
        startRecord.setProcessorTime(new Date());
        startRecord.setLevel(item.getLevel());
        startRecord.setProcessOrder(1);
        startRecord.setNodeName("发起人");
        return startRecord;
    }

    /**
     * 处理下一节点
     */
    private List<FlowInstanceStartResp.FlowInstanceNode> processNextNodes(
        FlowTaskNextListResp flowTaskNextListResp, NetworkFreezeInfo info,
        NetworkFreezeItem item, LoginUser user, List<NetworkFreezeRecord> records,
        List<String> processorIdList, List<String> processorNameList,
        NetworkFreezeRecord startRecord) {

        List<FlowInstanceStartResp.FlowInstanceNode> nodeList = new ArrayList<>();

        if (flowTaskNextListResp.getData() == null) {
            return nodeList;
        }

        for (FlowTaskNextListResp.FlowTaskNext taskNext : flowTaskNextListResp.getData()) {
            FlowInstanceStartResp.FlowInstanceNode node = new FlowInstanceStartResp.FlowInstanceNode();
            node.setNodeId(taskNext.getId());
            node.setNodeName(taskNext.getName());

            // 设置处理人列表
            List<FlowInstanceStartResp.FlowInstanceHandler> handlerList = new ArrayList<>();

            if (taskNext.getCandidateList() != null) {
                for (FlowTaskNextListResp.Candidate handler : taskNext.getCandidateList()) {
                    handlerList.add(createInstanceHandler(handler));

                    // 添加处理人ID和名称
                    processorIdList.add(handler.getAssigneeId());
                    processorNameList.add(handler.getAssigneeName());

                    // 添加待处理记录
                    NetworkFreezeRecord record = createPendingRecord(info, item, handler, taskNext);
                    records.add(record);
                }
            }

            node.setHandlerList(handlerList);
            nodeList.add(node);
        }

        return nodeList;
    }

    /**
     * 创建待处理记录
     */
    private NetworkFreezeRecord createPendingRecord(NetworkFreezeInfo info, NetworkFreezeItem item,
                                                    FlowTaskNextListResp.Candidate handler,
                                                    FlowTaskNextListResp.FlowTaskNext taskNext) {
        SysUser sysUser = sysUserMapper.selectById(handler.getAssigneeId());
        NetworkFreezeRecord pendingRecord = new NetworkFreezeRecord();
        pendingRecord.setFreezeId(info.getId());
        pendingRecord.setFreezeCode(item.getFreezeCode());
        pendingRecord.setTitle(info.getTitle());
        pendingRecord.setProcessorStatus(ProcessorStatusEnum.PENDING);
        pendingRecord.setProcessor(sysUser.getUserVo());
        pendingRecord.setStage(NetworkFreezeStageEnum.SUBMITTED);
        pendingRecord.setLevel(item.getLevel());
        pendingRecord.setProcessOrder(2);

        // 会签类型在任务创建后通过populateRecordWithTaskInfo方法填充
        return pendingRecord;
    }

    /**
     * 启动流程实例
     */
    private String startFlowInstance(NetworkFreezeInfo info, NetworkFreezeItem item,
                                     LoginUser user, List<FlowInstanceStartResp.FlowInstanceNode> nodeList,
                                     TreeMap<String, Object> variables) {

        FlowInstanceStartReq req = createFlowInstanceStartReq(info, item, user);
        req.setNodeList(nodeList);
        req.setVariables(variables);

        // 调用流程启动并验证返回结果
        FlowInstanceBaseResp flowInstanceBaseResp = checkResponse(smartFlowUtils.startFlow(req));
        return flowInstanceBaseResp.getData();
    }

    /**
     * 创建流程实例启动请求
     */
    private FlowInstanceStartReq createFlowInstanceStartReq(NetworkFreezeInfo info, NetworkFreezeItem item, LoginUser user) {
        FlowInstanceStartReq req = new FlowInstanceStartReq();
        req.setName(info.getTitle());
        req.setBusinessId(item.getFreezeCode());
        req.setFormKey(cecSmartFlowConfig.getParam().getFormKey());
        req.setUserId(String.valueOf(user.getUserId()));
        req.setUserName(user.getStaffName());
        req.setUserOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setUserOrgName(cecSmartFlowConfig.getParam().getDeptName());
        return req;
    }

    /**
     * 获取下一个处理节点列表
     *
     * @param user      当前用户
     * @param variables
     * @return 节点列表响应
     */
    private FlowTaskNextListResp getNextTaskList(LoginUser user, TreeMap<String, Object> variables) {
        FlowTaskNextListReq req = new FlowTaskNextListReq();
        req.setAssigneeId(String.valueOf(user.getUserId()));
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setFormKey(cecSmartFlowConfig.getParam().getFormKey());
        req.setVariables(variables);
        return checkResponse(smartFlowUtils.nextList(req));
    }

    /**
     * 创建流程实例处理人
     */
    private FlowInstanceStartResp.FlowInstanceHandler createInstanceHandler(FlowTaskNextListResp.Candidate candidate) {
        FlowInstanceStartResp.FlowInstanceHandler handler = new FlowInstanceStartResp.FlowInstanceHandler();
        handler.setAssigneeId(candidate.getAssigneeId());
        handler.setAssigneeName(candidate.getAssigneeName());
        handler.setAssigneeOrgId(candidate.getAssigneeOrgId());
        handler.setAssigneeOrgName(candidate.getAssigneeOrgName());
        return handler;
    }

    /**
     * 更新封网项处理人信息
     */
    private void updateItemWithProcessors(NetworkFreezeItem item, String instanceId,
                                          List<String> processorIdList, List<String> processorNameList,
                                          TreeMap<String, Object> variables) {
        item.setInstanceId(instanceId);
        List<UserVo> list = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserId, processorIdList))
            .stream().map(SysUser::getUserVo).toList();

        item.setProcessorList(list);
        item.setVariables(JsonUtils.toJsonString(variables));
    }

    /**
     * 检查API响应状态
     *
     * @param response API响应对象
     * @param <T>      响应类型
     * @return 校验通过的响应对象
     */
    private <T> T checkResponse(T response) {
        if (response == null) {
            throw new ServiceException("调用流程引擎失败，返回结果为空");
        }

        try {
            // 通过反射获取code和success属性值
            Integer code = (Integer) response.getClass().getMethod("getCode").invoke(response);
            Boolean success = (Boolean) response.getClass().getMethod("isSuccess").invoke(response);

            if (code == null || !code.equals(SUCCESS_CODE) || !success) {
                // 尝试获取错误消息
                String message = getErrorMessage(response);
                throw new ServiceException(message);
            }

            return response;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("调用流程引擎失败，无法验证返回结果: " + e.getMessage());
        }
    }

    /**
     * 获取错误消息
     */
    private <T> String getErrorMessage(T response) {
        try {
            return (String) response.getClass().getMethod("getMessage").invoke(response);
        } catch (Exception ex) {
            return "调用流程引擎失败，状态异常";
        }
    }

    /**
     * 修改封网申请
     *
     * @param bo 封网申请
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(NetworkFreezeInfoBo bo) {
        NetworkFreezeItem item = networkFreezeItemMapper.selectOne(new LambdaQueryWrapper<NetworkFreezeItem>().eq(NetworkFreezeItem::getFreezeId, bo.getId()));
        if (!item.getStage().equals(NetworkFreezeStageEnum.DRAFT)) {
            throw new ServiceException("非草稿状态不能修改封网申请");
        }

        NetworkFreezeInfo info = networkFreezeInfoMapper.selectById(bo.getId());
        if (info == null) {
            throw new ServiceException("未找到封网申请基本信息");
        }

        BeanUtil.copyProperties(bo, info, "periodAndArea", "notificationEmailTo", "notificationEmailCc");
        info.setNotificationEmailTo(JsonUtils.toJsonString(bo.getNotificationEmailTo()));
        info.setNotificationEmailCc(JsonUtils.toJsonString(bo.getNotificationEmailCc()));

        LoginUser user = LoginHelper.getLoginUser();
        if (!NetworkFreezeStageEnum.DRAFT.equals(bo.getStage())) {
            checkPeriodAndArea(bo.getPeriodAndArea());
        }
        // 更新信息
        NetworkFreezeStageEnum originalStage = item.getStage();
        item.setStage(bo.getStage());
        item.setTitle(bo.getTitle());
        item.setLevel(bo.getLevel());

        // 如果从草稿状态转为非草稿状态，生成编码
        if (StringUtils.isBlank(item.getFreezeCode()) && !bo.getStage().equals(NetworkFreezeStageEnum.DRAFT)) {
            item.setFreezeCode(generateFreezeCode());
        }

        ArrayList<NetworkFreezeRecord> records = new ArrayList<>();

        // 如果从草稿状态转为已提交状态，启动流程
        if (NetworkFreezeStageEnum.DRAFT.equals(originalStage) && NetworkFreezeStageEnum.SUBMITTED.equals(bo.getStage())) {
            try {
                String instanceId = processWorkflow(info, item, user, records);
                item.setInstanceId(instanceId);
                info.setRequester(BeanUtil.copyProperties(user.getUserVo(), UserVo.class));
            } catch (Exception e) {
                throw new ServiceException("调用流程引擎失败: " + e.getMessage());
            }
        }

        // 更新数据库
        boolean infoResult = networkFreezeInfoMapper.updateById(info) > 0;
        if (!infoResult) {
            throw new ServiceException("更新封网申请基本信息失败");
        }

        networkFreezeAreaMapper.delete(new LambdaQueryWrapper<NetworkFreezeArea>().eq(NetworkFreezeArea::getFreezeId, info.getId()));

        Date earliestStartTime = null;
        Date latestEndTime = null;
        Date now = new Date();

        for (PeriodAndAreaVo areaVo : bo.getPeriodAndArea()) {

            if (!NetworkFreezeStageEnum.DRAFT.equals(bo.getStage())) {
                if (areaVo.getEndTime().before(now)) {
                    throw new ServiceException("封网时间不能早于当前时间");
                }
            }

            if (earliestStartTime == null || areaVo.getStartTime().before(earliestStartTime)) {
                earliestStartTime = areaVo.getStartTime();
            }
            if (latestEndTime == null || areaVo.getEndTime().after(latestEndTime)) {
                latestEndTime = areaVo.getEndTime();
            }
            NetworkFreezeArea networkFreezeArea = new NetworkFreezeArea();
            networkFreezeArea.setFreezeId(info.getId());
            networkFreezeArea.setAreaId(areaVo.getAreaId());
            networkFreezeArea.setAreaName(areaVo.getAreaName());
            networkFreezeArea.setAreaNameEn(areaVo.getAreaNameEn());
            networkFreezeArea.setStartTime(areaVo.getStartTime());
            networkFreezeArea.setEndTime(areaVo.getEndTime());
            networkFreezeAreaMapper.insert(networkFreezeArea);
        }
        item.setFreezeStartTime(earliestStartTime);
        item.setFreezeEndTime(latestEndTime);

        boolean itemResult = networkFreezeItemMapper.updateById(item) > 0;
        if (!itemResult) {
            throw new ServiceException("更新封网申请项信息失败");
        }

        // 保存处理记录
        if (!CollUtil.isEmpty(records)) {
            saveProcessRecords(item, records);
            records.parallelStream()
                .filter(record -> ProcessorStatusEnum.PENDING.equals(record.getProcessorStatus()))
                .forEach(record -> sendEmailAsync(record.getId(), record.getFreezeId(), record.getProcessor().getUserId()));
        }

        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(NetworkFreezeInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除封网申请信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return networkFreezeInfoMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取当前时间及未来7天内存在的已发布状态封网申请列表
     *
     * @param date 当前日期
     * @return 封网申请列表
     */
    @Override
    public List<NetworkFreezeInfoVo> getListNetworkFreezeInfoVo(Date date) {
        // 计算7天后的日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        Date endDate = calendar.getTime();

        // 先查询指定时间范围内的区域信息
        LambdaQueryWrapper<NetworkFreezeArea> areaQueryWrapper = new LambdaQueryWrapper<>();
        areaQueryWrapper.and(wrapper ->
            // 开始时间在查询范围内
            wrapper.between(NetworkFreezeArea::getStartTime, date, endDate)
                // 或者结束时间在查询范围内
                .or().between(NetworkFreezeArea::getEndTime, date, endDate)
                // 或者开始时间早于当前时间，结束时间晚于未来7天（即覆盖整个范围）
                .or(w -> w.le(NetworkFreezeArea::getStartTime, date).ge(NetworkFreezeArea::getEndTime, endDate))
        );

        // 查询符合条件的区域信息
        List<NetworkFreezeArea> areaList = networkFreezeAreaMapper.selectList(areaQueryWrapper);
        if (CollectionUtils.isEmpty(areaList)) {
            return Collections.emptyList();
        }

        // 获取所有相关的封网ID
        List<Long> areaFreezeIds = areaList.stream()
            .map(NetworkFreezeArea::getFreezeId)
            .distinct()
            .collect(Collectors.toList());

        // 按封网ID分组
        Map<Long, List<NetworkFreezeArea>> areaMap = areaList.stream()
            .collect(Collectors.groupingBy(NetworkFreezeArea::getFreezeId));

        // 然后查询已发布状态的封网申请列表，并且限制在相关的封网ID中
        LambdaQueryWrapper<NetworkFreezeItem> itemQueryWrapper = new LambdaQueryWrapper<>();
        itemQueryWrapper.eq(NetworkFreezeItem::getStage, NetworkFreezeStageEnum.PUBLISHED)
            .in(NetworkFreezeItem::getFreezeId, areaFreezeIds);

        // 查询满足条件的NetworkFreezeItem列表
        List<NetworkFreezeItem> itemList = networkFreezeItemMapper.selectList(itemQueryWrapper);
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }

        // 获取已发布状态的封网ID
        List<Long> freezeIds = itemList.stream()
            .map(NetworkFreezeItem::getFreezeId)
            .collect(Collectors.toList());

        // 查询这些ID对应的基本信息
        LambdaQueryWrapper<NetworkFreezeInfo> infoQueryWrapper = new LambdaQueryWrapper<>();
        infoQueryWrapper.in(NetworkFreezeInfo::getId, freezeIds);
        List<NetworkFreezeInfo> infoList = networkFreezeInfoMapper.selectList(infoQueryWrapper);

        // 转换为VO对象并设置关联信息
        List<NetworkFreezeInfoVo> resultList = new ArrayList<>();
        for (NetworkFreezeInfo info : infoList) {
            NetworkFreezeInfoVo vo = BeanUtil.copyProperties(info, NetworkFreezeInfoVo.class, "periodAndArea", "notificationEmailTo", "notificationEmailCc");
            vo.setPeriodAndArea(areaMap.get(info.getId()));

            for (NetworkFreezeItem item : itemList) {
                if (item.getFreezeId().equals(info.getId())) {
                    vo.setFreezeCode(item.getFreezeCode());
                    vo.setStage(item.getStage());
                    break;
                }
            }
            resultList.add(vo);
        }

        // 按开始时间升序排序，优先显示开始时间早的记录
        resultList.sort(Comparator.comparing(vo -> {
            List<NetworkFreezeArea> areas = vo.getPeriodAndArea();
            if (CollectionUtils.isEmpty(areas)) {
                return new Date(Long.MAX_VALUE);
            }
            return areas.stream()
                .map(NetworkFreezeArea::getStartTime)
                .min(Date::compareTo)
                .orElse(new Date(Long.MAX_VALUE));
        }));

        return resultList;
    }

    /**
     * 设置变更编号
     * 格式：ITS-NF-YYYYMM-XXX
     * YYYYMM:年月，例如202412
     * XXX:每月从001开始递增的序列号
     */
    private String generateFreezeCode() {
        // 获取当前年月作为前缀
        String yearMonth = DateUtil.format(DateUtil.date(), "yyyyMM");
        // Redis中的key，用于存储序列号
        String redisKey = "ITS-NF-" + yearMonth;
        // 使用SequenceUtils生成序列号，每月重置，从1开始，步长为1
        long sequence = SequenceUtils.nextId(redisKey, Duration.ofDays(31), 1L, 1L);
        // 格式化序列号为3位数，例如：001, 010, 100
        String sequenceStr = String.format("%03d", sequence);
        // 设置完整的freezeCode
        return "ITS-NF-" + yearMonth + "-" + sequenceStr;
    }

    @Override
    public TableDataInfo<NetworkFreezeRecordVo> queryPageList(NetworkFreezeRecordBo bo, PageQuery pageQuery) {
        LoginUser user = LoginHelper.getLoginUser();

        // 确定要返回的字段
        List<String> code = bo.getSelectFiledNameList() != null && !bo.getSelectFiledNameList().isEmpty()
            ? bo.getSelectFiledNameList()
            : List.of("freezeCode", "title", "processorStatus", "processorList", "processorTime", "level");

        List<Long> freezeIds = null;
        boolean hasAreaOrTimeFilter = StringUtils.isNotBlank(bo.getAreaId())
            || StringUtils.isNotBlank(bo.getFreezeTimeStart())
            || StringUtils.isNotBlank(bo.getFreezeTimeEnd());

        if (hasAreaOrTimeFilter) {
            LambdaQueryWrapper<NetworkFreezeArea> lqw = Wrappers.lambdaQuery();
            lqw.like(StringUtils.isNotBlank(bo.getAreaId()), NetworkFreezeArea::getAreaId, bo.getAreaId());

            // 修复时间范围重叠查询逻辑
            if (StringUtils.isNotBlank(bo.getFreezeTimeStart()) || StringUtils.isNotBlank(bo.getFreezeTimeEnd())) {
                lqw.and(wrapper -> {
                    // 情况1：数据的开始时间在查询范围内
                    if (StringUtils.isNotBlank(bo.getFreezeTimeStart()) && StringUtils.isNotBlank(bo.getFreezeTimeEnd())) {
                        wrapper.between(NetworkFreezeArea::getStartTime, bo.getFreezeTimeStart(), bo.getFreezeTimeEnd())
                            // 情况2：数据的结束时间在查询范围内
                            .or().between(NetworkFreezeArea::getEndTime, bo.getFreezeTimeStart(), bo.getFreezeTimeEnd())
                            // 情况3：数据的时间范围完全包含查询范围（开始时间早于查询开始，结束时间晚于查询结束）
                            .or(w -> w.le(NetworkFreezeArea::getStartTime, bo.getFreezeTimeStart()).ge(NetworkFreezeArea::getEndTime, bo.getFreezeTimeEnd()));
                    } else if (StringUtils.isNotBlank(bo.getFreezeTimeStart())) {
                        // 只有开始时间：数据的结束时间 >= 查询开始时间
                        wrapper.ge(NetworkFreezeArea::getEndTime, bo.getFreezeTimeStart());
                    } else if (StringUtils.isNotBlank(bo.getFreezeTimeEnd())) {
                        // 只有结束时间：数据的开始时间 <= 查询结束时间
                        wrapper.le(NetworkFreezeArea::getStartTime, bo.getFreezeTimeEnd());
                    }
                });
            }

            List<NetworkFreezeArea> networkFreezeAreas = networkFreezeAreaMapper.selectList(lqw);
            freezeIds = networkFreezeAreas.stream().map(NetworkFreezeArea::getFreezeId).distinct().toList();

            if (CollUtil.isEmpty(freezeIds)) {
                return TableDataInfo.build(new Page<>());
            }
        }

        // 构建查询条件
        LambdaQueryWrapper<NetworkFreezeRecord> lqw = Wrappers.lambdaQuery();
        // lqw.orderByDesc(NetworkFreezeRecord::getId);
        // lqw.last("ORDER BY processor_status=4 DESC, id DESC");
        // 获取排序字段和排序方式
        String orderByColumn = pageQuery.getOrderByColumn();
        String isAsc = pageQuery.getIsAsc();

        // 清空PageQuery中的排序设置，防止它自动添加排序条件
        pageQuery.setOrderByColumn(null);
        pageQuery.setIsAsc(null);

        // 构建自定义排序SQL
        StringBuilder orderBySql = new StringBuilder("ORDER BY processor_status=4 DESC");
        if (StringUtils.isNotBlank(orderByColumn)) {
            String columnName = StringUtils.toUnderScoreCase(orderByColumn);
            String direction = "asc".equalsIgnoreCase(isAsc) ? "ASC" : "DESC";
            orderBySql.append(", ").append(columnName).append(" ").append(direction);
        }
        orderBySql.append(", id DESC");

        // 设置排序SQL
        lqw.last(orderBySql.toString());

        //  只查询当前登录用户的处理记录
        lqw.apply("JSON_EXTRACT(processor, '$.userId') = {0}", user.getUserId());
        lqw.in(CollUtil.isNotEmpty(freezeIds), NetworkFreezeRecord::getFreezeId, freezeIds);
        lqw.like(StringUtils.isNotBlank(bo.getFreezeCode()), NetworkFreezeRecord::getFreezeCode, bo.getFreezeCode());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), NetworkFreezeRecord::getTitle, bo.getTitle());
        lqw.eq(ObjectUtils.isNotNull(bo.getProcessorStatus()), NetworkFreezeRecord::getProcessorStatus, bo.getProcessorStatus());
        lqw.in(CollUtil.isNotEmpty(bo.getProcessorStatusList()), NetworkFreezeRecord::getProcessorStatus, bo.getProcessorStatusList());
        lqw.in(CollUtil.isNotEmpty(bo.getLevelList()), NetworkFreezeRecord::getLevel, bo.getLevelList());
        lqw.apply(StringUtils.isNotBlank(bo.getProcessorId()), "JSON_EXTRACT(processor, '$.userId') = {0}", bo.getProcessorId());
        lqw.apply(StringUtils.isNotBlank(bo.getProcessorName()), "JSON_EXTRACT(processor, '$.staffName') LIKE CONCAT('%', {0}, '%')", bo.getProcessorName());
        lqw.ge(StringUtils.isNotBlank(bo.getProcessorTimeStart()), NetworkFreezeRecord::getProcessorTime, bo.getProcessorTimeStart());
        lqw.le(StringUtils.isNotBlank(bo.getProcessorTimeEnd()), NetworkFreezeRecord::getProcessorTime, bo.getProcessorTimeEnd());
        lqw.in(CollUtil.isNotEmpty(bo.getStageList()), NetworkFreezeRecord::getStage, bo.getStageList());

        Page<NetworkFreezeRecordVo> result = networkFreezeRecordMapper.selectVoPage(pageQuery.build(), lqw);
        List<NetworkFreezeRecordVo> records = result.getRecords();

        if (records != null && !records.isEmpty()) {

            // 判断是否需要查询额外字段
            boolean needExtraFields1 = code.contains("freezeApps") || code.contains("periodAndArea");

            // 如果需要额外字段，批量查询相关信息
            Map<Long, NetworkFreezeInfo> infoMap = needExtraFields1
                ? networkFreezeInfoMapper.selectBatchIds(
                    records.stream().map(NetworkFreezeRecordVo::getFreezeId).toList())
                .stream().collect(java.util.stream.Collectors.toMap(
                    NetworkFreezeInfo::getId,
                    info -> info,
                    (k1, k2) -> k1))
                : Collections.emptyMap();

            boolean needExtraFields2 = code.contains("periodAndArea");
            Map<Long, List<NetworkFreezeArea>> areaMap = needExtraFields2
                ? networkFreezeAreaMapper.selectList(new LambdaQueryWrapper<NetworkFreezeArea>()
                    .in(NetworkFreezeArea::getFreezeId, records.stream().map(NetworkFreezeRecordVo::getFreezeId).toList()))
                .stream().collect(java.util.stream.Collectors.groupingBy(NetworkFreezeArea::getFreezeId))
                : Collections.emptyMap();

            // 过滤记录中的字段
            result.setRecords(records.stream()
                .map(item -> {
                    // 创建新对象
                    NetworkFreezeRecordVo filteredItem = new NetworkFreezeRecordVo();

                    // 总是保留ID字段
                    filteredItem.setId(item.getId());
                    filteredItem.setFreezeId(item.getFreezeId());
                    filteredItem.setFreezeItemId(item.getFreezeItemId());

                    // 有选择地设置基本字段
                    if (code.contains("freezeCode")) filteredItem.setFreezeCode(item.getFreezeCode());
                    if (code.contains("title")) filteredItem.setTitle(item.getTitle());
                    if (code.contains("processorStatus")) filteredItem.setProcessorStatus(item.getProcessorStatus());
                    if (code.contains("processorList")) filteredItem.setProcessor(item.getProcessor());
                    if (code.contains("processorTime")) filteredItem.setProcessorTime(item.getProcessorTime());
                    if (code.contains("level")) filteredItem.setLevel(item.getLevel());
                    if (code.contains("stage")) filteredItem.setStage(item.getStage());

                    // 处理需要额外查询的字段
                    Optional.ofNullable(infoMap.get(item.getFreezeId())).ifPresent(info -> {
                        if (code.contains("periodAndArea")) {
                            filteredItem.setPeriodAndArea(areaMap.getOrDefault(info.getId(), Collections.emptyList()));
                        }
                        if (code.contains("freezeApps") && StringUtils.isNotBlank(info.getFreezeApps())) {
                            filteredItem.setFreezeApps(info.getFreezeApps());
                        }
                    });

                    return filteredItem;
                }).toList());
        }

        return TableDataInfo.build(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(NetworkFreezeRecordBo bo) {
        LoginUser user = LoginHelper.getLoginUser();
        LambdaQueryWrapper<NetworkFreezeRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(NetworkFreezeRecord::getId, bo.getId());
        NetworkFreezeRecord record = networkFreezeRecordMapper.selectById(bo.getId());
        if (record == null) {
            throw new ServiceException("找不到对应的处理记录");
        }

        if (!record.getProcessor().getUserId().equals(user.getUserId())) {
            throw new ServiceException("无权限审批");
        }

        String opinion = bo.getOpinion();
        ProcessorStatusEnum processorStatus = bo.getProcessorStatus();
        NetworkFreezeItem item = networkFreezeItemMapper.selectById(record.getFreezeItemId());
        if (item == null) {
            throw new ServiceException("找不到对应的封网项");
        }

        // 获取当前执行任务
        FlowTaskCurrentResp currentTask = getCurrentTask(item, String.valueOf(user.getUserId()));

        // 根据会签类型处理审批逻辑
        Integer signType = record.getCountersignType();

        if (signType == 0) {
            // 会签逻辑，所有人都要通过才能通过，任一人拒绝则整个流程拒绝
            return handleCounterSignLogic(record, processorStatus, opinion, item, currentTask);
        } else {
            // 或签逻辑，只要有一人通过即可，所有人拒绝才算拒绝
            return handleOrSignLogic(record, processorStatus, opinion, item, currentTask);
        }
    }

    /**
     * 获取当前任务
     */
    private FlowTaskCurrentResp getCurrentTask(NetworkFreezeItem item, String staffId) {
        FlowTaskCurrentReq req = new FlowTaskCurrentReq();
        req.setId(Long.valueOf(item.getInstanceId()));
        req.setAssigneeId(staffId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        return checkResponse(smartFlowUtils.getFlowTaskCurrent(req));
    }

    /**
     * 处理拒绝
     */
    private void processRejection(NetworkFreezeItem item, NetworkFreezeRecord record,
                                  FlowTaskCurrentResp currentTask, String opinion) {
        // 更新状态
        item.setStage(NetworkFreezeStageEnum.REJECTED);
        record.setProcessorStatus(ProcessorStatusEnum.REJECTED);
        record.setProcessorTime(new Date());
        record.setOpinion(opinion);
        record.setStage(NetworkFreezeStageEnum.REJECTED);

        // 调用拒绝
        FlowTaskRejectReq rejectReq = buildFlowTaskRejectReq(currentTask, opinion);
        checkResponse(smartFlowUtils.reject(rejectReq));

        // 保存更新
        networkFreezeRecordMapper.updateById(record);
        networkFreezeItemMapper.updateById(item);
    }

    private FlowTaskRejectReq buildFlowTaskRejectReq(FlowTaskCurrentResp current, String opinion) {
        FlowTaskCurrentResp.FlowTaskCurrent data = current.getData();
        if (data == null) {
            throw new ServiceException("无当前任务");
        }
        FlowTaskRejectReq req = new FlowTaskRejectReq();
        req.setAssigneeId(data.getAssigneeId());
        req.setAssigneeOrgId(data.getAssigneeOrgId());
        req.setId(data.getId());
        // req.setComment(opinion);
        return req;
    }

    private FlowTaskApproveReq buildFlowTaskApproveReq(FlowTaskCurrentResp current, NetworkFreezeItem item, String opinion) {
        FlowTaskCurrentResp.FlowTaskCurrent data = current.getData();
        if (data == null) {
            throw new ServiceException("无当前任务");
        }
        FlowTaskApproveReq req = new FlowTaskApproveReq();
        req.setName(data.getName());
        req.setAssigneeId(data.getAssigneeId());
        req.setAssigneeOrgId(data.getAssigneeOrgId());
        req.setId(data.getId());
        req.setNodeList(new ArrayList<>());
        req.setNodeId(data.getNodeId());
        req.setNodeName(data.getNodeName());
        // req.setComment(opinion);

        TreeMap<String, Object> variables = item.getVariables() != null
            ? JSONUtil.toBean(item.getVariables(), TreeMap.class)
            : new TreeMap<>();
        req.setVariables(variables);

        return req;
    }

    /**
     * 填充记录的任务信息
     */
    private void populateRecordWithTaskInfo(NetworkFreezeItem item, NetworkFreezeRecord record) {
        try {
            String userId = String.valueOf(record.getProcessor().getUserId());
            // 获取当前执行任务
            FlowTaskCurrentReq req = new FlowTaskCurrentReq();
            req.setId(Long.valueOf(item.getInstanceId()));
            req.setAssigneeId(userId);
            req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
            FlowTaskCurrentResp currentTask = checkResponse(smartFlowUtils.getFlowTaskCurrent(req));

            if (currentTask != null && currentTask.getData() != null) {
                record.setTaskId(String.valueOf(currentTask.getData().getId()));
                record.setTaskCurrent(JsonUtils.toJsonString(currentTask));

                // 获取下一节点列表
                TreeMap<String, Object> vars = JSONUtil.toBean(item.getVariables(), TreeMap.class);
                FlowTaskNextListReq nextListReq = new FlowTaskNextListReq();
                nextListReq.setAssigneeId(userId);
                nextListReq.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
                nextListReq.setFormKey(cecSmartFlowConfig.getParam().getFormKey());
                nextListReq.setId(Long.valueOf(record.getTaskId()));
                nextListReq.setVariables(vars);
                FlowTaskNextListResp nextListResp = checkResponse(smartFlowUtils.nextList(nextListReq));
                record.setNextList(JsonUtils.toJsonString(nextListResp));

                // 获取任务处理策略
                FlowTaskStrategyReq strategyReq = new FlowTaskStrategyReq();
                strategyReq.setAssigneeId(userId);
                strategyReq.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
                strategyReq.setFormKey(cecSmartFlowConfig.getParam().getFormKey());
                strategyReq.setId(record.getTaskId());
                FlowTaskStrategyResp strategyResp = checkResponse(smartFlowUtils.strategy(strategyReq));
                record.setStrategy(JsonUtils.toJsonString(strategyResp));
                record.setCountersignType(strategyResp.getData().getCountersignType());
                record.setNodeName(currentTask.getData().getNodeName());
            }
        } catch (Exception e) {
            throw new ServiceException("填充任务信息失败: " + e.getMessage());
        }
    }

    /**
     * 保存处理记录
     */
    private void saveProcessRecords(NetworkFreezeItem item, List<NetworkFreezeRecord> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        for (NetworkFreezeRecord record : records) {
            record.setFreezeItemId(item.getId());
            if (!record.getProcessorStatus().equals(ProcessorStatusEnum.STARTED)) {
                populateRecordWithTaskInfo(item, record);
            }
            networkFreezeRecordMapper.insert(record);
        }
    }

    /**
     * 处理会签逻辑
     * 会签需要所有审批人都通过，任一人拒绝则整个流程拒绝
     */
    private boolean handleCounterSignLogic(NetworkFreezeRecord record, ProcessorStatusEnum processorStatus,
                                           String opinion, NetworkFreezeItem item, FlowTaskCurrentResp currentTask) {

        // 统一设置当前记录的基本信息
        Date currentTime = new Date();
        record.setProcessorStatus(processorStatus);
        record.setProcessorTime(currentTime);
        record.setOpinion(opinion);

        // 处理拒绝状态 - 会签中任一人拒绝则整个流程拒绝
        if (ProcessorStatusEnum.REJECTED.equals(processorStatus)) {
            // 更新所有待处理记录为已拒绝
            updateAllPendingRecords(record, ProcessorStatusEnum.REJECTED, "");

            // 调用流程引擎拒绝
            FlowTaskRejectReq rejectReq = buildFlowTaskRejectReq(currentTask, opinion);
            checkResponse(smartFlowUtils.reject(rejectReq));

            // 批量更新：当前记录状态 + 项目状态和处理人列表
            networkFreezeRecordMapper.updateById(record);
            item.setStage(NetworkFreezeStageEnum.REJECTED);
            item.setProcessorList(List.of());
            networkFreezeItemMapper.updateById(item);
            return true;
        }

        // 处理通过状态 - 需要所有人都通过才能流转到下一阶段
        if (ProcessorStatusEnum.APPROVED.equals(processorStatus)) {
            // 调用流程引擎通过
            FlowTaskApproveReq approveReq = buildFlowTaskApproveReq(currentTask, item, opinion);
            checkResponse(smartFlowUtils.approve(approveReq));

            // 从处理人列表中移除当前审批人并更新记录
            removeCurrentProcessor(item, record);
            networkFreezeRecordMapper.updateById(record);

            // 检查是否所有审批人都已处理完成
            boolean allProcessed = !hasPendingRecords(record);
            if (allProcessed) {
                // 所有人都已审批通过，流转到已发布状态并发送邮件通知
                item.setStage(NetworkFreezeStageEnum.PUBLISHED);
                sendEmailAsync(item.getFreezeId());
            }

            networkFreezeItemMapper.updateById(item);
            return true;
        }

        // 其他状态（如PENDING等）只需更新当前记录
        networkFreezeRecordMapper.updateById(record);
        return true;
    }

    /**
     * 处理或签逻辑
     * 或签只需一人通过即可，所有人拒绝才算拒绝
     */
    private boolean handleOrSignLogic(NetworkFreezeRecord record, ProcessorStatusEnum processorStatus,
                                      String opinion, NetworkFreezeItem item, FlowTaskCurrentResp currentTask) {
        // 更新当前记录的状态
        record.setProcessorStatus(processorStatus);
        record.setProcessorTime(new Date());
        record.setOpinion(opinion);
        networkFreezeRecordMapper.updateById(record);

        // 处理通过状态
        if (ProcessorStatusEnum.APPROVED.equals(processorStatus)) {
            // 更新所有待处理记录为已通过
            updateAllPendingRecords(record, ProcessorStatusEnum.APPROVED, "或签已被处理");

            // 执行审批通过
            FlowTaskApproveReq approveReq = buildFlowTaskApproveReq(currentTask, item, opinion);
            checkResponse(smartFlowUtils.approve(approveReq));
            removeCurrentProcessor(item, record);

            // 更新阶段并移除处理人
            item.setStage(NetworkFreezeStageEnum.PUBLISHED);
            item.setProcessorList(List.of());
            networkFreezeItemMapper.updateById(item);
            sendEmailAsync(item.getFreezeId());
            return true;
        }

        // 处理拒绝状态 - 检查是否所有人都拒绝了
        if (ProcessorStatusEnum.REJECTED.equals(processorStatus)) {
            if (!hasPendingRecords(record)) {
                // 所有人都已拒绝，流转到拒绝阶段
                FlowTaskRejectReq rejectReq = buildFlowTaskRejectReq(currentTask, opinion);
                checkResponse(smartFlowUtils.reject(rejectReq));

                item.setStage(NetworkFreezeStageEnum.REJECTED);
                item.setProcessorList(List.of());
                networkFreezeItemMapper.updateById(item);
            }
        }

        return true;
    }

    /**
     * 检查是否有待处理记录
     */
    private boolean hasPendingRecords(NetworkFreezeRecord record) {
        LambdaQueryWrapper<NetworkFreezeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NetworkFreezeRecord::getFreezeItemId, record.getFreezeItemId())
            .eq(NetworkFreezeRecord::getStage, record.getStage())
            .eq(NetworkFreezeRecord::getProcessorStatus, ProcessorStatusEnum.PENDING);

        return networkFreezeRecordMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 更新所有待处理记录
     */
    private void updateAllPendingRecords(NetworkFreezeRecord currentRecord, ProcessorStatusEnum status, String opinion) {
        LambdaQueryWrapper<NetworkFreezeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NetworkFreezeRecord::getFreezeItemId, currentRecord.getFreezeItemId())
            .eq(NetworkFreezeRecord::getStage, currentRecord.getStage())
            .eq(NetworkFreezeRecord::getProcessorStatus, ProcessorStatusEnum.PENDING)
            .ne(NetworkFreezeRecord::getId, currentRecord.getId());

        List<NetworkFreezeRecord> pendingRecords = networkFreezeRecordMapper.selectList(queryWrapper);

        for (NetworkFreezeRecord record : pendingRecords) {
            record.setProcessorStatus(status);
            record.setOpinion(opinion);
            record.setProcessorTime(new Date());
            networkFreezeRecordMapper.updateById(record);
        }
    }

    /**
     * 异步发送变更状态邮件
     *
     * @param freezeInfoId 封网ID
     */
    private void sendEmailAsync(Long freezeInfoId) {
        CompletableFuture.runAsync(() -> {
            sendEmailWithRetry(freezeInfoId);
        });
    }

    /**
     * 发送变更状态邮件，支持重试
     *
     * @param freezeInfoId 封网ID
     */
    private void sendEmailWithRetry(Long freezeInfoId) {
        for (int attempt = 1; attempt <= EMAIL_MAX_RETRIES; attempt++) {
            try {
                // 检查封网信息是否存在
                NetworkFreezeInfo info = networkFreezeInfoMapper.selectById(freezeInfoId);
                if (info == null) {
                    if (attempt < EMAIL_MAX_RETRIES) {
                        log.warn("封网信息不存在，进行第{}次重试, freezeInfoId: {}", attempt, freezeInfoId);
                        Thread.sleep(EMAIL_RETRY_DELAY_MS);
                        continue;
                    }
                    log.error("重试{}次后封网信息仍不存在, freezeInfoId: {}", EMAIL_MAX_RETRIES, freezeInfoId);
                    return;
                }

                // 检查封网项是否存在
                NetworkFreezeItem item = networkFreezeItemMapper.selectOne(new LambdaQueryWrapper<NetworkFreezeItem>()
                    .eq(NetworkFreezeItem::getFreezeId, freezeInfoId));
                if (item == null) {
                    if (attempt < EMAIL_MAX_RETRIES) {
                        log.warn("封网项信息不存在，进行第{}次重试, freezeInfoId: {}", attempt, freezeInfoId);
                        Thread.sleep(EMAIL_RETRY_DELAY_MS);
                        continue;
                    }
                    log.error("重试{}次后封网项信息仍不存在, freezeInfoId: {}", EMAIL_MAX_RETRIES, freezeInfoId);
                    return;
                }

                // 发送邮件
                emailService.sendEmailByFreezeId(freezeInfoId);
                log.info("异步发送封网状态邮件成功, freezeInfoId: {}", freezeInfoId);
                return; // 发送成功，退出循环
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("邮件发送重试中断, freezeInfoId: {}", freezeInfoId, e);
                return;
            } catch (Exception e) {
                log.error("第{}次发送邮件失败: {}, freezeInfoId: {}", attempt, e.getMessage(), freezeInfoId);
                if (attempt < EMAIL_MAX_RETRIES) {
                    try {
                        Thread.sleep(EMAIL_RETRY_DELAY_MS * attempt); // 逐次增加重试等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return;
                    }
                } else {
                    log.error("邮件发送失败达到最大重试次数: {}", EMAIL_MAX_RETRIES);
                }
            }
        }
    }

    /**
     * 异步发送变更状态邮件
     *
     * @param freezeRecordId 封网记录ID
     * @param freezeInfoId   封网ID
     * @param userId         用户ID
     */
    private void sendEmailAsync(Long freezeRecordId, Long freezeInfoId, Long userId) {
        CompletableFuture.runAsync(() -> sendEmailWithRetry(freezeRecordId, freezeInfoId, userId));
    }

    /**
     * 发送审批邮件，支持重试
     *
     * @param freezeRecordId 封网记录ID
     * @param freezeInfoId   封网ID
     * @param userId         用户ID
     */
    private void sendEmailWithRetry(Long freezeRecordId, Long freezeInfoId, Long userId) {
        for (int attempt = 1; attempt <= EMAIL_MAX_RETRIES; attempt++) {
            try {
                // 检查封网信息是否存在
                NetworkFreezeInfo info = networkFreezeInfoMapper.selectById(freezeInfoId);
                if (info == null) {
                    if (attempt < EMAIL_MAX_RETRIES) {
                        log.warn("封网信息不存在，进行第{}次重试, freezeInfoId: {}, userId: {}", attempt, freezeInfoId, userId);
                        Thread.sleep(EMAIL_RETRY_DELAY_MS);
                        continue;
                    }
                    log.error("重试{}次后封网信息仍不存在, freezeInfoId: {}, userId: {}", EMAIL_MAX_RETRIES, freezeInfoId, userId);
                    return;
                }

                // 检查封网项是否存在
                NetworkFreezeItem item = networkFreezeItemMapper.selectOne(new LambdaQueryWrapper<NetworkFreezeItem>()
                    .eq(NetworkFreezeItem::getFreezeId, freezeInfoId));
                if (item == null) {
                    if (attempt < EMAIL_MAX_RETRIES) {
                        log.warn("封网项信息不存在，进行第{}次重试, freezeInfoId: {}, userId: {}", attempt, freezeInfoId, userId);
                        Thread.sleep(EMAIL_RETRY_DELAY_MS);
                        continue;
                    }
                    log.error("重试{}次后封网项信息仍不存在, freezeInfoId: {}, userId: {}", EMAIL_MAX_RETRIES, freezeInfoId, userId);
                    return;
                }

                // 发送邮件
                emailService.sendApprovalFreezeEmail(freezeRecordId, freezeInfoId, userId);
                log.info("异步发送封网审批邮件成功, freezeInfoId: {}, userId: {}", freezeInfoId, userId);
                return; // 发送成功，退出循环
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("邮件发送重试中断, freezeInfoId: {}, userId: {}", freezeInfoId, userId, e);
                return;
            } catch (Exception e) {
                log.error("第{}次发送邮件失败: {}, freezeInfoId: {}, userId: {}", attempt, e.getMessage(), freezeInfoId, userId);
                if (attempt < EMAIL_MAX_RETRIES) {
                    try {
                        Thread.sleep(EMAIL_RETRY_DELAY_MS * attempt); // 逐次增加重试等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return;
                    }
                } else {
                    log.error("邮件发送失败达到最大重试次数: {}", EMAIL_MAX_RETRIES);
                }
            }
        }
    }

    @Override
    public List<NetworkFreezeItemVo> queryfreezeUnfinishedList() {
        Date date = new Date();
        List<NetworkFreezeItem> items = networkFreezeItemMapper.selectList(new LambdaQueryWrapper<NetworkFreezeItem>()
            .gt(NetworkFreezeItem::getFreezeEndTime, date)
            .in(NetworkFreezeItem::getStage, List.of(NetworkFreezeStageEnum.PUBLISHED, NetworkFreezeStageEnum.SUBMITTED)));

        if (CollUtil.isEmpty(items)) {
            return new ArrayList<>();
        }

        List<Long> freezeIds = items.stream().map(NetworkFreezeItem::getFreezeId).toList();

        List<NetworkFreezeArea> networkFreezeAreas = networkFreezeAreaMapper.selectList(new LambdaQueryWrapper<NetworkFreezeArea>()
            .in(NetworkFreezeArea::getFreezeId, freezeIds));
        Map<Long, List<NetworkFreezeArea>> freezeIdAndAreas = networkFreezeAreas.stream().collect(Collectors.groupingBy(NetworkFreezeArea::getFreezeId));

        return items.stream().map(item -> {
            NetworkFreezeItemVo vo = new NetworkFreezeItemVo();
            BeanUtils.copyProperties(item, vo);
            vo.setPeriodAndArea(freezeIdAndAreas.getOrDefault(item.getFreezeId(), new ArrayList<>()));
            return vo;
        }).toList();
    }

    /**
     * 从处理人列表中移除当前审批人
     *
     * @param item   封网项
     * @param record 处理记录
     */
    private void removeCurrentProcessor(NetworkFreezeItem item, NetworkFreezeRecord record) {
        Long currentUserId = record.getProcessor().getUserId();
        if (item.getProcessorList() != null) {
            List<UserVo> updatedProcessorList = item.getProcessorList().stream()
                .filter(userVo -> !userVo.getUserId().equals(currentUserId))
                .toList();
            item.setProcessorList(updatedProcessorList);
        }
    }

    @Override
    public NetworkFreezeRecord getRecord(Long recordId) {
        NetworkFreezeRecord record = networkFreezeRecordMapper.selectById(recordId);
        if (ObjectUtils.isNull(record)) {
            return null;
        }
        LoginUser user = LoginHelper.getLoginUser();
        if (!record.getProcessor().getStaffId().equals(user.getStaffId())) {
            throw new ServiceException(MessageUtils.message("network.freeze.no.permission.approval", record.getFreezeCode()));
        }
        return record;
    }

    @Override
    public List<NetworkFreezeRecordVo> listRecord(Long infoId) {
        networkFreezeRecordMapper.selectList(new LambdaQueryWrapper<NetworkFreezeRecord>().eq(NetworkFreezeRecord::getFreezeId, infoId));
        return networkFreezeRecordMapper.selectList(new LambdaQueryWrapper<NetworkFreezeRecord>().eq(NetworkFreezeRecord::getFreezeId, infoId))
            .stream()
            .map(record -> {
                NetworkFreezeRecordVo recordVo = new NetworkFreezeRecordVo();
                recordVo.setId(record.getId());
                recordVo.setFreezeItemId(record.getFreezeItemId());
                recordVo.setFreezeId(record.getFreezeId());
                recordVo.setFreezeCode(record.getFreezeCode());
                recordVo.setStage(record.getStage());
                recordVo.setProcessorStatus(record.getProcessorStatus());
                recordVo.setProcessor(record.getProcessor());
                recordVo.setProcessorTime(record.getProcessorTime());
                recordVo.setOpinion(record.getOpinion());
                recordVo.setProcessOrder(record.getProcessOrder());
                recordVo.setNodeName(record.getNodeName());
                return recordVo;
            }).toList();
    }

    @Override
    public FlowNodeVo getFlowPreview(Long infoId) {
        log.info("获取变更申请ID{}的流程预览节点", infoId);

        NetworkFreezeItem item = networkFreezeItemMapper.selectOne(new LambdaQueryWrapper<NetworkFreezeItem>().eq(NetworkFreezeItem::getFreezeId, infoId));
        if (item == null) {
            log.warn("未找到变更申请ID{}对应的变更项", infoId);
            return null;
        }

        if (item.getInstanceId() == null) {
            log.warn("变更申请ID{}对应的变更项没有实例ID", infoId);
            return null;
        }

        try {
            // 调用工作流API获取流程实例数据
            FlowInstanceResp resp = smartFlowUtils.getFlowInstanceById(item.getInstanceId(), null);
            if (resp == null || resp.getData() == null) {
                log.warn("获取流程实例数据失败，实例ID: {}", item.getInstanceId());
                return null;
            }

            // 获取预览数据字符串
            String previewData = resp.getData().getPreviewData();
            if (org.apache.commons.lang3.StringUtils.isEmpty(previewData)) {
                log.warn("流程实例{}的预览数据为空", item.getInstanceId());
                return null;
            }
            return JsonUtils.parseObject(previewData, FlowNodeVo.class);
        } catch (Exception e) {
            log.error("获取流程预览节点失败，变更申请ID: {}, 异常: {}", infoId, e.getMessage(), e);
            return new FlowNodeVo();
        }
    }
}
