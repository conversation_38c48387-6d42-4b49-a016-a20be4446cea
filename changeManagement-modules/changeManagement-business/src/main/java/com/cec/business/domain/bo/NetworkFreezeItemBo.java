package com.cec.business.domain.bo;

import com.cec.business.domain.NetworkFreezeItem;
import com.cec.business.domain.enums.NetworkFreezeLevelEnum;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 封网申请记录业务对象 cm_network_freeze_item
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@AutoMapper(target = NetworkFreezeItem.class, reverseConvertGenerate = false)
public class NetworkFreezeItemBo{

    /**
     * 字段展示
     * freezeCode/title/level/stage/processorList/createTime/periodAndArea/freezeApps
     */
    private List<String> selectFiledNameList;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 变更ID
     */
    private Long freezeId;

    /**
     * 变更编号
     */
    private String freezeCode;

    /**
     * 封网名称
     */
    @NotBlank(message = "封网名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String title;

    /**
     * 封网等级
     */
    @NotNull(message = "封网等级不能为空", groups = {AddGroup.class, EditGroup.class})
    private NetworkFreezeLevelEnum level;

    /**
     * 封网等级<多选>
     */
    private List<NetworkFreezeLevelEnum> levelList;

    /**
     * 阶段
     */
    private NetworkFreezeStageEnum stage;

    /**
     * 阶段<多选>
     */
    private List<NetworkFreezeStageEnum> stageList;

    /**
     * 当前处理人id
     */
    private String processorId;

    /**
     * 当前处理人名字
     */
    private String processorName;

    /**
     * 封网地区id
     */
    private String areaId;

    /**
     * 封网时间开始(yyyy-MM-dd HH:mm:ss)
     */
    private String freezeTimeStart;

    /**
     * 封网时间结束(yyyy-MM-dd HH:mm:ss)
     */
    private String freezeTimeEnd;

    /**
     * 创建时间开始(yyyy-MM-dd HH:mm:ss)
     */
    private String createTimeStart;

    /**
     * 创建时间结束(yyyy-MM-dd HH:mm:ss)
     */
    private String createTimeEnd;


    private String code;
}
