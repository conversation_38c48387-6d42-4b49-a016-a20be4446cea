package com.cec.business.domain.vo;

import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 日历事件VO
 */
@Data
public class CalendarEventVo {

    /**
     * 日期
     */
    private Date date;

    /**
     * 变更操作列表
     */
    private List<ChangeInfoEvent> changeEvents;

    /**
     * 封网信息列表
     */
    private List<NetworkFreezeEvent> freezeEvents;

    /**
     * 变更操作事件
     */
    @Data
    public static class ChangeInfoEvent {
        /**
         * 变更操作ID
         */
        private Long changeInfoId;

        /**
         * 变更操作标题
         */
        private String title;

        /**
         * 变更操作阶段
         */
        private ChangeStageEnum stage;
    }

    /**
     * 封网事件
     */
    @Data
    public static class NetworkFreezeEvent {
        /**
         * 封网ID
         */
        private Long freezeId;

        /**
         * 封网阶段
         */
        private NetworkFreezeStageEnum stage;
    }
}
