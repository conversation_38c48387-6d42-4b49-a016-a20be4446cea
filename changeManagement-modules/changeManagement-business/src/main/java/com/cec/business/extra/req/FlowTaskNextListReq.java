package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.TreeMap;

/***
 * 查询任务处理策略
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowTaskNextListReq extends BaseReq implements Serializable {
    /**
     * 处理人id
     */
    @NotEmpty(message = "处理人id不能为空")
    private String assigneeId;
    /**
     * 处理人部门id
     */
    @NotEmpty(message = "处理人部门id不能为空")
    private String assigneeOrgId;
    /**
     * 表单Key（任务id和表单Key不能同时为空）
     */
    private String formKey;
    /**
     * 任务id（任务id和表单Key不能同时为空）
     */
    private Long id;
    /**
     * 流程执行变量
     */
    private TreeMap<String, Object> variables;

    @Override
    public String method() {
        return "/openapi/flow/task/next/list";
    }
}
