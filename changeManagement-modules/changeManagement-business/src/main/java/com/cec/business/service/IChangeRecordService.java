package com.cec.business.service;

import com.cec.business.domain.ChangeRecord;
import com.cec.business.domain.bo.ChangeRecordBo;
import com.cec.business.domain.vo.ChangeImplementingVo;
import com.cec.business.domain.vo.ChangeRecordVo;
import com.cec.business.domain.vo.FlowNodeVo;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 变更申请记录流水Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IChangeRecordService {

    /**
     * 查询变更申请记录流水
     *
     * @param id 主键
     * @return 变更申请记录流水
     */
    ChangeRecordVo queryById(Long id);

    /**
     * 分页查询变更申请记录流水列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 变更申请记录流水分页列表
     */
    TableDataInfo<ChangeRecordVo> queryPageList(ChangeRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的变更申请记录流水列表
     *
     * @param bo 查询条件
     * @return 变更申请记录流水列表
     */
    List<ChangeRecordVo> queryList(ChangeRecordBo bo);

    /**
     * 新增变更申请记录流水
     *
     * @param bo 变更申请记录流水
     * @return 是否新增成功
     */
    Boolean insertByBo(ChangeRecordBo bo);

    /**
     * 修改变更申请记录流水
     *
     * @param bo 变更申请记录流水
     * @return 是否修改成功
     */
    Boolean updateByBo(ChangeRecordBo bo);

    /**
     * 校验并批量删除变更申请记录流水信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取变更进行中的列表
     *
     * @param infoId 变更申请ID
     * @return 变更实施进度信息
     */
    ChangeImplementingVo getImplementing(Long infoId);

    /**
     * 获取审批明细表
     *
     * @param infoId 变更申请ID
     * @return 审批记录列表
     */
    List<ChangeImplementingVo.ChangeRecordVo> listRecord(Long infoId);

    /**
     * 获取流程预览节点
     *
     * @param infoId 变更申请ID
     * @return 流程节点数据
     */
    FlowNodeVo getFlowPreview(Long infoId);

    ChangeRecord getRecord(Long recordId);
}
