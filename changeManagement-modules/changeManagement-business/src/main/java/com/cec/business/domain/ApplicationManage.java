package com.cec.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.StatusEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.business.domain.vo.FreezeAreaVo;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.cec.system.domain.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 应用管理对象 cm_application_manage
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cm_application_manage", autoResultMap = true)
public class ApplicationManage extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 应用名称
     */
    private String applicationName;


    /**
     * BusinessOwner
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private UserVo businessOwner;

    /**
     * TeamLeader
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private UserVo teamLeader;

    /**
     * Team_ID
     */
    private Long teamId;

    /**
     * Team_Name
     */
    private String teamName;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 分类
     */
    private String categoryName;

    /**
     * 核心项目（1-是 2-否）
     */
    private WhetherEnum isKeyProject;

    /**
     * 外部项目（1-是 2-否）
     */
    private WhetherEnum isExternalSystem;

    /**
     * 维护影响等级（1-一级 2-二级 3-三级)
     */
    private LevelEnum maintenanceLevel;

    /**
     * 状态（1-可用 2-非可用）
     */
    private StatusEnum status;

    /**
     * 简单检查表ids
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> simpleCheckListIds;

    /**
     * 全量检查表ids
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> fullCheckListIds;

    /**
     * 封网地区
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<FreezeAreaVo> freezeAreaList;

}
