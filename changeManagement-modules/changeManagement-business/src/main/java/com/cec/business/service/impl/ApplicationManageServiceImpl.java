package com.cec.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.business.domain.ApplicationManage;
import com.cec.business.domain.NetworkFreezeArea;
import com.cec.business.domain.NetworkFreezeInfo;
import com.cec.business.domain.NetworkFreezeItem;
import com.cec.business.domain.bo.ApplicationManageBo;
import com.cec.business.domain.bo.CheckCategoryAndAppBo;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.business.domain.vo.ApplicationManageVo;
import com.cec.business.domain.vo.CheckCategoryAndAppVo;
import com.cec.business.domain.vo.FreezeAreaVo;
import com.cec.business.domain.vo.NetworkFreezeItemVo;
import com.cec.business.mapper.ApplicationManageMapper;
import com.cec.business.mapper.NetworkFreezeAreaMapper;
import com.cec.business.mapper.NetworkFreezeInfoMapper;
import com.cec.business.mapper.NetworkFreezeItemMapper;
import com.cec.business.service.IApplicationManageService;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 应用管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RequiredArgsConstructor
@Service
public class ApplicationManageServiceImpl implements IApplicationManageService {

    private final ApplicationManageMapper baseMapper;
    private final NetworkFreezeItemMapper networkFreezeItemMapper;
    private final NetworkFreezeInfoMapper networkFreezeInfoMapper;
    private final NetworkFreezeAreaMapper networkFreezeAreaMapper;

    /**
     * 查询应用管理
     *
     * @param id 主键
     * @return 应用管理
     */
    @Override
    public ApplicationManageVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询应用管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应用管理分页列表
     */
    @Override
    public TableDataInfo<ApplicationManageVo> queryPageList(ApplicationManageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ApplicationManage> lqw = buildQueryWrapper(bo);
        Page<ApplicationManageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的应用管理列表
     *
     * @param bo 查询条件
     * @return 应用管理列表
     */
    @Override
    public List<ApplicationManageVo> queryList(ApplicationManageBo bo) {
        LambdaQueryWrapper<ApplicationManage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ApplicationManage> buildQueryWrapper(ApplicationManageBo bo) {
        LambdaQueryWrapper<ApplicationManage> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ApplicationManage::getId);
        lqw.like(StringUtils.isNotBlank(bo.getApplicationName()), ApplicationManage::getApplicationName, bo.getApplicationName());
        lqw.apply(StringUtils.isNotBlank(bo.getBusinessOwnerName()), "JSON_EXTRACT(business_owner, '$.staffName') LIKE CONCAT('%', {0}, '%');", bo.getBusinessOwnerName());
        lqw.apply(StringUtils.isNotBlank(bo.getTeamLeaderName()), "JSON_EXTRACT(team_leader, '$.staffName') LIKE CONCAT('%', {0}, '%');", bo.getTeamLeader());
        lqw.eq(bo.getTeamId() != null, ApplicationManage::getTeamId, bo.getTeamId());
        lqw.like(StringUtils.isNotBlank(bo.getTeamName()), ApplicationManage::getTeamName, bo.getTeamName());
        lqw.eq(bo.getCategoryId() != null, ApplicationManage::getCategoryId, bo.getCategoryId());
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), ApplicationManage::getCategoryName, bo.getCategoryName());
        lqw.eq(bo.getIsKeyProject() != null, ApplicationManage::getIsKeyProject, bo.getIsKeyProject());
        lqw.eq(bo.getIsExternalSystem() != null, ApplicationManage::getIsExternalSystem, bo.getIsExternalSystem());
        lqw.eq(bo.getMaintenanceLevel() != null, ApplicationManage::getMaintenanceLevel, bo.getMaintenanceLevel());
        lqw.eq(bo.getStatus() != null, ApplicationManage::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增应用管理
     *
     * @param bo 应用管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ApplicationManageBo bo) {
        ApplicationManage add = MapstructUtils.convert(bo, ApplicationManage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改应用管理
     *
     * @param bo 应用管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ApplicationManageBo bo) {
        ApplicationManage update = MapstructUtils.convert(bo, ApplicationManage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ApplicationManage entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除应用管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 检查分类和应用关系
     * 根据分类IDs找出不存在的应用IDs，根据应用IDs找出不存在的分类IDs
     *
     * @param bo 包含分类IDs和应用IDs的请求参数
     * @return 返回不存在的分类IDs和应用IDs
     */
    @Override
    public CheckCategoryAndAppVo checkCategoryAndApplication(CheckCategoryAndAppBo bo) {
        CheckCategoryAndAppVo result = new CheckCategoryAndAppVo();

        // 如果分类IDs为空，则排除所有应用IDs
        if (bo.getCategoryIds() == null || bo.getCategoryIds().isEmpty()) {
            result.setExcludeAppIds(bo.getApplicationIds() != null ? bo.getApplicationIds() : new ArrayList<>());
            result.setExcludeCategoryIds(new ArrayList<>());
            return result;
        }

        // 如果应用IDs为空，则排除所有分类IDs
        if (bo.getApplicationIds() == null || bo.getApplicationIds().isEmpty()) {
            result.setExcludeAppIds(new ArrayList<>());
            result.setExcludeCategoryIds(bo.getCategoryIds());
            return result;
        }

        // 对输入的分类IDs和应用IDs去重
        List<Long> distinctCategoryIds = bo.getCategoryIds().stream().distinct().toList();
        List<Long> distinctAppIds = bo.getApplicationIds().stream().distinct().toList();

        // 通过分类IDs查询所有关联的应用IDs
        List<Long> appIdsInCategories = baseMapper.selectList(
                Wrappers.<ApplicationManage>lambdaQuery()
                    .select(ApplicationManage::getId)
                    .in(ApplicationManage::getCategoryId, distinctCategoryIds))
            .stream()
            .map(ApplicationManage::getId)
            .distinct() // 确保应用ID去重
            .toList();

        // 使用hutool的CollUtil计算差集，找出不存在的应用ID
        List<Long> excludeAppIds = CollUtil.subtractToList(distinctAppIds, appIdsInCategories);
        result.setExcludeAppIds(excludeAppIds);

        // 通过应用IDs查询所有关联的分类IDs
        List<Long> categoryIdsOfApps = baseMapper.selectList(
                Wrappers.<ApplicationManage>lambdaQuery()
                    .select(ApplicationManage::getCategoryId)
                    .in(ApplicationManage::getId, distinctAppIds))
            .stream()
            .map(ApplicationManage::getCategoryId)
            .distinct() // 确保分类ID去重
            .toList();

        // 使用hutool的CollUtil计算差集，找出不存在的分类ID
        List<Long> excludeCategoryIds = CollUtil.subtractToList(distinctCategoryIds, categoryIdsOfApps);
        result.setExcludeCategoryIds(excludeCategoryIds);

        return result;
    }

    @Override
    public List<NetworkFreezeItemVo> queryList(Date planTimeStart, Date planTimeEnd, Long[] applicationIds) {
        if (applicationIds == null || applicationIds.length == 0) {
            return new ArrayList<>();
        }

        List<NetworkFreezeItem> items = networkFreezeItemMapper.selectList(new LambdaQueryWrapper<NetworkFreezeItem>()
            .in(NetworkFreezeItem::getStage, List.of(NetworkFreezeStageEnum.PUBLISHED, NetworkFreezeStageEnum.SUBMITTED))
            .and(wrapper -> wrapper
                .le(NetworkFreezeItem::getFreezeStartTime, planTimeEnd)
                .ge(NetworkFreezeItem::getFreezeEndTime, planTimeStart)
            )
        );

        if (CollUtil.isEmpty(items)) {
            return new ArrayList<>();
        }

        Set<Long> freezeIds = items.stream().map(NetworkFreezeItem::getFreezeId).collect(Collectors.toSet());

        List<NetworkFreezeArea> networkFreezeAreas = networkFreezeAreaMapper.selectList(
            new LambdaQueryWrapper<NetworkFreezeArea>().in(NetworkFreezeArea::getFreezeId, freezeIds));

        if (CollUtil.isEmpty(networkFreezeAreas)) {
            return new ArrayList<>();
        }

        List<ApplicationManage> apps = baseMapper.selectList(
            new LambdaQueryWrapper<ApplicationManage>().in(ApplicationManage::getId, applicationIds));

        List<NetworkFreezeInfo> freezeInfosList = networkFreezeInfoMapper.selectList(
            new LambdaQueryWrapper<NetworkFreezeInfo>().in(NetworkFreezeInfo::getId, freezeIds));

        Set<String> appAreaIds = apps.stream()
            .filter(app -> CollUtil.isNotEmpty(app.getFreezeAreaList()))
            .flatMap(app -> app.getFreezeAreaList().stream())
            .map(FreezeAreaVo::getAreaId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Set<String> appNames = apps.stream()
            .map(ApplicationManage::getApplicationName)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Map<Long, NetworkFreezeInfo> freezeInfoMap = freezeInfosList.stream()
            .collect(Collectors.toMap(NetworkFreezeInfo::getId, Function.identity()));

        Set<Long> matchedFreezeIds = networkFreezeAreas.stream()
            .filter(area -> {
                // 区域匹配
                List<String> areaIds = area.getAreaId();
                boolean areaMatch = areaIds != null && areaIds.stream().anyMatch(appAreaIds::contains);

                // 时间范围匹配
                Date startTime = area.getStartTime();
                Date endTime = area.getEndTime();
                boolean timeMatch = startTime != null && endTime != null
                    && planTimeStart.compareTo(endTime) <= 0
                    && planTimeEnd.compareTo(startTime) >= 0;

                // 应用匹配
                NetworkFreezeInfo freezeInfo = freezeInfoMap.get(area.getFreezeId());
                boolean appMatch = freezeInfo != null &&
                    appNames.stream().anyMatch(appName -> freezeInfo.getFreezeApps().contains(appName));

                return areaMatch && timeMatch && appMatch;
            })
            .map(NetworkFreezeArea::getFreezeId)
            .collect(Collectors.toSet());

        if (matchedFreezeIds.isEmpty()) {
            return new ArrayList<>();
        }

        Map<Long, List<NetworkFreezeArea>> areaMap = networkFreezeAreas.stream()
            .filter(area -> matchedFreezeIds.contains(area.getFreezeId()))
            .collect(Collectors.groupingBy(NetworkFreezeArea::getFreezeId));

        return items.stream()
            .filter(item -> matchedFreezeIds.contains(item.getFreezeId()))
            .map(item -> {
                NetworkFreezeItemVo itemVo = new NetworkFreezeItemVo();
                BeanUtil.copyProperties(item, itemVo);
                itemVo.setPeriodAndArea(areaMap.getOrDefault(item.getFreezeId(), new ArrayList<>()));
                return itemVo;
            })
            .toList();
    }

    @Override
    public List<ApplicationManageVo> queryListById(Long[] ids) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<ApplicationManage>().in(ApplicationManage::getId, ids));
    }
}
