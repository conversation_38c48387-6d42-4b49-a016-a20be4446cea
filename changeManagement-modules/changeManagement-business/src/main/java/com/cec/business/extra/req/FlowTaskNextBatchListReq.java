package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 批量查询任务下一节点请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowTaskNextBatchListReq extends BaseReq implements Serializable {
    /**
     * 请求列表
     */
    @NotEmpty(message = "请求列表不能为空")
    @Valid
    private List<FlowTaskNextReqVO> flowTaskNextReqVOList;

    @Override
    public String method() {
        return "/openapi/flow/task/batch/next/list";
    }
} 