package com.cec.business.extra.req;

import com.cec.business.extra.resp.FlowInstanceStartResp;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 发起流程实例
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowInstanceStartReq extends BaseReq implements Serializable {

    /**
     * 名称
     */
    @NotEmpty(message = "名称不能为空")
    private String name;

    /**
     * 业务id
     */
    @NotEmpty(message = "业务id不能为空")
    private String businessId;

    /**
     * 表单唯一Key
     */
    @NotEmpty(message = "id不能为空")
    private String formKey;

    /**
     * 发起人id
     */
    @NotEmpty(message = "发起人id不能为空")
    private String userId;

    /**
     * 发起人名称
     */
    @NotEmpty(message = "发起人名称不能为空")
    private String userName;

    /**
     * 发起人部门id
     */
    @NotEmpty(message = "发起人部门id不能为空")
    private String userOrgId;

    /**
     * 发起人部门名称
     */
    @NotEmpty(message = "发起人部门名称不能为空")
    private String userOrgName;

    /**
     * 步骤信息列表
     */
    @NotNull(message = "步骤信息列表不能为空")
    private List<FlowInstanceStartResp.FlowInstanceNode> nodeList;

    /**
     * 抄送人员信息列表
     */
    private List<FlowInstanceStartResp.CopyFor> copyForList;

    /**
     * 流程自定义表单数据
     */
    private TreeMap<String, Object> formData;

    /**
     * 流程执行变量
     */
    private TreeMap<String, Object> variables;


    @Override
    public String method() {
        return "/openapi/flow/instance/start";
    }
}
