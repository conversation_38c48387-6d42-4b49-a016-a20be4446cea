package com.cec.business.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 流程节点VO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FlowNodeVo {

    /**
     * 节点ID
     */
    private String id;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 会签类型
     * 0-或签(一人同意即可)
     * 1-会签(全部同意)
     */
    private Integer countersignType;

    /**
     * 自选标志
     * 0-不可自选
     * 1-可自选
     */
    private Integer selfSelect;

    /**
     * 候选人列表
     */
    private List<CandidateVo> candidateList;

    /**
     * 下级节点列表
     */
    private List<FlowNodeVo> next;

    /**
     * 候选人VO
     */
    @Data
    @NoArgsConstructor
    public static class CandidateVo {

        /**
         * 审批人ID
         */
        private String assigneeId;

        /**
         * 审批人姓名
         */
        private String assigneeName;

        /**
         * 审批人组织ID
         */
        private String assigneeOrgId;

        /**
         * 审批人组织名称
         */
        private String assigneeOrgName;

        /**
         * 委托人
         */
        private String delegator;
    }
} 