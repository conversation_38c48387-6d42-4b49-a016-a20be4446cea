package com.cec.business.service;

import com.cec.business.domain.bo.ChangeDelayBo;
import com.cec.business.domain.vo.ChangeDelayVo;

import java.util.Collection;
import java.util.List;

/**
 * 变更延期记录Service接口
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
public interface IChangeDelayService {

    /**
     * 查询变更延期记录
     *
     * @param id 主键
     * @return 变更延期记录
     */
    ChangeDelayVo queryById(Long id);

    /**
     * 查询变更延期记录列表
     *
     * @param bo        查询条件
     * @return 变更延期记录分页列表
     */
    List<ChangeDelayVo> queryPageList(ChangeDelayBo bo);

    /**
     * 查询符合条件的变更延期记录列表
     *
     * @param bo 查询条件
     * @return 变更延期记录列表
     */
    List<ChangeDelayVo> queryList(ChangeDelayBo bo);

    /**
     * 新增变更延期记录
     *
     * @param bo 变更延期记录
     * @return 是否新增成功
     */
    Boolean insertByBo(ChangeDelayBo bo);

    /**
     * 修改变更延期记录
     *
     * @param bo 变更延期记录
     * @return 是否修改成功
     */
    Boolean updateByBo(ChangeDelayBo bo);

    /**
     * 校验并批量删除变更延期记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
