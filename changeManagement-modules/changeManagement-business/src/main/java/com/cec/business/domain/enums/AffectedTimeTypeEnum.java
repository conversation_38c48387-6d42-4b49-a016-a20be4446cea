package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 影响时间类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum AffectedTimeTypeEnum {
    /**
     * 分钟
     */
    MINUTE(1, "分钟"),
    /**
     * 小时
     */
    HOUR(2, "小时");

    @EnumValue
    private final Integer code;
    private final String info;

    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static AffectedTimeTypeEnum findByCode(Integer code) {
        for (AffectedTimeTypeEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static AffectedTimeTypeEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
} 