package com.cec.business.domain.bo;

import com.cec.business.domain.ChangeDelay;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.core.validate.QueryGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 变更延期记录业务对象 cm_change_delay
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@AutoMapper(target = ChangeDelay.class, reverseConvertGenerate = false)
public class ChangeDelayBo {

    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 变更id
     */
    @NotNull(message = "变更id不能为空", groups = {QueryGroup.class})
    private Long changeId;

    /**
     * 新结束时间
     */
    @NotNull(message = "新结束时间不能为空")
    private Date scheduleEndTime;

    /**
     * 备注
     */
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;

    /**
     * 延期文件
     */
    private List<String> delayFiles;


}
