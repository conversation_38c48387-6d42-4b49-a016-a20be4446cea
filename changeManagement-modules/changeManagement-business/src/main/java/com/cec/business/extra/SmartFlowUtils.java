package com.cec.business.extra;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.cec.business.extra.req.*;
import com.cec.business.extra.resp.*;
import com.cec.common.core.config.CecSmartFlowConfig;
import com.cec.common.core.constant.CacheNames;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.redis.utils.RedisUtils;
import jakarta.annotation.Resource;
import jodd.net.HttpMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/***
 * 对接smartflow的工具类
 */
@Slf4j
@Component
public class SmartFlowUtils {
    /**
     * 超时时间
     */
    private static final Integer TIMEOUT = 120 * 1000;

    @Resource
    private CecSmartFlowConfig config;

    private String apiDomain;
    private BiConsumer<String, Object> handler;

    private static final String REQUEST_ID = "requestId";
    private static final String SIGNATURE = "signature";
    private static final String TIMESTAMP = "timestamp";
    private static final String URL = "_url_";
    private static final String PARAMS = "_params_";

    /**
     * 初始化处理器
     */
    public void init() {
        apiDomain = config.getSmartFlowDomain();
        log.info("SmartFlowUtils初始化成功，apiDomain: {}", apiDomain);
    }

    /**
     * 设置自定义回调处理器
     */
    public void setHandler(BiConsumer<String, Object> handler) {
        this.handler = handler;
    }

    /**
     * 查询流程模型定义信息
     */
    public FlowModelResp flowModelList(FlowModelReq req) {
        return sendRequest(req, FlowModelResp.class);
    }

    /**
     * 查询最新绑定流程的表单
     */
    public FlowFormKeyResp flowFormKey(String key, Map<String, Object> param) {
        String url = apiDomain + "/openapi/flow/form/key/" + key;
        return sendGetRequest(url, param == null ? new TreeMap<>() : param, FlowFormKeyResp.class);
    }

    /**
     * 根据ID查询表单信息
     */
    public FlowFormKeyResp flowFormId(String id, Map<String, Object> param) {
        String url = apiDomain + "/openapi/flow/form/" + id;
        return sendGetRequest(url, param == null ? new TreeMap<>() : param, FlowFormKeyResp.class);
    }

    /**
     * 获取当前执行的任务
     */
    public FlowTaskCurrentResp getFlowTaskCurrent(FlowTaskCurrentReq req) {
        return sendRequest(req, FlowTaskCurrentResp.class);
    }

    /**
     * 查询任务处理策略
     */
    public FlowTaskStrategyResp strategy(FlowTaskStrategyReq req) {
        return sendRequest(req, FlowTaskStrategyResp.class);
    }

    /**
     * 待发送节点列表
     */
    public FlowTaskNextListResp nextList(FlowTaskNextListReq req) {
        return sendRequest(req, FlowTaskNextListResp.class);
    }

    /**
     * 批量查询待发送节点列表
     */
    public FlowTaskNextBatchListResp nextBatchList(FlowTaskNextBatchListReq req) {
        return sendRequest(req, FlowTaskNextBatchListResp.class);
    }

    /**
     * 任务加减签
     */
    public BaseResp countersign(FlowTaskCountersignReq req) {
        return sendRequest(req, BaseResp.class);
    }

    /**
     * 根据流程实例ID查询任务列表
     */
    public FlowTaskListResp getFlowTaskListByInstanceId(String instanceId, Map<String, Object> param) {
        String url = apiDomain + "/openapi/flow/task/list/" + instanceId;
        return sendGetRequest(url, param == null ? new TreeMap<>() : param, FlowTaskListResp.class);
    }

    /**
     * 根据流程实例ID查询任务进度条信息
     */
    public FlowTaskDisplayResp getFlowTaskDisplayByInstanceId(String instanceId) {
        String url = apiDomain + "/openapi/flow/task/list/display/" + instanceId;
        return sendGetRequest(url, new TreeMap<>(), FlowTaskDisplayResp.class);
    }

    /**
     * 待回退节点列表
     */
    public FlowTaskNextListResp backList(FlowTaskBackListReq req) {
        return sendRequest(req, FlowTaskNextListResp.class);
    }

    /**
     * 获取待办任务列表
     */
    public FlowTaskTodoListResp getFlowTaskTodoList(FlowTaskTodoListReq req) {
        return sendRequest(req, FlowTaskTodoListResp.class);
    }

    /**
     * 执行任务
     */
    public BaseResp approve(FlowTaskApproveReq req) {
        return sendRequest(req, BaseResp.class);
    }

    /**
     * 回退任务
     */
    public BaseResp back(FlowTaskBackReq req) {
        return sendRequest(req, BaseResp.class);
    }

    /**
     * 拒绝任务
     */
    public BaseResp reject(FlowTaskRejectReq req) {
        return sendRequest(req, BaseResp.class);
    }

    /**
     * 抄送任务已阅
     */
    public BaseResp know(FlowTaskKnowReq req) {
        return sendRequest(req, BaseResp.class);
    }

    /**
     * 启动流程实例
     */
    public FlowInstanceBaseResp startFlow(FlowInstanceStartReq req) {
        return sendRequest(req, FlowInstanceBaseResp.class);
    }

    /**
     * 取消流程实例
     */
    public FlowInstanceBaseResp cancelFlow(FlowInstanceCancelReq req) {
        return sendRequest(req, FlowInstanceBaseResp.class);
    }

    /**
     * 获取流程实例列表
     */
    public FlowInstanceListResp getFlowInstanceList(FlowInstanceListReq req) {
        return sendRequest(req, FlowInstanceListResp.class);
    }

    /**
     * 根据ID查询流程实例信息
     */
    public FlowInstanceResp getFlowInstanceById(String id, Map<String, Object> param) {
        String url = apiDomain + "/openapi/flow/instance/" + id;
        return sendGetRequest(url, param == null ? new TreeMap<>() : param, FlowInstanceResp.class);
    }

    /**
     * 新增委托信息
     */
    public BaseResp insertDelegate(FlowDelegateInsertReq req) {
        return sendRequest(req, BaseResp.class);
    }

    /**
     * 修改委托信息
     */
    public BaseResp updateDelegate(FlowDelegateUpdateReq req) {
        return sendRequest(req, BaseResp.class);
    }

    /**
     * 委托信息分页
     */
    public FlowDelegateListResp delegateList(FlowDelegateListReq req) {
        return sendRequest(req, FlowDelegateListResp.class);
    }

    /**
     * 获取当前token
     */
    public String getCurrentToken() {
        Object tokenObj = RedisUtils.getCacheObject(CacheNames.SMART_FLOW_TOKEN);

        if (tokenObj instanceof String) {
            String token = (String) tokenObj;
            // 检查token是否非空且不是字面上的 "null" 字符串
            if (ObjectUtil.isNotEmpty(token) && !"null".equalsIgnoreCase(token)) {
                return token;
            } else {
                log.warn("Token from Redis is blank or literal 'null' (Value: \"{}\"). Refreshing token.", token);
                return refreshToken();
            }
        } else {
            if (tokenObj != null) {
                // 如果对象存在但不是String类型，记录其类型以帮助排查问题
                log.warn("Token from Redis is not a String. Actual type: {}. Refreshing token.", tokenObj.getClass().getName());
            } else {
                // 如果 tokenObj 为 null，说明缓存中没有有效的token
                log.info("Token not found in Redis (null value) for key {}. Refreshing token.", CacheNames.SMART_FLOW_TOKEN);
            }
            return refreshToken();
        }
    }

    /**
     * 调用接口时出现token错误，可调用该接口重新获取token
     */
    public String refreshToken() {
        LoginReq req = new LoginReq();
        req.setUsername(config.getSmartFlowAccount());
        req.setPassword(Base64.getEncoder().encodeToString(config.getSmartFlowPwd().getBytes()));
        LoginResp resp = login(req);
        if (resp != null) {
            // 存放进缓存
            RedisUtils.setCacheObject(CacheNames.SMART_FLOW_TOKEN, resp.getData(), Duration.ofDays(1));
            return resp.getData();
        } else {
            return null;
        }
    }

    /**
     * 登录获取token
     */
    private LoginResp login(LoginReq req) {
        return sendRequest(req, LoginResp.class);
    }

    /**
     * 发送post请求
     */
    private <T extends BaseResp> T sendRequest(BaseReq req, Class<T> tClass) {
        String url = apiDomain + req.method();
        Map<String, Object> params = getParams(req);
        String result;
        Map<String, String> header = fillHttpHeader(url, params, HttpMethod.POST);
        long start = System.currentTimeMillis();
        long ms = 0;
        try (HttpResponse response = HttpRequest.post(url)
            .headerMap(header, true)
            .body(JSON.toJSONString(params))
            .timeout(TIMEOUT)
            .setReadTimeout(TIMEOUT)
            .execute()) {
            result = response.body();
            ms = System.currentTimeMillis() - start;
        } catch (Exception e) {
            log.error("SmartFlow请求接口报错", e);
            requestHandler(url, JSON.toJSONString(header), JSON.toJSONString(params), e.getMessage(), ms);
            throw new RuntimeException("SmartFlow请求报错, " + e.getMessage());
        }
        return requestAfterProcess(url, params, tClass, result, header, ms);
    }

    /**
     * 发送get请求
     */
    private <T extends BaseResp> T sendGetRequest(String url, Map<String, Object> params, Class<T> tClass) {
        String result;
        Map<String, String> header = fillHttpHeader(url, params, HttpMethod.GET);
        long start = System.currentTimeMillis();
        long ms = 0;
        try (HttpResponse response = HttpRequest.get(url)
            .timeout(TIMEOUT)
            .setReadTimeout(TIMEOUT)
            .headerMap(header, true)
            .execute()) {
            result = response.body();
            ms = System.currentTimeMillis() - start;
        } catch (Exception e) {
            log.error("SmartFlow请求接口报错", e);
            requestHandler(url, JSON.toJSONString(header), JSON.toJSONString(params), e.getMessage(), ms);
            throw new RuntimeException("SmartFlow请求报错, " + e.getMessage());
        }
        return requestAfterProcess(url, params, tClass, result, header, ms);
    }

    /**
     * 请求后处理
     */
    private <T extends BaseResp> T requestAfterProcess(String url,
                                                       Map<String, Object> params,
                                                       Class<T> tClass,
                                                       String result,
                                                       Map<String, String> header,
                                                       long ms) {
        if (JSONUtil.isTypeJSON(result)) {
            JSONObject jsonResult = JSONUtil.parseObj(JSONUtil.parse(result));
            requestHandler(url, JSON.toJSONString(header), JSON.toJSONString(params), jsonResult, ms);

            try {
                return JSONUtil.toBean(jsonResult, tClass);
            } catch (Exception e) {
                log.error("处理响应数据失败: {}", e.getMessage(), e);
                throw new ServiceException("处理响应数据失败: " + e.getMessage());
            }
        } else {
            requestHandler(url, JSON.toJSONString(header), JSON.toJSONString(params), result, ms);
            throw new RuntimeException("SmartFlow请求报错, " + result);
        }
    }

    /**
     * 计算签名字段
     */
    private String sign(SortedMap<String, Object> sortedMap) {
        sortedMap = sortedMap.entrySet()
            .stream()
            .filter(e -> ObjectUtil.isNotEmpty(e.getValue()))
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (e1, e2) -> e1,
                TreeMap::new
            ));
        String jsonString = config.getAppSecret() + JSON.toJSONString(sortedMap);
        log.info("签名前的原文:{}", jsonString);
        return DigestUtils.md5DigestAsHex(jsonString.getBytes()).toUpperCase();
    }

    /**
     * 填充请求头参数
     */
    private Map<String, String> fillHttpHeader(String reqUrl, Map<String, Object> params, HttpMethod method) {
        Map<String, String> header = new TreeMap<>();
        if (!reqUrl.contains("/openapi/sys/login")) {
            // 非登录接口，都需要传Authorization字段
            header.put("Authorization", getCurrentToken());
        }
        String decodeUrl = getDecodeUrl(reqUrl);
        header.put(TIMESTAMP, String.valueOf(System.currentTimeMillis()));
        header.put(REQUEST_ID, UUID.fastUUID().toString(true));
        SortedMap<String, Object> sortedMap;
        if (method.equals(HttpMethod.GET)) {
            sortedMap = new TreeMap<>();
            sortedMap.put(URL, decodeUrl);
            sortedMap.put(TIMESTAMP, header.get(TIMESTAMP));
            sortedMap.put(REQUEST_ID, header.get(REQUEST_ID));
            if (params != null && !params.isEmpty()) {
                // 如果get请求有参数的情况下需要把参数放到params参数中
                sortedMap.put(PARAMS, convertMapToString(params));
            }
        } else if (method.equals(HttpMethod.POST)) {
            params.put(URL, decodeUrl);
            params.put(TIMESTAMP, header.get(TIMESTAMP));
            params.put(REQUEST_ID, header.get(REQUEST_ID));
            sortedMap = new TreeMap<>(params);
        } else {
            throw new RuntimeException("不支持的请求方式");
        }
        header.put(SIGNATURE, sign(sortedMap));
        if (params != null && !params.isEmpty()) {
            params.remove(URL);
            params.remove(TIMESTAMP);
            params.remove(REQUEST_ID);
        }
        return header;
    }

    /**
     * 将 Map 转换为 key=value&key=value 格式的字符串
     */
    private String convertMapToString(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (first) {
                first = false;
            } else {
                result.append("&");
            }
            String encodedKey = entry.getKey();
            String encodedValue = entry.getValue() != null
                ? String.valueOf(entry.getValue())
                : "";
            result.append(encodedKey).append("=").append(encodedValue);
        }
        return result.toString();
    }

    /**
     * 执行请求后处理内容
     */
    private void requestHandler(String url, String header, String params, Object result, long ms) {
        log.info("SmartFlow请求地址:{}, 请求头:{}, 请求体:{}, 结果:{}, 耗时{}毫秒", url, header, params, result, ms);
        if (handler != null) {
            handler.accept(url, result);
        }
    }

    /**
     * 接口拼装参数并签名
     */
    private Map<String, Object> getParams(BaseReq req) {
        return BeanUtil.beanToMap(req, false, true);
    }

    /**
     * 解码URL
     */
    private String getDecodeUrl(String reqUrl) {
        String decodeUrl;
        try {
            java.net.URL url = new URL(reqUrl);
            decodeUrl = URLDecoder.decode(url.getPath(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("组装请求参数异常", e);
            throw new ServiceException("组装请求参数异常");
        }
        return decodeUrl;
    }

    /**
     * 获取用户列表
     */
    public SysUserResp getUserListByApp(String keyword) {
        String url = apiDomain + "/sysUser/getUserListByApp";
        Map<String, Object> params = new TreeMap<>();
        if (keyword != null && !keyword.isEmpty()) {
            params.put("keyword", keyword);
        }
        return sendGetRequest(url, params, SysUserResp.class);
    }

    /**
     * 用户同步
     */
    public SysUserSynchronizationResp syncUserData(SysUserSynchronizationReqVo req) {
        return sendRequest(req, SysUserSynchronizationResp.class);
    }

    /**
     * 获取启用的区域列表
     */
    public SysRegionListResp getSysRegionEnabledList() {
        String url = apiDomain + "/sysRegion/enable/list";
        return sendGetRequest(url, new TreeMap<>(), SysRegionListResp.class);
    }

    /**
     * 获取组织用户树
     *
     * @param userDeptIds 部门ID列表
     * @param userIds 用户ID列表
     * @return 组织用户树响应
     */
    public SysOrgUserTreeResp getOrgUserTree(List<Integer> userDeptIds, List<Integer> userIds) {
        String url = apiDomain + "/sysUser/getOrgUserTree";
        Map<String, Object> params = new TreeMap<>();
        if (userDeptIds != null && !userDeptIds.isEmpty()) {
            params.put("userDeptIds", userDeptIds);
        }
        if (userIds != null && !userIds.isEmpty()) {
            params.put("userIds", userIds);
        }
        return sendGetRequest(url, params, SysOrgUserTreeResp.class);
    }

//    public static void main(String[] args) {
//        LoginReq req = new LoginReq();
//        req.setUsername("admin");
//        req.setPassword(Base64.getEncoder().encodeToString("123456".getBytes()));
//        LoginResp resp = SmartFlowUtils.Account.login(req);
//        System.out.println("返回:" + JSON.toJSONString(resp));
//        SmartFlowUtils.Instance.flowFormKey("*********");
//    }

}
