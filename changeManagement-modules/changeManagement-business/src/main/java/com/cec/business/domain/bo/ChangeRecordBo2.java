package com.cec.business.domain.bo;

import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.cec.system.domain.vo.UserVo;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 变更申请记录流水业务对象 cm_change_record
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChangeRecordBo2 extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 变更记录ID
     */
    private Long changeItemId;

    /**
     * 变更ID
     */
    private Long changeId;

    /**
     * 审批状态
     * 1-发起 2-已审批 3-已拒绝 4-待审批 5-已实施 6-已回滚 7-开始变更 8-通过 9-不通过 10-已取消
     */
    private ProcessorStatusEnum processorStatus;

    /**
     * 审批意见/备注
     */
    private String opinion;

    /**
     * 是否需要加签
     * 1-是 2-否
     */
    private WhetherEnum isSealAddition;

    /**
     * 加签列表id
     */
    private List<UserVo> sealAdditionList;

    /**
     * 步骤
     */
    private String step;

    private String code;

}
