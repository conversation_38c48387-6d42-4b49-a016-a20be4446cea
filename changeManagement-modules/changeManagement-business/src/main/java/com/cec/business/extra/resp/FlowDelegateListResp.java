package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 委托信息分页查询相应数据
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowDelegateListResp extends BaseResp implements Serializable {
    /**
     * 委托信息相应数据
     */
    private Record data;

    @Data
    public static class Record implements Serializable {
        /**
         * 委托记录列表
         */
        private List<FlowDelegate> records;

        /**
         * 当前页码
         */
        private Integer current;

        /**
         * 是否命中
         */
        private boolean hitCount;

        /**
         * 总页数
         */
        private Integer pages;

        /**
         * 是否进行count查询
         */
        private boolean searchCount;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 总记录数
         */
        private Integer total;
    }

    @Data
    public static class FlowDelegate implements Serializable {
        /**
         * 创建人id（上游服务）	string
         */
        private String creatorId;
        /**
         * 创建人名称（上游服务）	string
         */
        private String creatorName;
        /**
         * 创建人部门id（上游服务）	string
         */
        private String creatorOrgId;
        /**
         * 创建人部门名称（上游服务）	string
         */
        private String creatorOrgName;
        /**
         * 结束时间	date
         */
        private Date endTime;
        /**
         * 流程委托组列表
         */
        private List<FlowDelegateItem> flowDelegateItemList;
        /**
         * 流程委托id	integer
         */
        private Integer id;
        /**
         * 关联的表单id，多个逗号分割	string
         */
        private String modules;
        /**
         * 委托说明	string
         */
        private String remark;
        /**
         * 开始时间	date
         */
        private Date startTime;
        /**
         * 状态 0：正常，1：关闭	integer
         */
        private Integer status;
        /**
         * 委托人id	string
         */
        private String userId;
        /**
         * 委托人名称	string
         */
        private String userName;
        /**
         * 委托人部门id	string
         */
        private String userOrgId;
        /**
         * 委托人部门名称	string
         */
        private String userOrgName;
    }

    @Data
    public static class FlowDelegateItem implements Serializable {
        /**
         * 流程委托id，关联flow_delegate	integer
         */
        private Integer delegateId;
        /**
         * 流程委托组id	integer
         */
        private Integer id;
        /**
         * 流程委托组名称	string
         */
        private String name;
        /**
         * 被委托人id	string
         */
        private String receiverId;
        /**
         * 被委托人名称	string
         */
        private String receiverName;
        /**
         * 被委托人部门id	string
         */
        private String receiverOrgId;
        /**
         * 被委托人部门名称	string
         */
        private String receiverOrgName;
        /**
         * 发起者id列表	string
         */
        private String starterId;
        /**
         * 发起者名称列表	string
         */
        private String starterName;
        /**
         * 类型：0 普通，1 默认	integer
         */
        private Integer type;
    }
}
