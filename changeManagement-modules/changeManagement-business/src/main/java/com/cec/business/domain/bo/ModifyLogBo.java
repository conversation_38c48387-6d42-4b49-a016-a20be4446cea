package com.cec.business.domain.bo;

import com.cec.business.domain.ModifyLog;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 变更修改记录业务对象 cm_modify_log
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ModifyLog.class, reverseConvertGenerate = false)
public class ModifyLogBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 变更编号
     */
    private String changeCode;

    /**
     * 修改字段
     */
    private String modifyFiled;

    /**
     * 修改字段_zh（简体中文名称）
     */
    private String filedNameZh;

    /**
     * 修改字段_us（英文名称）
     */
    private String filedNameUs;

    /**
     * 修改字段_tw（繁体中文名称）
     */
    private String filedNameTw;

    /**
     * 修改内容-旧
     */
    private String contentOld;

    /**
     * 修改内容-新
     */
    private String contentNew;

    /**
     * StaffId
     */
    private String staffId;

    /**
     * StaffName
     */
    private String staffName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改时间起始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    /**
     * 修改时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;
}
