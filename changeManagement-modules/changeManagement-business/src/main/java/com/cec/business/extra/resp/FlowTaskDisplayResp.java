package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 流程实例任务进度条信息响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowTaskDisplayResp extends BaseResp implements Serializable {
    
    /**
     * 响应数据
     */
    private DisplayData data;
    
    @Data
    public static class DisplayData implements Serializable {
        /**
         * 展示信息
         */
        private TaskInfo displayInfo;
        /**
         * 完成状态 0：处理中 1：已完成 2：已拒绝 3：已终止
         */
        private Integer finishStatus;
        /**
         * 下一个任务
         */
        private DisplayData nextTask;
        /**
         * 通知任务列表
         */
        private List<TaskInfo> notifyTaskList;
        /**
         * 同级任务列表
         */
        private List<TaskInfo> sameLevelTaskList;
    }
    
    @Data
    public static class TaskInfo implements Serializable {
        /**
         * 审批人ID
         */
        private Long assigneeId;
        /**
         * 审批人名称
         */
        private String assigneeName;
        /**
         * 审批人部门ID
         */
        private Long assigneeOrgId;
        /**
         * 审批人部门名称
         */
        private String assigneeOrgName;
        /**
         * 审批意见
         */
        private String comment;
        /**
         * 加签人列表
         */
        private Map<String, Object> countersignList;
        /**
         * 会签类型 0：会签 1：或签
         */
        private Integer countersignType;
        /**
         * 加签人ID
         */
        private Long countersignUserId;
        /**
         * 加签人名称
         */
        private String countersignUserName;
        /**
         * 创建人
         */
        private Long createUser;
        /**
         * 委托人ID
         */
        private Long delegatorId;
        /**
         * 委托人名称
         */
        private String delegatorName;
        /**
         * 委托人部门ID
         */
        private Long delegatorOrgId;
        /**
         * 委托人部门名称
         */
        private String delegatorOrgName;
        /**
         * 是否启用
         */
        private Integer enable;
        /**
         * 结束时间
         */
        private String endTime;
        /**
         * 创建时间
         */
        private String gmt8Create;
        /**
         * 修改时间
         */
        private String gmt8Modified;
        /**
         * 主键ID
         */
        private String id;
        /**
         * 实例ID
         */
        private String instanceId;
        /**
         * 任务名称
         */
        private String name;
        /**
         * 节点ID
         */
        private String nodeId;
        /**
         * 节点名称
         */
        private String nodeName;
        /**
         * 上一节点ID
         */
        private String prevId;
        /**
         * 上一节点标识
         */
        private String prevNodeId;
        /**
         * 上一节点名称
         */
        private String prevNodeName;
        /**
         * 流程定义ID
         */
        private String processDefinitionId;
        /**
         * 流程实例ID
         */
        private String processInstanceId;
        /**
         * 状态 0：审批中 1：已完成 2：已回退 3：已拒绝 4：已终止
         */
        private Integer status;
        /**
         * 任务ID
         */
        private String taskId;
        /**
         * 任务类型 0：审批 1：知会
         */
        private Integer taskType;
        /**
         * 租户ID
         */
        private Long tenantId;
        /**
         * 更新人
         */
        private Long updateUser;
    }
} 