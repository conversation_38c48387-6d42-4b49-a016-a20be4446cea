package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 查询最新绑定流程的表单
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowFormKeyResp extends BaseResp implements Serializable {
    /**
     * 响应数据
     */
    private FlowFormKey data;

    @Data
    public static class FlowFormKey {
        /**
         * 表单配置信息json
         */
        private String configJson;
        /**
         * 表单字段信息列表
         */
        private List<FlowFormField> fieldList;
        /**
         * 表单唯一key
         */
        private String formKey;
        /**
         * 表单名称
         */
        private String formName;
        /**
         * 表单类型：0，业务文档类；1，办公类；2.人力资源类
         */
        private Integer formType;
        /**
         * 创建时间
         */
        private Date gmt8Create;
        /**
         * 表单id
         */
        private Integer id;
        /**
         * 来源：0，自定义表单；1，外部表单
         */
        private Integer source;
        /**
         * 是否已关联流程 0、null：否 1：是
         */
        private Integer used;
        /**
         * 版本
         */
        private Integer version;
    }

    @Data
    public static class FlowFormField {
        /**
         * 适用类型：0-全部 1-条件 2-表单权限
         */
        private Integer applyType;
        /**
         * 字段名
         */
        private String fieldName;
        /**
         * 字段类型 字符串-string，数值-number，日期-date，日期时间-datetime
         */
        private String fieldType;
        /**
         * 字段值列表
         */
        private List<FlowFormFieldValue> fieldValueList;
        /**
         * 关联表单id
         */
        private Integer formId;
        /**
         * 流程条件id
         */
        private Integer id;
        /**
         * 字段显示名
         */
        private String labelName;
        /**
         * 描述
         */
        private String remark;
        /**
         * 文本框类型: 0, 输入框 ; 1,下拉框
         */
        private Integer textBoxType;
    }

    @Data
    public static class FlowFormFieldValue {
        /**
         * 关联字段id
         */
        private Integer fieldId;
        /**
         * 条件字段值id
         */
        private Integer id;
        /**
         * 字段值显示名
         */
        private String labelName;
        /**
         * 条件字段值描述信息
         */
        private String remark;
        /**
         * 条件字段值
         */
        private String value;
    }
}
