package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;

/**
 * 获取当前执行的任务信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowTaskCurrentResp extends BaseResp implements Serializable {

    /**
     * 当前执行的任务信息
     */
    private FlowTaskCurrent data;

    @Data
    public static class FlowTaskCurrent {
        /**
         * 审批人id
         */
        private String assigneeId;
        /**
         * 审批人名称
         */
        private String assigneeName;
        /**
         * 审批人部门id
         */
        private String assigneeOrgId;
        /**
         * 审批人部门名称
         */
        private String assigneeOrgName;
        /**
         * 审批意见
         */
        private String comment;
        /**
         * 当前任务加签人员信息列表
         */
        private String countersignList;
        /**
         * 会签类型 0：会签 1：或签
         */
        private Integer countersignType;
        /**
         * 加签人id
         */
        private String countersignUserId;
        /**
         * 加签人名称
         */
        private String countersignUserName;
        /**
         * 创建人
         */
        private Long createUser;
        /**
         * 委托人id
         */
        private String delegatorId;
        /**
         * 委托人名称
         */
        private String delegatorName;
        /**
         * 委托人部门id
         */
        private String delegatorOrgId;
        /**
         * 委托人部门名称
         */
        private String delegatorOrgName;
        /**
         * 任务结束时间
         */
        private Date endTime;
        /**
         * 创建时间
         */
        private Date gmt8Create;
        /**
         * 更新时间
         */
        private Date gmt8Modified;
        /**
         * 主键
         */
        private Long id;
        /**
         * 实例id
         */
        private Long instanceId;
        /**
         * 任务名称
         */
        private String name;
        /**
         * 节点id
         */
        private String nodeId;
        /**
         * 节点名称
         */
        private String nodeName;
        /**
         * 上一任务id
         */
        private Long prevId;
        /**
         * 上一节点id
         */
        private String prevNodeId;
        /**
         * 上一节点名称
         */
        private String prevNodeName;
        /**
         * 处理结果 0进行中 1已处理 2他人已处理 3已拒绝 4他人已拒绝 5已取消 6他人已取消 7已退回 8他人已退回
         */
        private Integer status;
        /**
         * 任务id
         */
        private String taskId;
        /**
         * 任务类型 0常规 1委托 2指派 3退回 4抄送 5加签 6通知
         */
        private Integer taskType;
        /**
         * 更新人
         */
        private Long updateUser;
    }
}
