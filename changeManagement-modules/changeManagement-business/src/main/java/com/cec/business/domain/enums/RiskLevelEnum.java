package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 风险等级枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum RiskLevelEnum {
    /**
     * 低
     */
    LOW(1, "低"),
    /**
     * 中
     */
    MEDIUM(2, "中"),
    /**
     * 高
     */
    HIGH(3, "高"),
    /**
     * 不可接受
     */
    UNACCEPTABLE(4, "不可接受");

    @EnumValue
    private final Integer code;
    private final String info;

    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RiskLevelEnum findByCode(Integer code) {
        for (RiskLevelEnum level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static RiskLevelEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
