package com.cec.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.cec.business.domain.ApplicationManage;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.StatusEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.system.domain.vo.UserVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;



/**
 * 应用管理视图对象 cm_application_manage
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ApplicationManage.class)
public class ApplicationManageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long id;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * BusinessOwner
     */
    private UserVo businessOwner;

    /**
     * TeamLeader
     */
    private UserVo teamLeader;

    /**
     * Team_ID
     */
    private Long teamId;

    /**
     * Team_Name
     */
    private String teamName;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 分类
     */
    private String categoryName;

    /**
     * 核心项目（1-是 2-否）
     */
    private WhetherEnum isKeyProject;

    /**
     * 外部项目（1-是 2-否）
     */
    private WhetherEnum isExternalSystem;

    /**
     * 维护影响等级（1-一级 2-二级 3-三级)
     */
    private LevelEnum maintenanceLevel;

    /**
     * 状态（1-可用 2-非可用）
     */
    private StatusEnum status;

    /**
     * 简单检查表ids
     */
    private List<String> simpleCheckListIds;

    /**
     * 全量检查表ids
     */
    private List<String> fullCheckListIds;

    /**
     * 封网地区
     */
    private List<FreezeAreaVo> freezeAreaList;

}
