package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 待发送节点列表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowTaskNextListResp extends BaseResp implements Serializable {

    /**
     * 响应数据
     */
    private List<FlowTaskNext> data;

    @Data
    public static class FlowTaskNext {
        /**
         * 候选人列表
         */
        private List<Candidate> candidateList;
        /**
         * 会签类型 0：会签 1：或签
         */
        private Integer countersignType;
        /**
         * 节点id
         */
        private String id;
        /**
         * 节点名称
         */
        private String name;
        /**
         * 下个执行步骤列表
         */
        private String next;
        /**
         * 是否自选 0：否， 1：是
         */
        private Integer selfSelect;
    }

    @Data
    public static class Candidate implements Serializable {
        /**
         * 审批人id
         */
        private String assigneeOrgId;
        /**
         * 审批人名称
         */
        private String assigneeOrgName;
        /**
         * 审批人部门id
         */
        private String assigneeId;
        /**
         * 审批人部门名称
         */
        private String assigneeName;
    }
}
