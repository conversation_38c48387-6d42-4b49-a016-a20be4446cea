//package com.cec.business.controller;
//
//import java.util.List;
//
//import lombok.RequiredArgsConstructor;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.constraints.*;
//import cn.dev33.satoken.annotation.SaCheckPermission;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.validation.annotation.Validated;
//import com.cec.common.idempotent.annotation.RepeatSubmit;
//import com.cec.common.log.annotation.Log;
//import com.cec.common.web.core.BaseController;
//import com.cec.common.mybatis.core.page.PageQuery;
//import com.cec.common.core.domain.R;
//import com.cec.common.core.validate.AddGroup;
//import com.cec.common.core.validate.EditGroup;
//import com.cec.common.log.enums.BusinessType;
//import com.cec.common.excel.utils.ExcelUtil;
//import com.cec.business.domain.vo.NetworkFreezeItemVo;
//import com.cec.business.domain.bo.NetworkFreezeItemBo;
//import com.cec.business.service.INetworkFreezeItemService;
//import com.cec.common.mybatis.core.page.TableDataInfo;
//
///**
// * 封网申请记录
// *
// * <AUTHOR>
// * @date 2025-05-13
// */
//@Validated
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/business/networkFreezeItem")
//public class NetworkFreezeItemController extends BaseController {
//
//    private final INetworkFreezeItemService networkFreezeItemService;
//
//    /**
//     * 查询封网申请记录列表
//     */
//    @SaCheckPermission("business:networkFreezeItem:list")
//    @GetMapping("/list")
//    public TableDataInfo<NetworkFreezeItemVo> list(NetworkFreezeItemBo bo, PageQuery pageQuery) {
//        return networkFreezeItemService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出封网申请记录列表
//     */
//    @SaCheckPermission("business:networkFreezeItem:export")
//    @Log(title = "封网申请记录", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(NetworkFreezeItemBo bo, HttpServletResponse response) {
//        List<NetworkFreezeItemVo> list = networkFreezeItemService.queryList(bo);
//        ExcelUtil.exportExcel(list, "封网申请记录", NetworkFreezeItemVo.class, response);
//    }
//
//    /**
//     * 获取封网申请记录详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("business:networkFreezeItem:query")
//    @GetMapping("/{id}")
//    public R<NetworkFreezeItemVo> getInfo(@NotNull(message = "主键不能为空")
//                                     @PathVariable Long id) {
//        return R.ok(networkFreezeItemService.queryById(id));
//    }
//
//    /**
//     * 新增封网申请记录
//     */
//    @SaCheckPermission("business:networkFreezeItem:add")
//    @Log(title = "封网申请记录", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody NetworkFreezeItemBo bo) {
//        return toAjax(networkFreezeItemService.insertByBo(bo));
//    }
//
//    /**
//     * 修改封网申请记录
//     */
//    @SaCheckPermission("business:networkFreezeItem:edit")
//    @Log(title = "封网申请记录", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody NetworkFreezeItemBo bo) {
//        return toAjax(networkFreezeItemService.updateByBo(bo));
//    }
//
//    /**
//     * 删除封网申请记录
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("business:networkFreezeItem:remove")
//    @Log(title = "封网申请记录", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//                          @PathVariable Long[] ids) {
//        return toAjax(networkFreezeItemService.deleteWithValidByIds(List.of(ids), true));
//    }
//}
