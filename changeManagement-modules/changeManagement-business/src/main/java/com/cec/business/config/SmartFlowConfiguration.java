package com.cec.business.config;

import cn.hutool.json.JSONUtil;
import com.cec.business.extra.SmartFlowUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * SmartFlow工具配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class SmartFlowConfiguration {

    /**
     * 初始化SmartFlowUtils处理器
     *
     * @param smartFlowUtils SmartFlow工具类实例
     * @return SmartFlowHandler 返回初始化后的SmartFlowUtils实例
     */
    @Bean
    public SmartFlowUtils initSmartFlowHandler(SmartFlowUtils smartFlowUtils) {
        smartFlowUtils.init();
        smartFlowUtils.setHandler((url, result) -> {
            // 请求后处理，记录详细的请求响应日志
            try {
                if (result instanceof Throwable) {
                    log.error("SmartFlow接口调用出错 - URL: {}, 错误信息: {}",
                        url, ((Throwable) result).getMessage());
                } else {
                    // 美化输出JSON便于阅读
                    String resultStr = result instanceof String ?
                        (String) result : JSONUtil.toJsonPrettyStr(result);

                    // 截断过长的结果避免日志过大
                    if (resultStr.length() > 500) {
                        resultStr = resultStr.substring(0, 500) + "... (内容已截断)";
                    }

                    log.debug("SmartFlow接口响应 - URL: {}, 响应内容: {}", url, resultStr);
                }
            } catch (Exception e) {
                log.warn("SmartFlow日志处理异常", e);
            }
        });

        return smartFlowUtils;
    }
}
