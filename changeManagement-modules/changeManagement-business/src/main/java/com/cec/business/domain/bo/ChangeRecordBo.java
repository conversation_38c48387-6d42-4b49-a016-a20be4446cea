package com.cec.business.domain.bo;

import com.cec.business.domain.ChangeRecord;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 变更申请记录流水业务对象 cm_change_record
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ChangeRecord.class, reverseConvertGenerate = false)
public class ChangeRecordBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 变更记录ID
     */
    private Long changeItemId;

    /**
     * 变更ID
     */
    private Long changeId;

    /**
     * 变更编号
     */
    private String changeCode;

    /**
     * 变更标题
     */
    private String title;

    /**
     * 审批状态
     * 1-发起 2-已审批 3-已拒绝 4-待审批 5-已实施 6-已回滚 7-开始变更 8-通过 9-不通过 10-已取消
     */
    private ProcessorStatusEnum processorStatus;

    /**
     * 当前处理人id
     */
    private String processorId;

    /**
     * 当前处理人名字
     */
    private String processorName;

    /**
     * 审批时间起(yyyy-MM-dd HH:mm:ss)
     */
    private String processorTimeStart;

    /**
     * 审批时间止(yyyy-MM-dd HH:mm:ss)
     */
    private String processorTimeEnd;

    /**
     * 变更组ID
     */
    private Long teamId;

    /**
     * 变更组ID<多选>
     */
    private List<Long> teamIdList;

    /**
     * 变更组名
     */
    private String teamName;

    /**
     * 是否紧急变更
     * 1-是 2-否
     */
    private WhetherEnum isUrgentChange;

    /**
     * 是否紧急变更<多选>
     */
    private List<WhetherEnum> isUrgentChangeList;

    /**
     * 是否封网变更（1-是 2-否）
     */
    private WhetherEnum isNetworkFreezeChange;

    /**
     * 是否封网变更<多选>
     */
    private List<WhetherEnum> isNetworkFreezeChangeList;

    /**
     * 优先级
     * 1-高 2-中 3-低
     */
    private LevelEnum priority;

    /**
     * 优先级<多选>
     */
    private List<LevelEnum> priorityList;

    /**
     * 审批意见/备注
     */
    private String opinion;

    /**
     * 是否需要加签
     * 1-是 2-否
     */
    private WhetherEnum isSealAddition;

    /**
     * 阶段
     * 1-草稿 2-已提交 3-待审批 4-已审批 5-实施中 6-已完成 7-已拒绝 8-已回滚 9-已取消 10-待验证
     */
    private ChangeStageEnum stage;

    /**
     * 阶段<多选>
     */
    private List<ChangeStageEnum> stageList;

    /**
     * 地点id<多选>
     */
    private List<Long> locationIdList;

    /**
     * 地点名
     */
    private String locationName;

    /**
     * 步骤
     */
    private String step;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 会签类型
     * 0-会签 1-或签
     */
    private String countersignType;

    /**
     * 是否允许加签
     * 0-否 1-是
     */
    private String countersign;

    /**
     * smartFlow节点策略
     */
    private String strategy;

    /**
     * smartFlow待发送节点列表
     */
    private String nextList;

    /**
     * smartFlow当前任务信息
     */
    private String taskCurrent;

    /**
     * 需要返回的字段列表
     * 默认字段: changeCode, title, processorStatus, processorList, teamName, isUrgentChange, priority, processorTime, nodeName
     *
     * 可用的额外字段:
     * - 基础字段: processorId, teamId, opinion, isSealAddition, stage, step, taskId, countersignType,
     *   countersign, strategy, nextList, taskCurrent
     * - 扩展字段(需要额外查询):
     *   - 枚举类型: isNetworkFreezeChange(WhetherEnum), systemsAffectedNo(Integer), importantUsers(String),
     *     fallback(String), complexity(String), riskLevel(String), riskScore(Integer)
     *   - 字符串类型: locationName, changeType, requesterName, affectedUser
     *   - 日期类型: planTimeStart, planTimeEnd
     *   - 集合类型: applicationName(List), affectedApplicationName(List), affectedDeviceName(List),
     *     teamLeaderList(List), changeApproverList(List), applicationOwnerList(List),
     *     changeImplementerList(List), changeVerifierList(List), urgentChangeInspectorList(List),
     *     deptLeaderList(List), serviceDeliveryTeamList(List)
     *
     * @see com.cec.business.service.impl.ChangeRecordServiceImpl#queryPageList
     */
    private List<String> selectFiledNameList;

    /**
     * 申请人ID
     */
    private String requesterId;

    /**
     * 申请人名称
     */
    private String requesterName;

    /**
     * 节点名称
     */
    private List<String> nodeNameList;

}
