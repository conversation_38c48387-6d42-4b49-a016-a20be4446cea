package com.cec.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cec.business.handler.JsonListStringTypeHandler;
import com.cec.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 变更延期记录对象 cm_change_delay
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cm_change_delay")
public class ChangeDelay extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 变更id
     */
    private Long changeId;

    /**
     * 新结束时间
     */
    private Date scheduleEndTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 延期文件
     */
    @TableField(typeHandler = JsonListStringTypeHandler.class)
    private List<String> delayFiles;

}
