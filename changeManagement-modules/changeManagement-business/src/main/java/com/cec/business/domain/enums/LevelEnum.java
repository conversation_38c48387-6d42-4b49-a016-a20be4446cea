package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 等级枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum LevelEnum {
    /**
     * 高
     */
    HIGH(1, "高"),
    /**
     * 中
     */
    MEDIUM(2, "中"),
    /**
     * 低
     */
    LOW(3, "低");

    @EnumValue
    private final Integer code;
    private final String info;

    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static LevelEnum findByCode(Integer code) {
        for (LevelEnum level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static LevelEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
} 