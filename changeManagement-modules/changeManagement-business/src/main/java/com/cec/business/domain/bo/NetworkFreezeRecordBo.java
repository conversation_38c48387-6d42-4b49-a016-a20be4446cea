package com.cec.business.domain.bo;

import com.cec.business.domain.NetworkFreezeRecord;
import com.cec.business.domain.enums.NetworkFreezeLevelEnum;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 封网申请记录流水业务对象 cm_network_freeze_record
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = NetworkFreezeRecord.class, reverseConvertGenerate = false)
public class NetworkFreezeRecordBo extends BaseEntity {

    /**
     * 字段展示
     * freezeCode/title/processorStatus/processorId/processorList/processorTime/level/periodAndArea/freezeApps
     */
    private List<String> selectFiledNameList;

    /**
     *
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 封网记录ID
     */
    private Long freezeItemId;

    /**
     * 封网ID
     */
    private Long freezeId;

    /**
     * 封网编号
     */
    private String freezeCode;

    /**
     * 变更标题
     */
    private String title;

    /**
     * 审批状态
     */
    @NotNull(message = "processorStatus不能为空", groups = { EditGroup.class })
    private ProcessorStatusEnum processorStatus;

    /**
     * 审批状态<多选>
     */
    private List<ProcessorStatusEnum> processorStatusList;

    /**
     * 当前处理人id
     */
    private String processorId;

    /**
     * 当前处理人名字
     */
    private String processorName;



    /**
     * 审批意见/备注
     */
    @NotNull(message = "opinion不能为空", groups = { EditGroup.class })
    private String opinion;

    /**
     * 阶段(1-草稿/2-已提交/3已发布/4-已拒绝)
     */
    private List<NetworkFreezeStageEnum> stageList;

    /**
     * 封网等级(1-一级 2-二级 3-三级)
     */
    private NetworkFreezeLevelEnum level;

    /**
     * 封网等级<多选>
     * (1-一级 2-二级 3-三级)
     */
    private List<NetworkFreezeLevelEnum> levelList;

    /**
     * 封网地区id
     */
    private String areaId;

    /**
     * 封网时间开始(yyyy-MM-dd HH:mm:ss)
     */
    private String freezeTimeStart;

    /**
     * 封网时间结束(yyyy-MM-dd HH:mm:ss)
     */
    private String freezeTimeEnd;

    /**
     * 审批时间起(yyyy-MM-dd HH:mm:ss)
     */
    private String processorTimeStart;

    /**
     * 审批时间止(yyyy-MM-dd HH:mm:ss)
     */
    private String processorTimeEnd;

    private String code;
}
