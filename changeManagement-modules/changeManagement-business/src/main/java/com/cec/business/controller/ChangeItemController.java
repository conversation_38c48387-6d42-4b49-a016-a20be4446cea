//package com.cec.business.controller;
//
//import java.util.List;
//
//import lombok.RequiredArgsConstructor;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.constraints.*;
//import cn.dev33.satoken.annotation.SaCheckPermission;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.validation.annotation.Validated;
//import com.cec.common.idempotent.annotation.RepeatSubmit;
//import com.cec.common.log.annotation.Log;
//import com.cec.common.web.core.BaseController;
//import com.cec.common.mybatis.core.page.PageQuery;
//import com.cec.common.core.domain.R;
//import com.cec.common.core.validate.AddGroup;
//import com.cec.common.core.validate.EditGroup;
//import com.cec.common.log.enums.BusinessType;
//import com.cec.common.excel.utils.ExcelUtil;
//import com.cec.business.domain.vo.ChangeItemVo;
//import com.cec.business.domain.bo.ChangeItemBo;
//import com.cec.business.service.IChangeItemService;
//import com.cec.common.mybatis.core.page.TableDataInfo;
//
///**
// * 变更申请记录
// *
// * <AUTHOR>
// * @date 2025-05-13
// */
//@Validated
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/business/changeItem")
//public class ChangeItemController extends BaseController {
//
//    private final IChangeItemService changeItemService;
//
//    /**
//     * 查询变更申请记录列表
//     */
//    @SaCheckPermission("business:changeItem:list")
//    @GetMapping("/list")
//    public TableDataInfo<ChangeItemVo> list(ChangeItemBo bo, PageQuery pageQuery) {
//        return changeItemService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出变更申请记录列表
//     */
//    @SaCheckPermission("business:changeItem:export")
//    @Log(title = "变更申请记录", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(ChangeItemBo bo, HttpServletResponse response) {
//        List<ChangeItemVo> list = changeItemService.queryList(bo);
//        ExcelUtil.exportExcel(list, "变更申请记录", ChangeItemVo.class, response);
//    }
//
//    /**
//     * 获取变更申请记录详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("business:changeItem:query")
//    @GetMapping("/{id}")
//    public R<ChangeItemVo> getInfo(@NotNull(message = "主键不能为空")
//                                     @PathVariable Long id) {
//        return R.ok(changeItemService.queryById(id));
//    }
//
//    /**
//     * 新增变更申请记录
//     */
//    @SaCheckPermission("business:changeItem:add")
//    @Log(title = "变更申请记录", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody ChangeItemBo bo) {
//        return toAjax(changeItemService.insertByBo(bo));
//    }
//
//    /**
//     * 修改变更申请记录
//     */
//    @SaCheckPermission("business:changeItem:edit")
//    @Log(title = "变更申请记录", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ChangeItemBo bo) {
//        return toAjax(changeItemService.updateByBo(bo));
//    }
//
//    /**
//     * 删除变更申请记录
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("business:changeItem:remove")
//    @Log(title = "变更申请记录", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//                          @PathVariable Long[] ids) {
//        return toAjax(changeItemService.deleteWithValidByIds(List.of(ids), true));
//    }
//}
