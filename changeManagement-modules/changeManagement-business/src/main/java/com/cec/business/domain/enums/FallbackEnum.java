package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 回退/回滚枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum FallbackEnum {
    /**
     * No Back Out
     */
    NO_BACK_OUT(1, "No Back Out", 1),
    /**
     * Back Out. 在维护前经过测试，并且还原措施可靠
     */
    BACK_OUT_TESTED(2, "Back Out. 在维护前经过测试，并且还原措施可靠", 2),
    /**
     * Back Out. 没有进行测试，或者还原步骤会受到实际环境中的不确定因素影响
     */
    BACK_OUT_UNTESTED(3, "Back Out. 没有进行测试，或者还原步骤会受到实际环境中的不确定因素影响", 5),
    /**
     * Fallback is not possible
     */
    FALLBACK_NOT_POSSIBLE(4, "Fallback is not possible", 6);

    @EnumValue
    private final Integer code;
    private final String info;
    private final Integer score;

    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static FallbackEnum findByCode(Integer code) {
        for (FallbackEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static FallbackEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
} 