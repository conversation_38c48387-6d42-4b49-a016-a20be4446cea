package com.cec.business.controller;

import com.cec.business.extra.SmartFlowUtils;
import com.cec.business.extra.req.*;
import com.cec.business.extra.resp.*;
import java.util.List;
import com.cec.common.core.domain.R;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 测试流程相关接口
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/testFlow")
public class TestFlowController {

    private final SmartFlowUtils smartFlowUtils;

    /**
     * 获取流程模型列表
     */
    @GetMapping("/flowModelList")
    public R<FlowModelResp> flowModelList(@RequestParam(value = "name", required = false) String name) {
        FlowModelReq req = new FlowModelReq();
        req.setName(name);
        FlowModelResp resp = smartFlowUtils.flowModelList(req);
        return R.ok(resp);
    }

    /**
     * 获取流程模型列表
     */
    @PostMapping("/flowModelList")
    public R<FlowModelResp> flowModelList(@RequestBody @Valid FlowModelReq req) {
        FlowModelResp resp = smartFlowUtils.flowModelList(req);
        return R.ok(resp);
    }

    /**
     * 查询最新绑定流程的表单
     */
    @PostMapping("/flowFormKey")
    public R<FlowFormKeyResp> flowFormKey(@RequestBody @Valid FlowFormKeyReq req) {
        FlowFormKeyResp resp = smartFlowUtils.flowFormKey(req.getKey(), req.getParam());
        return R.ok(resp);
    }

    /**
     * 根据ID查询表单信息
     */
    @PostMapping("/flowFormId")
    public R<FlowFormKeyResp> flowFormId(@RequestBody @Valid FlowFormIdReq req) {
        FlowFormKeyResp resp = smartFlowUtils.flowFormId(req.getId(), req.getParam());
        return R.ok(resp);
    }

    /**
     * 获取当前执行的任务
     */
    @PostMapping("/getFlowTaskCurrent")
    public R<FlowTaskCurrentResp> getFlowTaskCurrent(@RequestBody @Valid FlowTaskCurrentReq req) {
        FlowTaskCurrentResp resp = smartFlowUtils.getFlowTaskCurrent(req);
        return R.ok(resp);
    }

    /**
     * 查询任务处理策略
     */
    @PostMapping("/strategy")
    public R<FlowTaskStrategyResp> strategy(@RequestBody @Valid FlowTaskStrategyReq req) {
        FlowTaskStrategyResp resp = smartFlowUtils.strategy(req);
        return R.ok(resp);
    }

    /**
     * 待发送节点列表
     */
    @PostMapping("/nextList")
    public R<FlowTaskNextListResp> nextList(@RequestBody @Valid FlowTaskNextListReq req) {
        FlowTaskNextListResp resp = smartFlowUtils.nextList(req);
        return R.ok(resp);
    }

    /**
     * 批量查询待发送节点列表
     */
    @PostMapping("/nextBatchList")
    public R<FlowTaskNextBatchListResp> nextBatchList(@RequestBody @Valid FlowTaskNextBatchListReq req) {
        FlowTaskNextBatchListResp resp = smartFlowUtils.nextBatchList(req);
        return R.ok(resp);
    }

    /**
     * 任务加减签
     */
    @PostMapping("/countersign")
    public R<BaseResp> countersign(@RequestBody @Valid FlowTaskCountersignReq req) {
        BaseResp resp = smartFlowUtils.countersign(req);
        return R.ok(resp);
    }

    /**
     * 根据流程实例ID查询任务列表
     */
    @GetMapping("/getFlowTaskListByInstanceId/{instanceId}")
    public R<FlowTaskListResp> getFlowTaskListByInstanceId(@PathVariable String instanceId,
                                                           @RequestParam(required = false) Map<String, Object> param) {
        FlowTaskListResp resp = smartFlowUtils.getFlowTaskListByInstanceId(instanceId, param);
        return R.ok(resp);
    }

    /**
     * 根据流程实例ID查询任务进度条信息
     */
    @GetMapping("/getFlowTaskDisplayByInstanceId/{instanceId}")
    public R<FlowTaskDisplayResp> getFlowTaskDisplayByInstanceId(@PathVariable String instanceId) {
        FlowTaskDisplayResp resp = smartFlowUtils.getFlowTaskDisplayByInstanceId(instanceId);
        return R.ok(resp);
    }

    /**
     * 待回退节点列表
     */
    @PostMapping("/backList")
    public R<FlowTaskNextListResp> backList(@RequestBody @Valid FlowTaskBackListReq req) {
        FlowTaskNextListResp resp = smartFlowUtils.backList(req);
        return R.ok(resp);
    }

    /**
     * 获取待办任务列表
     */
    @PostMapping("/getFlowTaskTodoList")
    public R<FlowTaskTodoListResp> getFlowTaskTodoList(@RequestBody @Valid FlowTaskTodoListReq req) {
        FlowTaskTodoListResp resp = smartFlowUtils.getFlowTaskTodoList(req);
        return R.ok(resp);
    }

    /**
     * 执行任务
     */
    @PostMapping("/approve")
    public R<BaseResp> approve(@RequestBody @Valid FlowTaskApproveReq req) {
        BaseResp resp = smartFlowUtils.approve(req);
        return R.ok(resp);
    }

    /**
     * 回退任务
     */
    @PostMapping("/back")
    public R<BaseResp> back(@RequestBody @Valid FlowTaskBackReq req) {
        BaseResp resp = smartFlowUtils.back(req);
        return R.ok(resp);
    }

    /**
     * 拒绝任务
     */
    @PostMapping("/reject")
    public R<BaseResp> reject(@RequestBody @Valid FlowTaskRejectReq req) {
        BaseResp resp = smartFlowUtils.reject(req);
        return R.ok(resp);
    }

    /**
     * 抄送任务已阅
     */
    @PostMapping("/know")
    public R<BaseResp> know(@RequestBody @Valid FlowTaskKnowReq req) {
        BaseResp resp = smartFlowUtils.know(req);
        return R.ok(resp);
    }

    /**
     * 启动流程实例
     */
    @PostMapping("/startFlow")
    public R<FlowInstanceBaseResp> startFlow(@RequestBody @Valid FlowInstanceStartReq req) {
        FlowInstanceBaseResp resp = smartFlowUtils.startFlow(req);
        return R.ok(resp);
    }

    /**
     * 取消流程实例
     */
    @PostMapping("/cancelFlow")
    public R<FlowInstanceBaseResp> cancelFlow(@RequestBody @Valid FlowInstanceCancelReq req) {
        FlowInstanceBaseResp resp = smartFlowUtils.cancelFlow(req);
        return R.ok(resp);
    }

    /**
     * 获取流程实例列表
     */
    @PostMapping("/getFlowInstanceList")
    public R<FlowInstanceListResp> getFlowInstanceList(@RequestBody @Valid FlowInstanceListReq req) {
        FlowInstanceListResp resp = smartFlowUtils.getFlowInstanceList(req);
        return R.ok(resp);
    }

    /**
     * 根据ID查询流程实例信息
     */
    @PostMapping("/getFlowInstanceById")
    public R<FlowInstanceResp> getFlowInstanceById(@RequestBody @Valid FlowInstanceByIdReq req) {
        FlowInstanceResp resp = smartFlowUtils.getFlowInstanceById(req.getId(), req.getParam());
        return R.ok(resp);
    }

    /**
     * 新增委托信息
     */
    @PostMapping("/insertDelegate")
    public R<BaseResp> insertDelegate(@RequestBody @Valid FlowDelegateInsertReq req) {
        BaseResp resp = smartFlowUtils.insertDelegate(req);
        return R.ok(resp);
    }

    /**
     * 修改委托信息
     */
    @PostMapping("/updateDelegate")
    public R<BaseResp> updateDelegate(@RequestBody @Valid FlowDelegateUpdateReq req) {
        BaseResp resp = smartFlowUtils.updateDelegate(req);
        return R.ok(resp);
    }

    /**
     * 委托信息分页
     */
    @PostMapping("/delegateList")
    public R<FlowDelegateListResp> delegateList(@RequestBody @Valid FlowDelegateListReq req) {
        FlowDelegateListResp resp = smartFlowUtils.delegateList(req);
        return R.ok(resp);
    }

    /**
     * 获取当前token
     */
    @PostMapping("/getCurrentToken")
    public R<String> getCurrentToken() {
        String token = smartFlowUtils.getCurrentToken();
        return R.ok(token);
    }

    /**
     * 刷新token
     */
    @PostMapping("/refreshToken")
    public R<String> refreshToken() {
        String token = smartFlowUtils.refreshToken();
        return R.ok(token);
    }

    /**
     * 获取基于应用的用户列表
     */
    @GetMapping("/getUserListByApp")
    public R<SysUserResp> getUserListByApp(@RequestParam(value = "keyword", required = false) String keyword) {
        SysUserResp resp = smartFlowUtils.getUserListByApp(keyword);
        return R.ok(resp);
    }

    /**
     * 用户数据同步
     */
    @PostMapping("/syncUserData")
    public R<SysUserSynchronizationResp> syncUserData(@RequestBody @Valid SysUserSynchronizationReqVo req) {
        SysUserSynchronizationResp resp = smartFlowUtils.syncUserData(req);
        return R.ok(resp);
    }
    
    /**
     * 获取启用的区域列表
     */
    @GetMapping("/getSysRegionEnabledList")
    public R<SysRegionListResp> getSysRegionEnabledList() {
        SysRegionListResp resp = smartFlowUtils.getSysRegionEnabledList();
        return R.ok(resp);
    }
    
    /**
     * 获取组织用户树
     */
    @GetMapping("/getOrgUserTree")
    public R<SysOrgUserTreeResp> getOrgUserTree(
            @RequestParam(value = "userDeptIds", required = false) List<Integer> userDeptIds,
            @RequestParam(value = "userIds", required = false) List<Integer> userIds) {
        SysOrgUserTreeResp resp = smartFlowUtils.getOrgUserTree(userDeptIds, userIds);
        return R.ok(resp);
    }
}
