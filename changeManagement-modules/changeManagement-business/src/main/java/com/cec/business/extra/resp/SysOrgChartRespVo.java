package com.cec.business.extra.resp;

import lombok.Data;

import java.util.List;

/**
 * 组织架构响应对象
 *
 * <AUTHOR>
 */
@Data
public class SysOrgChartRespVo {
    
    /**
     * 外部唯一id
     */
    private String externalId;
    
    /**
     * 主键id
     */
    private Long id;
    
    /**
     * 层级链id
     */
    private String levelHeadIds;
    
    /**
     * 组织机构层级id
     */
    private Long levelId;
    
    /**
     * 部门名称(组织机构名称，三林设备)
     */
    private String orgChartName;
    
    /**
     * 父节点id
     */
    private Long parentId;
    
    /**
     * 组织机构名称简称
     */
    private String shortForm;
    
    /**
     * 层级员工信息
     */
    private List<UserTreeEntity> sysUserTreeList;
    
    /**
     * 版本id
     */
    private Long versionId;
} 