package com.cec.business.domain;

import com.cec.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.cec.business.domain.enums.ConfInfoType;
import com.cec.business.domain.enums.ConfInfoStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 配置信息对象 cm_conf_info
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cm_conf_info")
public class ConfInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 类型（1-分类 2-封网地区 3- 变更地点 4-受影响设备）
     * @see ConfInfoType
     */
    private ConfInfoType type;

    /**
     * 名称
     */
    private String name;

    /**
     * 名称-英文
     */
    private String nameEn;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态（1-可用 2-非可用)
     * @see ConfInfoStatus
     */
    private ConfInfoStatus status;


}
