package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审批状态枚举
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ProcessorStatusEnum {
    /**
     * 发起
     */
    STARTED(1, "发起"),
    /**
     * 已审批
     */
    APPROVED(2, "已审批"),
    /**
     * 已拒绝
     */
    REJECTED(3, "已拒绝"),
    /**
     * 待审批
     */
    PENDING(4, "待审批"),
    /**
     * 已实施
     */
    IMPLEMENTED(5, "已实施"),
    /**
     * 已回滚
     */
    ROLLED_BACK(6, "已回滚"),
    /**
     * 开始变更
     */
    START_CHANGE(7, "开始变更"),
    /**
     * 通过
     */
    PASS(8, "通过"),
    /**
     * 不通过
     */
    NOT_PASS(9, "不通过"),
    /**
     * 已取消
     */
    CANCELLED(10, "已取消");

    @EnumValue
    private final Integer code;
    private final String info;

    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ProcessorStatusEnum findByCode(Integer code) {
        for (ProcessorStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static ProcessorStatusEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}

