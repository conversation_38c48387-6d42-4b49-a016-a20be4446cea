package com.cec.business.domain.vo;

import com.cec.business.domain.NetworkFreezeArea;
import com.cec.business.domain.enums.NetworkFreezeLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FreezeInfoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 封网id
     */
    private Long freezeId;
    /**
     * 封网code
     */
    private String freezeCode;
    /**
     * 标题
     */
    private String freezeTitle;
    /**
     * 等级
     */
    private NetworkFreezeLevelEnum freezeLevel;
    /**
     * 描述
     */
    private List<NetworkFreezeArea> freezeAreaList;
}
