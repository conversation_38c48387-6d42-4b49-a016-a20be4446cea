package com.cec.business.service;

import com.cec.business.domain.NetworkFreezeRecord;
import com.cec.business.domain.bo.NetworkFreezeInfoBo;
import com.cec.business.domain.bo.NetworkFreezeItemBo;
import com.cec.business.domain.bo.NetworkFreezeRecordBo;
import com.cec.business.domain.vo.FlowNodeVo;
import com.cec.business.domain.vo.NetworkFreezeInfoVo;
import com.cec.business.domain.vo.NetworkFreezeItemVo;
import com.cec.business.domain.vo.NetworkFreezeRecordVo;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 封网申请Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface INetworkFreezeService {

    /**
     * 查询封网申请
     *
     * @param id 主键
     * @return 封网申请
     */
    NetworkFreezeInfoVo queryById(Long id);

    /**
     * 分页查询封网申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 封网申请分页列表
     */
    TableDataInfo<NetworkFreezeItemVo> queryPageList(NetworkFreezeItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的封网申请列表
     *
     * @param bo 查询条件
     * @return 封网申请列表
     */
    List<NetworkFreezeInfoVo> queryList(NetworkFreezeInfoBo bo);

    /**
     * 新增封网申请
     *
     * @param bo 封网申请
     * @return 是否新增成功
     */
    Long insertByBo(NetworkFreezeInfoBo bo);

    /**
     * 修改封网申请
     *
     * @param bo 封网申请
     * @return 是否修改成功
     */
    Boolean updateByBo(NetworkFreezeInfoBo bo);

    /**
     * 校验并批量删除封网申请信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询封网申请待审批列表（个人）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 封网申请记录分页列表
     */
    TableDataInfo<NetworkFreezeRecordVo> queryPageList(NetworkFreezeRecordBo bo, PageQuery pageQuery);

    /**
     * 同意或拒绝封网申请
     *
     * @param bo 封网申请记录
     * @return 是否新增成功
     */
    Boolean updateByBo(NetworkFreezeRecordBo bo);

    /**
     * 获取当前时间及未来7天内存在的已发布状态封网申请列表
     *
     * @param date 当前日期
     * @return 封网申请列表
     */
    List<NetworkFreezeInfoVo> getListNetworkFreezeInfoVo(Date date);

    List<NetworkFreezeItemVo> queryfreezeUnfinishedList();

    NetworkFreezeRecord getRecord( Long recordId);

    List<NetworkFreezeRecordVo> listRecord(Long infoId);

    FlowNodeVo getFlowPreview( Long infoId);
}
