package com.cec.business.service.impl;

import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.cec.business.domain.bo.NetworkFreezeItemBo;
import com.cec.business.domain.vo.NetworkFreezeItemVo;
import com.cec.business.domain.NetworkFreezeItem;
import com.cec.business.mapper.NetworkFreezeItemMapper;
import com.cec.business.service.INetworkFreezeItemService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 封网申请记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RequiredArgsConstructor
@Service
public class NetworkFreezeItemServiceImpl implements INetworkFreezeItemService {

    private final NetworkFreezeItemMapper baseMapper;

    /**
     * 查询封网申请记录
     *
     * @param id 主键
     * @return 封网申请记录
     */
    @Override
    public NetworkFreezeItemVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询封网申请记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 封网申请记录分页列表
     */
    @Override
    public TableDataInfo<NetworkFreezeItemVo> queryPageList(NetworkFreezeItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<NetworkFreezeItem> lqw = buildQueryWrapper(bo);
        Page<NetworkFreezeItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的封网申请记录列表
     *
     * @param bo 查询条件
     * @return 封网申请记录列表
     */
    @Override
    public List<NetworkFreezeItemVo> queryList(NetworkFreezeItemBo bo) {
        LambdaQueryWrapper<NetworkFreezeItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<NetworkFreezeItem> buildQueryWrapper(NetworkFreezeItemBo bo) {
        LambdaQueryWrapper<NetworkFreezeItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(NetworkFreezeItem::getId);
        lqw.eq(bo.getFreezeId() != null, NetworkFreezeItem::getFreezeId, bo.getFreezeId());
        lqw.eq(StringUtils.isNotBlank(bo.getFreezeCode()), NetworkFreezeItem::getFreezeCode, bo.getFreezeCode());
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), NetworkFreezeItem::getTitle, bo.getTitle());
        lqw.eq(bo.getLevel() != null, NetworkFreezeItem::getLevel, bo.getLevel());
        lqw.eq(bo.getStage() != null, NetworkFreezeItem::getStage, bo.getStage());
        // lqw.eq(StringUtils.isNotBlank(bo.getProcessorId()), NetworkFreezeItem::getProcessorId, bo.getProcessorId());
        lqw.apply(StringUtils.isNotBlank(bo.getProcessorName()), "JSON_SEARCH(processor_list, 'one', CONCAT('%', {0}, '%'), NULL, '$[*].staffName') IS NOT NULL", bo.getProcessorName());
        return lqw;
    }

    /**
     * 新增封网申请记录
     *
     * @param bo 封网申请记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(NetworkFreezeItemBo bo) {
        NetworkFreezeItem add = MapstructUtils.convert(bo, NetworkFreezeItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改封网申请记录
     *
     * @param bo 封网申请记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(NetworkFreezeItemBo bo) {
        NetworkFreezeItem update = MapstructUtils.convert(bo, NetworkFreezeItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(NetworkFreezeItem entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除封网申请记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
