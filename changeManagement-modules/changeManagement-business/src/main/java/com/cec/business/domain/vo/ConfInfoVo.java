package com.cec.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cec.business.domain.ConfInfo;
import com.cec.business.domain.enums.ConfInfoType;
import com.cec.business.domain.enums.ConfInfoStatus;
import com.cec.common.excel.annotation.ExcelDictFormat;
import com.cec.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 配置信息视图对象 cm_conf_info
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ConfInfo.class)
public class ConfInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 类型（1-分类 2-封网地区 3- 变更地点 4-受影响设备）
     */
    @ExcelProperty(value = "类型")
    private ConfInfoType type;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 名称-英文
     */
    private String nameEn;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String description;

    /**
     * 状态（1-可用 2-非可用)
     */
    @ExcelProperty(value = "状态")
    private ConfInfoStatus status;


}
