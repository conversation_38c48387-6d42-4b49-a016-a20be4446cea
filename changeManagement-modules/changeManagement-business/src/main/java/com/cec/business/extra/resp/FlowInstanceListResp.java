package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;

/**
 * 流程实例列表响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowInstanceListResp extends BaseResp implements Serializable {
    /**
     * 响应数据
     */
    private List<FolwInstance> data;

    @Data
    public static class FolwInstance implements Serializable {
        /**
         * 流程实例记录列表
         */
        private List<Record> records;

        /**
         * 当前页码
         */
        private Integer current;

        /**
         * 是否命中
         */
        private boolean hitCount;

        /**
         * 总页数
         */
        private Integer pages;

        /**
         * 是否进行count查询
         */
        private boolean searchCount;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 总记录数
         */
        private Integer total;
    }

    @Data
    public static class Record implements Serializable {
        /**
         * 处理人名称列表
         */
        private String assigneeName;
        /**
         * 业务id
         */
        private String businessId;
        /**
         * 流程分类
         */
        private Integer category;
        /**
         * 创建人
         */
        private Integer createUser;
        /**
         * 结束时间
         */
        private Date endTime;
        /**
         * 流程自定义表单数据
         */
        private String formData;
        /**
         * 表单id
         */
        private Integer formId;
        /**
         * 表单Key
         */
        private String formKey;
        /**
         * 创建时间
         */
        private Date gmt8Create;
        /**
         * 更新时间
         */
        private Date gmt8Modified;
        /**
         * 主键
         */
        private Integer id;
        /**
         * 最后的更新人（逗号分割）
         */
        private String lastAssigneeIds;
        /**
         * 最后的更新意见
         */
        private String lastComment;
        /**
         * 流程模型id
         */
        private Integer modelId;
        /**
         * 流程实例名称
         */
        private String name;
        /**
         * 流程预览数据
         */
        private String previewData;
        /**
         * 流程预览id，关联flow_preview
         */
        private String previewId;
        /**
         * camunda中的流程定义id
         */
        private String processDefinitionId;
        /**
         * camunda中的流程实例id
         */
        private String processInstanceId;
        /**
         * 开始时间
         */
        private Date startTime;
        /**
         * 发起人id
         */
        private String starterId;
        /**
         * 发起人名称
         */
        private String starterName;
        /**
         * 发起人部门id
         */
        private String starterOrgId;
        /**
         * 发起人部门名称
         */
        private String starterOrgName;
        /**
         * 处理结果 -1等待中 0进行中 1已完成 2已拒绝 3已取消
         */
        private Integer status;
        /**
         * 租户id
         */
        private Integer tenantId;
        /**
         * 更新人
         */
        private Integer updateUser;
        /**
         * 变量值
         */
        private TreeMap<String, Object> variables;
    }
}
