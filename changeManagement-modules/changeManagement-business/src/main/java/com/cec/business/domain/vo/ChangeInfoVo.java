package com.cec.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cec.business.domain.ChangeInfo;
import com.cec.business.domain.enums.AffectedTimeTypeEnum;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.ComplexityEnum;
import com.cec.business.domain.enums.FallbackEnum;
import com.cec.business.domain.enums.ImportantUsersEnum;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.RiskLevelEnum;
import com.cec.business.domain.enums.SystemsAffectedNoEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.excel.annotation.ExcelDictFormat;
import com.cec.common.excel.convert.ExcelDictConvert;
import com.cec.system.domain.vo.UserVo;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;



/**
 * 变更申请视图对象 cm_change_info
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ChangeInfo.class)
public class ChangeInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 变更标题
     */
    @ExcelProperty(value = "变更标题")
    private String title;

    /**
     * 申请人
     */
    private UserVo requester;

    /**
     * 变更组ID
     */
    @ExcelProperty(value = "变更组ID")
    private Long teamId;

    /**
     * 变更组名
     */
    @ExcelProperty(value = "变更组名")
    private String teamName;

    /**
     * 是否紧急变更（1-是 2-否）
     */
    @ExcelProperty(value = "是否紧急变更", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-是,2=-否")
    private WhetherEnum isUrgentChange;

    /**
     * 变更类型
     */
    private String changeType;

    /**
     * 是否封网变更（1-是 2-否）
     */
    @ExcelProperty(value = "是否封网变更", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-是,2=-否")
    private WhetherEnum isNetworkFreezeChange;

    /**
     * 分类id
     */
    @ExcelProperty(value = "分类id")
    private List<String> categoryIds;

    /**
     * 分类名称
     */
    @ExcelProperty(value = "分类名称")
    private List<String> categoryNameList;

    /**
     * 应用/系统id
     */
    @ExcelProperty(value = "应用/系统id")
    private List<String> applicationIds;

    /**
     * 应用/系统名称
     */
    @ExcelProperty(value = "应用/系统名称")
    private List<String> applicationName;

    /**
     * 地点id
     */
    @ExcelProperty(value = "地点id")
    private Long locationId;

    /**
     * 地点名
     */
    @ExcelProperty(value = "地点名")
    private String locationName;

    /**
     * 相关SRC/MSP
     */
    @ExcelProperty(value = "相关SRC/MSP")
    private String src;

    /**
     * 优先级(1-高 2-中 3-低)
     */
    @ExcelProperty(value = "优先级(1-高 2-中 3-低)")
    private LevelEnum priority;

    /**
     * 计划开始时间
     */
    @ExcelProperty(value = "计划开始时间")
    private Date planTimeStart;

    /**
     * 计划结束时间
     */
    @ExcelProperty(value = "计划结束时间")
    private Date planTimeEnd;

    /**
     * 临时访问链接
     */
    @ExcelProperty(value = "临时访问链接")
    private String temporaryUrl;

    /**
     * 变更原因
     */
    @ExcelProperty(value = "变更原因")
    private String changeReason;

    /**
     * 变更描述
     */
    @ExcelProperty(value = "变更描述")
    private String changeDescription;

    /**
     * 附件地址
     */
    @ExcelProperty(value = "附件地址")
    private List<String> fileUrl;

    /**
     * 影响描述
     */
    @ExcelProperty(value = "影响描述")
    private String affectedDescription;

    /**
     * 影响时间
     */
    @ExcelProperty(value = "影响时间")
    private Integer affectedTime;

    /**
     * 影响时间类型(1-分钟 2-小时)
     */
    private AffectedTimeTypeEnum affectedTimeType;

    /**
     * 影响用户
     */
    @ExcelProperty(value = "影响用户")
    private String affectedUser;

    /**
     * 影响系统ids
     */
    @ExcelProperty(value = "影响系统ids")
    private List<String> affectedApplicationIds;

    /**
     * 影响系统名称
     */
    @ExcelProperty(value = "影响系统名称")
    private List<String> affectedApplicationName;

    /**
     * 影响设备ids
     */
    @ExcelProperty(value = "影响设备ids")
    private List<String> affectedDeviceIds;

    /**
     * 影响设备名
     */
    @ExcelProperty(value = "影响设备名")
    private List<String> affectedDeviceName;

    /**
     * 通知电邮地址
     */
    @ExcelProperty(value = "通知电邮地址")
    private List<String> notificationEmail;

    /**
     * 通知电邮抄送地址
     */
    @ExcelProperty(value = "通知电邮抄送地址")
    private List<String> notificationCcEmail;

    /**
     * Team Leader List
     */
    private List<UserVo> teamLeaderList;

    /**
     * Tester List
     */
    private List<UserVo> testerList;

    /**
     * 变更审批人List
     */
    private List<UserVo> changeApproverList;

    /**
     * 服务提交团队List
     */
    private List<UserVo> serviceDeliveryTeamList;

    /**
     * 系统所有人List
     */
    private List<UserVo> applicationOwnerList;

    /**
     * 变更实施人List
     */
    private List<UserVo> changeImplementerList;

    /**
     * 变更验证人List
     */
    private List<UserVo> changeVerifierList;

    /**
     * 变更所有者List
     */
    private List<UserVo> changeOwnerList;

    /**
     * 部门负责人List
     */
    private List<UserVo> deptLeaderList;

    /**
     * 紧急变更核查人List
     */
    private List<UserVo> urgentChangeInspectorList;

    /**
     * 受影响系统个数
     * 1-None(1分)/2-1 System(2分)/3-2 Systems(3分)/4-3 or more Systems(4分)
     */
    private SystemsAffectedNoEnum systemsAffectedNo;

    /**
     * 受影响重要用户
     * 1-None(1分)/2-Inconvenient to customer(2分)/3-Outage to 1 customer(3分)/4-Outage to 2 or more customers(4分)
     */
    private ImportantUsersEnum importantUsers;

    /**
     * 回退/回滚
     * 1-No Back Out(1分)/2-Back Out. 在维护前经过测试，并且还原措施可靠(2分)/3-Back Out. 没有进行测试，或者还原步骤会受到实际环境中的不确定因素影响(5分)/4-Fallback is not possible(6分)
     */
    private FallbackEnum fallback;

    /**
     * 变更复杂性/实施经验
     * 1-Standard technologies with good experience(1分)/2-Standard technologies with less experience(5分)/3-New technologies with Vendor support(4分)/4-New technologies without Vendor support(8分)
     */
    private ComplexityEnum complexity;

    /**
     * 风险等级(1-低 2-中 3-高 4-不可接受)
     */
    @ExcelProperty(value = "风险等级(1-低 2-中 3-高 4-不可接受)")
    private RiskLevelEnum riskLevel;

    /**
     * 风险得分
     */
    @ExcelProperty(value = "风险得分")
    private Long riskScore;

    /**
     * 需求文档
     */
    @ExcelProperty(value = "需求文档")
    private String requestDoc;

    /**
     * 需求文档附件ids
     */
    @ExcelProperty(value = "需求文档附件ids")
    private List<String> requestDocFileIds;

    /**
     * 测试文档
     */
    @ExcelProperty(value = "测试文档")
    private String testDoc;

    /**
     * 测试档附件ids
     */
    @ExcelProperty(value = "测试档附件ids")
    private List<String> testDocFileIds;

    /**
     * 代码复查
     */
    @ExcelProperty(value = "代码复查")
    private String codeReview;

    /**
     * 代码复查附件ids
     */
    @ExcelProperty(value = "代码复查附件ids")
    private List<String> codeReviewFileIds;

    /**
     * 上线计划
     */
    @ExcelProperty(value = "上线计划")
    private String rollOutPlan;

    /**
     * 回退计划
     */
    @ExcelProperty(value = "回退计划")
    private String backoutPlan;

    /**
     * 上线检查清单
     */
    @ExcelProperty(value = "上线检查清单")
    private String checkList;

    /**
     * 核查清单
     */
    @ExcelProperty(value = "核查清单")
    private String reviewCheckList;

    /**
     * 邮件内容
     */
    private String emailContent;

    /**
     * 变更编号
     */
    private String changeCode;

    /**
     * 阶段
     * 1-草稿 2-已提交 3-待审批 4-已审批 5-实施中 6-已完成 7-已拒绝 8-已回滚 9-已取消 10-待验证
     */
    private ChangeStageEnum stage;

    /**
     * 特殊备注
     */
    private String specialRemark;

    /**
     * 延期结束时间
     */
    private Date delayEndTime;

    /**
     * 封网信息
     */
    private FreezeInfoVo freezeInfo;
}
