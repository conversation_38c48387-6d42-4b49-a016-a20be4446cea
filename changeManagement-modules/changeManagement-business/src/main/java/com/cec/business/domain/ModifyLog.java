package com.cec.business.domain;

import com.cec.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 变更修改记录对象 cm_modify_log
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cm_modify_log")
public class ModifyLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 变更编号
     */
    private String changeCode;

    /**
     * 修改字段
     */
    private String modifyFiled;

    /**
     * 修改字段_zh
     */
    private String filedNameZh;

    /**
     * 修改字段_us
     */
    private String filedNameUs;

    /**
     * 修改字段_tw
     */
    private String filedNameTw;

    /**
     * 修改内容-旧
     */
    private String contentOld;

    /**
     * 修改内容-新
     */
    private String contentNew;

    /**
     * StaffId
     */
    private String staffId;

    /**
     * StaffName
     */
    private String staffName;

    /**
     * 备注
     */
    private String remark;


}
