package com.cec.business.config;

import com.cec.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * 枚举转换器配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class EnumConverterConfig implements WebMvcConfigurer {

    @Override
    public void addFormatters(FormatterRegistry registry) {
        // 注册通用的字符串到枚举的转换器工厂
        registry.addConverterFactory(new StringToEnumConverterFactory());
    }

    /**
     * 通用的字符串到枚举的转换器工厂
     * 支持所有实现了静态方法 findByCode(String) 的枚举类
     */
    public static class StringToEnumConverterFactory implements ConverterFactory<String, Enum<?>> {

        @Override
        public <T extends Enum<?>> Converter<String, T> getConverter(Class<T> targetType) {
            return new StringToEnumConverter<>(targetType);
        }

        private static class StringToEnumConverter<T extends Enum<?>> implements Converter<String, T> {
            private final Class<T> enumType;

            public StringToEnumConverter(Class<T> enumType) {
                this.enumType = enumType;
            }

            @Override
            @SuppressWarnings({"unchecked", "rawtypes"})
            public T convert(String source) {
                if (StringUtils.isBlank(source)) {
                    return null;
                }

                try {
                    // 尝试查找 findByCode(String) 方法
                    Method method = enumType.getMethod("findByCode", String.class);
                    @SuppressWarnings("unchecked")
                    T result = (T) method.invoke(null, source);
                    return result;
                } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                    try {
                        // 如果没有 findByCode(String)，尝试使用 valueOf 方法
                        // 这里需要使用强制转换来处理 Enum<?> 和 Enum<T> 之间的类型不匹配问题
                        return (T) Enum.valueOf((Class)enumType, source);
                    } catch (IllegalArgumentException ex) {
                        log.warn("无法将字符串 [{}] 转换为枚举类型 [{}]: {}", source, enumType.getName(), ex.getMessage());
                        return null;
                    }
                }
            }
        }
    }
}
