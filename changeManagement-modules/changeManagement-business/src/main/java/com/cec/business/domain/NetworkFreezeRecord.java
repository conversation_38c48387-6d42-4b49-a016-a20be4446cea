package com.cec.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.enums.NetworkFreezeLevelEnum;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.cec.system.domain.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 封网申请记录流水对象 cm_network_freeze_record
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cm_network_freeze_record", autoResultMap = true)
public class NetworkFreezeRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 变更记录ID
     */
    private Long freezeItemId;

    /**
     * 变更ID
     */
    private Long freezeId;

    /**
     * 变更编号
     */
    private String freezeCode;

    /**
     * 变更标题
     */
    private String title;

    /**
     * 封网等级(1-一级 2-二级 3-三级)
     */
    private NetworkFreezeLevelEnum level;

    /**
     * 审批状态(1-发起 2-已审批 3-已拒绝 4-待审批)
     */
    private ProcessorStatusEnum processorStatus;

    /**
     * 当前处理人
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private UserVo processor;

    /**
     * 审批时间
     */
    private Date processorTime;

    /**
     * 审批意见/备注
     */
    private String opinion;

    /**
     * 阶段(1-草稿/2-已提交/3已发布/4-已拒绝)
     */
    private NetworkFreezeStageEnum stage;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 当前任务信息
     */
    private String taskCurrent;

    /**
     * 下一节点列表
     */
    private String nextList;

    /**
     * 任务处理策略
     */
    private String strategy;

    /**
     * 会签类型（0为会签，1为或签）
     */
    private Integer countersignType;

    /**
     * 处理顺序
     */
    private Integer processOrder;

    /**
     * 节点名称
     */
    private String nodeName;
}
