package com.cec.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.business.domain.ModifyLog;
import com.cec.business.domain.bo.ModifyLogBo;
import com.cec.business.domain.vo.ModifyLogVo;
import com.cec.business.mapper.ModifyLogMapper;
import com.cec.business.service.IModifyLogService;
import com.cec.business.utils.CompareUtils;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 变更修改记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ModifyLogServiceImpl implements IModifyLogService {

    private final ModifyLogMapper modifyLogMapper;

    /**
     * 查询变更修改记录
     *
     * @param id 主键
     * @return 变更修改记录
     */
    @Override
    public ModifyLogVo queryById(Long id) {
        return modifyLogMapper.selectVoById(id);
    }

    /**
     * 分页查询变更修改记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 变更修改记录分页列表
     */
    @Override
    public TableDataInfo<ModifyLogVo> queryPageList(ModifyLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ModifyLog> lqw = new LambdaQueryWrapper<>();
        String language = getRequestLanguage();

        if (StringUtils.isNotBlank(bo.getModifyFiled())) {
            String searchValue = bo.getModifyFiled();
            switch (language) {
                case "zh_CN":
                    lqw.like(ModifyLog::getFiledNameZh, searchValue);
                    break;
                case "en_US":
                    lqw.like(ModifyLog::getFiledNameUs, searchValue);
                    break;
                case "zh_TW":
                    lqw.like(ModifyLog::getFiledNameTw, searchValue);
                    break;
                default:
                    lqw.like(ModifyLog::getFiledNameZh, searchValue);
            }
        }

        // 添加其他查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getChangeCode()), ModifyLog::getChangeCode, bo.getChangeCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStaffId()), ModifyLog::getStaffId, bo.getStaffId());
        lqw.like(StringUtils.isNotBlank(bo.getStaffName()), ModifyLog::getStaffName, bo.getStaffName());
        lqw.ge(bo.getCreateTimeStart() != null, ModifyLog::getCreateTime, bo.getCreateTimeStart());
        lqw.le(bo.getCreateTimeEnd() != null, ModifyLog::getUpdateTime, bo.getCreateTimeEnd());

        // 设置排序
        lqw.orderByDesc(ModifyLog::getId);

        Page<ModifyLogVo> result = modifyLogMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 获取当前请求的语言
     */
    private String getRequestLanguage() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String language = request.getHeader("Content-Language");
                return StringUtils.isBlank(language) ? "zh_CN" : language;
            }
        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.debug("获取请求语言失败: {}", e.getMessage());
        }
        return "zh_CN";
    }

    /**
     * 查询符合条件的变更修改记录列表
     *
     * @param bo 查询条件
     * @return 变更修改记录列表
     */
    @Override
    public List<ModifyLogVo> queryList(ModifyLogBo bo) {
        LambdaQueryWrapper<ModifyLog> lqw = new LambdaQueryWrapper<>();
        return modifyLogMapper.selectVoList(lqw);
    }

    /**
     * 新增变更修改记录
     *
     * @param bo 变更修改记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ModifyLogBo bo) {
        ModifyLog modifyLog = MapstructUtils.convert(bo, ModifyLog.class);
        return modifyLogMapper.insert(modifyLog) > 0;
    }

    /**
     * 修改变更修改记录
     *
     * @param bo 变更修改记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ModifyLogBo bo) {
        ModifyLog modifyLog = MapstructUtils.convert(bo, ModifyLog.class);
        return modifyLogMapper.updateById(modifyLog) > 0;
    }

    /**
     * 校验并批量删除变更修改记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return modifyLogMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量插入变更修改记录
     *
     * @param boList 变更修改记录列表
     * @return 是否批量插入成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsert(List<ModifyLogBo> boList) {
        if (boList == null || boList.isEmpty()) {
            return true;
        }

        List<ModifyLog> entityList = new ArrayList<>(boList.size());
        for (ModifyLogBo bo : boList) {
            ModifyLog entity = MapstructUtils.convert(bo, ModifyLog.class);
            entityList.add(entity);
        }
        return modifyLogMapper.insertBatch(entityList);
    }

    /**
     * 异步批量插入变更修改记录
     *
     * @param boList 变更修改记录列表
     * @return 异步执行结果
     */
    @Override
    @Async
    public CompletableFuture<Boolean> asyncBatchInsert(List<ModifyLogBo> boList) {
        return CompletableFuture.completedFuture(batchInsert(boList));
    }

    /**
     * 比较并记录两个对象的差异
     *
     * @param oldObj       旧对象
     * @param newObj       新对象
     * @param code         变更ID
     * @param ignoreFields 忽略比较的字段集合
     * @return 异步执行结果
     */
    @Override
    @Async
    public CompletableFuture<Boolean> compareAndLogDifferences(Object oldObj, Object newObj, String code, Set<String> ignoreFields) {
        // 调用带有LoginUser参数的方法，传入null
        return compareAndLogDifferences(oldObj, newObj, code, ignoreFields, null);
    }

    /**
     * 比较并记录两个对象的差异（包含用户信息）
     *
     * @param oldObj       旧对象
     * @param newObj       新对象
     * @param code         变更ID
     * @param ignoreFields 忽略比较的字段集合
     * @param loginUser    当前登录用户（可为null）
     * @return 异步执行结果
     */
    @Override
    @Async
    public CompletableFuture<Boolean> compareAndLogDifferences(Object oldObj, Object newObj, String code, Set<String> ignoreFields, LoginUser loginUser) {
        try {
            List<ModifyLogBo> logList = CompareUtils.compareObjects(oldObj, newObj, code, ignoreFields, loginUser);
            if (logList.isEmpty()) {
                return CompletableFuture.completedFuture(true);
            }
            return CompletableFuture.completedFuture(batchInsert(logList));
        } catch (Exception e) {
            log.error("比较并记录对象差异失败: {}", e.getMessage(), e);
            return CompletableFuture.completedFuture(false);
        }
    }
}
