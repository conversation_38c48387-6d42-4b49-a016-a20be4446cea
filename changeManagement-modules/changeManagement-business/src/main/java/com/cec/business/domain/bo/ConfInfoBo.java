package com.cec.business.domain.bo;

import com.cec.business.domain.ConfInfo;
import com.cec.business.domain.enums.ConfInfoStatus;
import com.cec.business.domain.enums.ConfInfoType;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 配置信息业务对象 cm_conf_info
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AutoMapper(target = ConfInfo.class, reverseConvertGenerate = false)
public class ConfInfoBo {

    /**
     *
     */
    @NotNull(message = "{id.not.null}", groups = {EditGroup.class})
    private Long id;

    /**
     * 类型（1-分类 2-封网地区 3-变更地点 4-受影响设备 5-维护种类）
     */
    @NotNull(message = "{conf.type.not.null}")
    private ConfInfoType type;

    /**
     * 名称
     */
    @NotNull(message = "{conf.name.not.null}",  groups = {EditGroup.class, AddGroup.class})
    @Size(max = 100, message = "{conf.name.size}")
    private String name;

    /**
     * 名称-英文（封网地区，种类维护）
     */
    @Size(max = 100, message = "{conf.name.size}")
    private String nameEn;

    /**
     * 描述
     */
    @Size(max = 200, message = "{conf.description.size}")
    private String description;

    /**
     * 状态（1-可用 2-非可用)
     */
    @NotNull(message = "{conf.status.not.null}",  groups = {EditGroup.class, AddGroup.class})
    private ConfInfoStatus status;

}
