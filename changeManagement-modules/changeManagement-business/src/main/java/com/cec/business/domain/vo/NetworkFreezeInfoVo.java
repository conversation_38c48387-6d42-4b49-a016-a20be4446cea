package com.cec.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cec.business.domain.NetworkFreezeArea;
import com.cec.business.domain.enums.NetworkFreezeLevelEnum;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.business.domain.enums.WhetherEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 封网申请视图对象 cm_network_freeze_info
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@ExcelIgnoreUnannotated
public class NetworkFreezeInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long id;

    /**
     * 封网名称
     */
    private String title;

    /**
     * 封网等级
     */
    private NetworkFreezeLevelEnum level;

    /**
     * 封网地区和时间;存json数组
     */
    private List<NetworkFreezeArea> periodAndArea;

    /**
     * 附件ids
     */
    private List<String> fileIds;

    /**
     * 是否增加到邮件附件（1-是 2否）
     */
    private WhetherEnum isAttachment;

    /**
     * 通知电邮地址
     */
    private List<String> notificationEmailTo;

    /**
     * 通知抄送电邮地址
     */
    private List<String> notificationEmailCc;

    /**
     * 特殊备注
     */
    private String specialRemark;

    /**
     * 邮件内容
     */
    private String emailContent;

    /**
     * 变更编号
     */
    private String freezeCode;

    /**
     * 封禁应用
     */
    private String freezeApps;

    /**
     * 阶段
     */
    private NetworkFreezeStageEnum stage;

}
