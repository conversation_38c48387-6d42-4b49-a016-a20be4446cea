package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 变更复杂性/实施经验枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ComplexityEnum {
    /**
     * Standard technologies with good experience
     */
    STANDARD_WITH_GOOD_EXP(1, "Standard technologies with good experience", 1),
    /**
     * Standard technologies with less experience
     */
    STANDARD_WITH_LESS_EXP(2, "Standard technologies with less experience", 5),
    /**
     * New technologies with Vendor support
     */
    NEW_WITH_VENDOR_SUPPORT(3, "New technologies with Vendor support", 4),
    /**
     * New technologies without Vendor support
     */
    NEW_WITHOUT_VENDOR_SUPPORT(4, "New technologies without Vendor support", 8);

    @EnumValue
    private final Integer code;
    private final String info;
    private final Integer score;

    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ComplexityEnum findByCode(Integer code) {
        for (ComplexityEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static ComplexityEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
} 