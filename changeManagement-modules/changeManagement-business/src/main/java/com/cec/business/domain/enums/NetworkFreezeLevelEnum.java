package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.IOException;

/**
 * 封网等级枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonDeserialize(using = NetworkFreezeLevelEnum.NetworkFreezeLevelEnumDeserializer.class)
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum NetworkFreezeLevelEnum {
    /**
     * 一级
     */
    LEVEL_ONE(1, "Level-1","一级"),
    /**
     * 二级
     */
    LEVEL_TWO(2, "Level-2","二级"),
    /**
     * 三级
     */
    LEVEL_THREE(3, "Level-3","三级");

    @EnumValue
    private final Integer code;
    private final String info;
    private final String infoCn;
    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static NetworkFreezeLevelEnum findByCode(Integer code) {
        for (NetworkFreezeLevelEnum level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static NetworkFreezeLevelEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 自定义反序列化器，支持从对象和整数值中反序列化
     */
    public static class NetworkFreezeLevelEnumDeserializer extends JsonDeserializer<NetworkFreezeLevelEnum> {
        @Override
        public NetworkFreezeLevelEnum deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            JsonNode node = parser.getCodec().readTree(parser);
            // 如果是对象节点，尝试从code字段获取值
            if (node.isObject()) {
                JsonNode codeNode = node.get("code");
                if (codeNode != null && codeNode.isInt()) {
                    return findByCode(codeNode.asInt());
                }
            }
            // 如果是整数节点，直接使用值
            else if (node.isInt()) {
                return findByCode(node.asInt());
            }
            // 如果是字符串节点，尝试解析为整数
            else if (node.isTextual()) {
                return findByCode(node.asText());
            }
            return null;
        }
    }
}
