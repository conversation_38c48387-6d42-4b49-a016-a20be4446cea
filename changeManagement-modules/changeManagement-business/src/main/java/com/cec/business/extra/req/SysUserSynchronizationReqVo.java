package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 用户同步请求对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysUserSynchronizationReqVo extends BaseReq {

    /**
     * 同步类型 （1：增量同步（对已有的数据进行更新，不存在的数据进行新增） 2：全量同步（先删除在新增））
     */
    private Integer syncType;

    /**
     * 同步数据
     */
    @NotEmpty(message = "同步数据不能为空")
    private List<SysUserSyncReqVo> sysUserSyncReqVoList;

    @Override
    public String method() {
        return "/sysDataSync/user";
    }

    /**
     * 用户同步数据项
     */
    @Data
    public static class SysUserSyncReqVo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 业务线（0: CPC 1: CEC）
         */
        private Integer bizLine;

        /**
         * 邮箱(和手机号至少一个)
         */
        private String email;

        /**
         * 用户外部唯一Id(必填)
         */
        private String externalId;

        /**
         * 部门外部唯一Id（必填）
         */
        private String externalOrgId;

        /**
         * 区域外部唯一Id(必填)
         */
        private String externalRegionId;

        /**
         * 职工头衔
         */
        private String jobTitle;

        /**
         * 金蝶公司ID
         */
        private Long kdCompanyId;

        /**
         * 金蝶公司名称
         */
        private String kdCompanyName;

        /**
         * 直属上级的编号(必填)
         */
        private String managerNum;

        /**
         * 手机号(和邮箱必填一个)
         */
        private String mobilePhone;

        /**
         * 组织机构名称（必填）
         */
        private String orgChartName;

        /**
         * 区域名称(必填)
         */
        private String region;

        /**
         * 备注信息
         */
        private String remark;

        /**
         * 职工姓名(必填)
         */
        private String staffName;

        /**
         * 职工编号(必填)
         */
        private String staffNum;

        /**
         * 员工状态 514离职 512在职
         */
        private Long versionId;
    }
}
