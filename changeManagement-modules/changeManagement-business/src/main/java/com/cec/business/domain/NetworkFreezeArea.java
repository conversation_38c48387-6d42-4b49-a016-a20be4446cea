package com.cec.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 封网删除 cm_network_freeze_area
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@TableName(value = "cm_network_freeze_area", autoResultMap = true)
public class NetworkFreezeArea {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 封网ID
     */
    private Long freezeId;

    /**
     * 封网地区ID
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> areaId;

    /**
     * 封网地区名称
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> areaName;

    /**
     * 封网地区名称-英文
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> areaNameEn;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
