package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.TreeMap;

/**
 * 流程实例列表响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowInstanceResp extends BaseResp implements Serializable {
    /**
     * 响应数据
     */
    private Record data;

    @Data
    public static class Record implements Serializable {
        /**
         * 处理人名称列表
         */
        private String assigneeName;
        /**
         * 业务id
         */
        private String businessId;
        /**
         * 流程分类
         */
        private Integer category;
        /**
         * 创建人
         */
        private Long createUser;
        /**
         * 结束时间
         */
        private Date endTime;
        /**
         * 流程自定义表单数据
         */
        private String formData;
        /**
         * 表单id
         */
        private Long formId;
        /**
         * 表单Key
         */
        private String formKey;
        /**
         * 创建时间
         */
        private Date gmt8Create;
        /**
         * 更新时间
         */
        private Date gmt8Modified;
        /**
         * 主键
         */
        private Long id;
        /**
         * 最后的更新人（逗号分割）
         */
        private String lastAssigneeIds;
        /**
         * 最后的更新意见
         */
        private String lastComment;
        /**
         * 流程模型id
         */
        private Long modelId;
        /**
         * 流程实例名称
         */
        private String name;
        /**
         * 流程预览数据
         */
        private String previewData;
        /**
         * 流程预览id，关联flow_preview
         */
        private String previewId;
        /**
         * camunda中的流程定义id
         */
        private String processDefinitionId;
        /**
         * camunda中的流程实例id
         */
        private String processInstanceId;
        /**
         * 开始时间
         */
        private Date startTime;
        /**
         * 发起人id
         */
        private String starterId;
        /**
         * 发起人名称
         */
        private String starterName;
        /**
         * 发起人部门id
         */
        private String starterOrgId;
        /**
         * 发起人部门名称
         */
        private String starterOrgName;
        /**
         * 处理结果 -1等待中 0进行中 1已完成 2已拒绝 3已取消
         */
        private Integer status;
        /**
         * 更新人
         */
        private Long updateUser;
        /**
         * 变量值
         */
        private String variables;
    }
}
