package com.cec.business.handler;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.List;

/**
 * 安全的List<Long>序列化器，可以处理混合类型的情况
 *
 * <AUTHOR>
 */
public class SafeListLongSerializer extends JsonSerializer<List<?>> {

    @Override
    public void serialize(List<?> value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeStartArray();
        if (value != null) {
            for (Object item : value) {
                if (item == null) {
                    gen.writeNull();
                } else if (item instanceof Number) {
                    gen.writeNumber(((Number) item).longValue());
                } else {
                    try {
                        // 尝试将字符串值转换为长整型
                        gen.writeNumber(Long.parseLong(item.toString()));
                    } catch (NumberFormatException e) {
                        // 如果转换失败，将其作为字符串写出
                        gen.writeString(item.toString());
                    }
                }
            }
        }
        gen.writeEndArray();
    }
}
