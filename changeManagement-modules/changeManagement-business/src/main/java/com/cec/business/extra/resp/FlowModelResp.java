package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 流程模型响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowModelResp extends BaseResp implements Serializable {

    /**
     * 响应数据
     */
    private FlowModel data;

    @Data
    public static class FlowModel implements Serializable {
        /**
         * 记录列表
         */
        private List<Records> records;

        /**
         * 当前页码
         */
        private Integer current;

        /**
         * 是否命中
         */
        private boolean hitCount;

        /**
         * 总页数
         */
        private Integer pages;

        /**
         * 是否进行count查询
         */
        private boolean searchCount;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 总记录数
         */
        private Integer total;
    }

    @Data
    private static class Records implements Serializable {
        /**
         * 流程定义JSON
         */
        private String bpmnJson;

        /**
         * 流程定义JSON草稿
         */
        private String bpmnJsonDraft;

        /**
         * 流程定义XML
         */
        private String bpmnXml;

        /**
         * 流程定义XML草稿
         */
        private String bpmnXmlDraft;

        /**
         * 流程分类
         */
        private Integer category;

        /**
         * 创建人
         */
        private Integer createUser;

        /**
         * 是否可委托 0否1是
         */
        private Integer delegate;

        /**
         * 流程部署id
         */
        private String deploymentId;

        /**
         * 描述
         */
        private String description;

        /**
         * 表单id，关联flow_form
         */
        private Integer formId;

        /**
         * 表单Key，关联flow_form
         */
        private String formKey;

        /**
         * 表单名称，关联flow_form
         */
        private String formName;

        /**
         * 创建时间
         */
        private Date gmt8Create;

        /**
         * 主键
         */
        private Integer id;

        /**
         * 名称
         */
        private String name;

        /**
         * camunda中的流程定义id
         */
        private String processDefinitionId;

        /**
         * camunda中的流程定义key
         */
        private String processDefinitionKey;

        /**
         * 部署状态 0:未部署 1:激活 2:挂起
         */
        private Integer status;

        /**
         * 租户id
         */
        private Integer tenantId;

        /**
         * 版本
         */
        private Integer version;
    }
}
