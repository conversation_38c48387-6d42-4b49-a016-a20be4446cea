package com.cec.business.service;

import com.cec.business.domain.bo.ChangeRecordBo;
import com.cec.business.domain.vo.CalendarEventVo;
import com.cec.business.domain.vo.ChangeDailyListVo;
import com.cec.business.domain.vo.ChangeItemVo;
import com.cec.business.domain.vo.ChangeRecordVo;
import com.cec.business.domain.vo.NetworkFreezeInfoVo;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;

import java.util.Date;
import java.util.List;

/**
 * Dashboard服务接口
 */
public interface IDashboardService {

    /**
     * 获取封网信息
     *
     * @param date 日期
     * @return 封网信息列表
     */
    List<NetworkFreezeInfoVo> getNetworkFreezeInfo(Date date);

    /**
     * 获取未完结变更
     *
     * @param pageQuery 分页参数
     * @return 未完结变更列表
     */
    TableDataInfo<ChangeItemVo> getChangeUnFinished(PageQuery pageQuery);

    /**
     * 获取待办列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 待办列表
     */
    TableDataInfo<ChangeRecordVo> getChangeTodo(ChangeRecordBo bo, PageQuery pageQuery);

    /**
     * 获取日历事件数据
     *
     * @param yearMonth 年月，格式为yyyyMM，为空时默认为当前年月
     * @return 日历事件列表
     */
    List<CalendarEventVo> getCalendarEvents(String yearMonth);

    /**
     * 获取特定日期的变更列表数据
     *
     * @param date 日期，格式为yyyy-MM-dd
     * @return 变更列表数据
     */
    List<ChangeDailyListVo> getChangeDailyList(String date);
}
