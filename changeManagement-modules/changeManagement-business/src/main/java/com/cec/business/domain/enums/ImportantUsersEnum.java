package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 受影响重要用户枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ImportantUsersEnum {
    /**
     * None
     */
    NONE(1, "None", 1),
    /**
     * Inconvenient to customer
     */
    INCONVENIENT_TO_CUSTOMER(2, "Inconvenient to customer", 2),
    /**
     * Outage to 1 customer
     */
    OUTAGE_TO_ONE_CUSTOMER(3, "Outage to 1 customer", 3),
    /**
     * Outage to 2 or more customers
     */
    OUTAGE_TO_TWO_OR_MORE_CUSTOMERS(4, "Outage to 2 or more customers", 4);

    @EnumValue
    private final Integer code;
    private final String info;
    private final Integer score;

    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ImportantUsersEnum findByCode(Integer code) {
        for (ImportantUsersEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static ImportantUsersEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
} 