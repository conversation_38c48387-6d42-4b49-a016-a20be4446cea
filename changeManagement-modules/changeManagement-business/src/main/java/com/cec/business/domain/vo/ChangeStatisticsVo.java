package com.cec.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.system.domain.vo.UserVo;
import lombok.Data;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 变更统计
 */
@Data
public class ChangeStatisticsVo {

    /**
     * 总数
     */
    private Integer totalCount;

    /**
     * 成功率
     */
    private String successRate;

    /**
     * 平均处理时常/秒
     */
    private String avgProcessTime;

    /**
     * 数据
     */
    TableDataInfo<ChangeItemVo> data;

    @Data
    public static class ChangeItemVo{
        /**
         *
         */
        @TableId(value = "id")
        @ExcelIgnore
        private Long id;

        /**
         * 变更ID
         */
        @ExcelIgnore
        private Long changeId;

        /**
         * 变更编号
         */
        @ExcelProperty("Change Code")
        private String changeCode;

        /**
         * 变更标题
         */
        @ExcelProperty("Title")
        private String title;

        /**
         * 阶段
         * 1-草稿 2-已提交 3-待审批 4-已审批 5-实施中 6-已完成 7-已拒绝 8-已回滚 9-已取消 10-待验证
         */
        @ExcelProperty(value = "Stage")
        private ChangeStageEnum stage;

        /**
         * 当前处理人
         */
        @ExcelIgnore
        private List<UserVo> processorList;

        /**
         * 变更组ID
         */
        @ExcelIgnore
        private Long teamId;

        /**
         * 变更组名
         */
        @ExcelProperty("Team Name")
        private String teamName;

        /**
         * 是否紧急变更（1-是 2-否）
         */
        @ExcelProperty(value = "Is Urgent Change")
        private WhetherEnum isUrgentChange;

        /**
         * 优先级(1-高 2-中 3-低)
         */
        @ExcelProperty(value = "Priority")
        private LevelEnum priority;

        /**
         * 流程实例id
         */
        @ExcelIgnore
        private String instanceId;

        /**
         * smartFlow Variables
         */
        @ExcelIgnore
        private String variables;

        /**
         * 完成时间
         */
        @ExcelIgnore
        private Date completeTime;

        /**
         * 变更耗时（原始秒数）
         */
        @ExcelIgnore
        private String processTime;

        /**
         * 变更耗时（格式化）
         */
        @ExcelProperty("Process Time")
        private String formattedProcessTime;

        public String getFormattedProcessTime() {
            try {
                if (processTime == null || processTime.isEmpty()) {
                    return "";
                }

                // 获取当前语言环境
                Locale locale = LocaleContextHolder.getLocale();
                MessageSource messageSource = SpringUtils.getBean(MessageSource.class);

                // 获取国际化资源
                String hourText = messageSource.getMessage("common.time.hour", null, "小时", locale);
                String minuteText = messageSource.getMessage("common.time.minute", null, "分钟", locale);
                String secondText = messageSource.getMessage("common.time.second", null, "秒", locale);

                long seconds = Long.parseLong(processTime);
                if (seconds < 60) {
                    return seconds + secondText;
                }

                long hours = seconds / 3600;
                long minutes = (seconds % 3600) / 60;
                long remainingSeconds = seconds % 60;

                StringBuilder result = new StringBuilder();
                if (hours > 0) {
                    result.append(hours).append(hourText);
                }
                if (minutes > 0 || hours > 0) {
                    result.append(minutes).append(minuteText);
                }
                result.append(remainingSeconds).append(secondText);

                return result.toString();
            } catch (Exception e) {
                return processTime;
            }
        }

    }
}
