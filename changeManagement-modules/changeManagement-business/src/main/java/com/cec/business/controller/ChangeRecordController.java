//package com.cec.business.controller;
//
//import java.util.List;
//
//import lombok.RequiredArgsConstructor;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.constraints.*;
//import cn.dev33.satoken.annotation.SaCheckPermission;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.validation.annotation.Validated;
//import com.cec.common.idempotent.annotation.RepeatSubmit;
//import com.cec.common.log.annotation.Log;
//import com.cec.common.web.core.BaseController;
//import com.cec.common.mybatis.core.page.PageQuery;
//import com.cec.common.core.domain.R;
//import com.cec.common.core.validate.AddGroup;
//import com.cec.common.core.validate.EditGroup;
//import com.cec.common.log.enums.BusinessType;
//import com.cec.common.excel.utils.ExcelUtil;
//import com.cec.business.domain.vo.ChangeRecordVo;
//import com.cec.business.domain.bo.ChangeRecordBo;
//import com.cec.business.service.IChangeRecordService;
//import com.cec.common.mybatis.core.page.TableDataInfo;
//
///**
// * 变更申请记录流水
// *
// * <AUTHOR>
// * @date 2025-05-13
// */
//@Validated
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/business/changeRecord")
//public class ChangeRecordController extends BaseController {
//
//    private final IChangeRecordService changeRecordService;
//
//    /**
//     * 查询变更申请记录流水列表
//     */
//    @SaCheckPermission("business:changeRecord:list")
//    @GetMapping("/list")
//    public TableDataInfo<ChangeRecordVo> list(ChangeRecordBo bo, PageQuery pageQuery) {
//        return changeRecordService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出变更申请记录流水列表
//     */
//    @SaCheckPermission("business:changeRecord:export")
//    @Log(title = "变更申请记录流水", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(ChangeRecordBo bo, HttpServletResponse response) {
//        List<ChangeRecordVo> list = changeRecordService.queryList(bo);
//        ExcelUtil.exportExcel(list, "变更申请记录流水", ChangeRecordVo.class, response);
//    }
//
//    /**
//     * 获取变更申请记录流水详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("business:changeRecord:query")
//    @GetMapping("/{id}")
//    public R<ChangeRecordVo> getInfo(@NotNull(message = "主键不能为空")
//                                     @PathVariable Long id) {
//        return R.ok(changeRecordService.queryById(id));
//    }
//
//    /**
//     * 新增变更申请记录流水
//     */
//    @SaCheckPermission("business:changeRecord:add")
//    @Log(title = "变更申请记录流水", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody ChangeRecordBo bo) {
//        return toAjax(changeRecordService.insertByBo(bo));
//    }
//
//    /**
//     * 修改变更申请记录流水
//     */
//    @SaCheckPermission("business:changeRecord:edit")
//    @Log(title = "变更申请记录流水", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ChangeRecordBo bo) {
//        return toAjax(changeRecordService.updateByBo(bo));
//    }
//
//    /**
//     * 删除变更申请记录流水
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("business:changeRecord:remove")
//    @Log(title = "变更申请记录流水", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//                          @PathVariable Long[] ids) {
//        return toAjax(changeRecordService.deleteWithValidByIds(List.of(ids), true));
//    }
//}
