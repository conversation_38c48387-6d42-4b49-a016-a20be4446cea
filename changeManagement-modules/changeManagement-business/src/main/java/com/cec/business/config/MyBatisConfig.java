package com.cec.business.config;

import com.cec.business.handler.JsonListLongTypeHandler;
import com.cec.business.handler.JsonListStringTypeHandler;
import jakarta.annotation.PostConstruct;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * MyBatis配置类
 *
 * <AUTHOR>
 */
@Configuration
public class MyBatisConfig {

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    /**
     * 注册自定义的TypeHandler
     */
    @PostConstruct
    public void registerTypeHandler() {
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            TypeHandlerRegistry typeHandlerRegistry = sqlSessionFactory.getConfiguration().getTypeHandlerRegistry();

            typeHandlerRegistry.register(java.util.List.class, JsonListLongTypeHandler.class);
            typeHandlerRegistry.register(java.util.List.class, JsonListStringTypeHandler.class);

        }
    }
}
