package com.cec.business.service;

import cn.hutool.json.JSONUtil;
import com.cec.business.extra.SmartFlowUtils;
import com.cec.business.extra.req.FlowInstanceCancelReq;
import com.cec.business.extra.req.FlowInstanceStartReq;
import com.cec.business.extra.req.FlowTaskApproveReq;
import com.cec.business.extra.req.FlowTaskBackListReq;
import com.cec.business.extra.req.FlowTaskBackReq;
import com.cec.business.extra.req.FlowTaskCountersignReq;
import com.cec.business.extra.req.FlowTaskCurrentReq;
import com.cec.business.extra.req.FlowTaskHandlerReqVO;
import com.cec.business.extra.req.FlowTaskNextListReq;
import com.cec.business.extra.req.FlowTaskRejectReq;
import com.cec.business.extra.req.FlowTaskStrategyReq;
import com.cec.business.extra.resp.FlowInstanceBaseResp;
import com.cec.business.extra.resp.FlowInstanceStartResp;
import com.cec.business.extra.resp.FlowTaskCurrentResp;
import com.cec.business.extra.resp.FlowTaskNextListResp;
import com.cec.business.extra.resp.FlowTaskStrategyResp;
import com.cec.common.core.config.CecSmartFlowConfig;
import com.cec.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;

/**
 * 封装对SmartFlow流程引擎的调用
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmartFlowService {

    private final SmartFlowUtils smartFlowUtils;
    private final CecSmartFlowConfig cecSmartFlowConfig;

    private final static Integer SUCCESS_CODE = 200;

    /**
     * 获取当前任务
     */
    public FlowTaskCurrentResp getCurrentTask(String instanceId, String staffId) {
        FlowTaskCurrentReq req = new FlowTaskCurrentReq();
        req.setId(Long.valueOf(instanceId));
        req.setAssigneeId(staffId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        return checkResponse(smartFlowUtils.getFlowTaskCurrent(req));
    }

    /**
     * 获取下一节点列表
     */
    public FlowTaskNextListResp getNextList(String staffId, TreeMap<String, Object> variables) {
        FlowTaskNextListReq req = new FlowTaskNextListReq();
        req.setAssigneeId(staffId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setFormKey(cecSmartFlowConfig.getParam().getFormKey());
        req.setVariables(variables);
        return checkResponse(smartFlowUtils.nextList(req));
    }

    /**
     * 获取任务下一节点列表
     */
    public FlowTaskNextListResp getTaskNextList(String staffId, String taskId, TreeMap<String, Object> variables) {
        FlowTaskNextListReq req = new FlowTaskNextListReq();
        req.setAssigneeId(staffId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setFormKey(cecSmartFlowConfig.getParam().getFormKey());
        req.setId(Long.valueOf(taskId));
        req.setVariables(variables);
        return checkResponse(smartFlowUtils.nextList(req));
    }

    /**
     * 获取任务处理策略
     */
    public FlowTaskStrategyResp getTaskStrategy(String staffId, String taskId) {
        FlowTaskStrategyReq req = new FlowTaskStrategyReq();
        req.setAssigneeId(staffId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setFormKey(cecSmartFlowConfig.getParam().getFormKey());
        req.setId(taskId);
        return checkResponse(smartFlowUtils.strategy(req));
    }

    /**
     * 启动流程实例
     */
    public String startFlow(String businessId, String title, String userId, String userName,
                            List<FlowInstanceStartResp.FlowInstanceNode> nodeList, TreeMap<String, Object> variables) {
        FlowInstanceStartReq req = new FlowInstanceStartReq();
        req.setName(title);
        req.setBusinessId(businessId);
        req.setFormKey(cecSmartFlowConfig.getParam().getFormKey());
        req.setUserId(userId);
        req.setUserName(userName);
        req.setUserOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setUserOrgName(cecSmartFlowConfig.getParam().getDeptName());
        req.setNodeList(nodeList);
        req.setVariables(variables);

        FlowInstanceBaseResp resp = checkResponse(smartFlowUtils.startFlow(req));
        return resp.getData();
    }

    /**
     * 任务审批通过
     */
    public void approveTask(String taskId, String title, String assigneeId, String nodeId,
                            String nodeName, String comment, TreeMap<String, Object> variables,
                            List<FlowInstanceStartResp.FlowInstanceNode> nodeList) {
        FlowTaskApproveReq req = new FlowTaskApproveReq();
        req.setName(title);
        req.setId(Long.valueOf(taskId));
        req.setAssigneeId(assigneeId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setNodeId(nodeId);
        req.setNodeName(nodeName);
        // req.setComment(comment);
        req.setVariables(variables);
        req.setNodeList(nodeList);

        checkResponse(smartFlowUtils.approve(req));
    }

    /**
     * 任务审批拒绝
     */
    public void rejectTask(String taskId, String assigneeId, String comment) {
        FlowTaskRejectReq req = new FlowTaskRejectReq();
        req.setId(Long.valueOf(taskId));
        req.setAssigneeId(assigneeId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        // req.setComment(comment);

        checkResponse(smartFlowUtils.reject(req));
    }

    /**
     * 会签任务
     */
    public void countersignTask(String taskId, String assigneeId, List<FlowTaskHandlerReqVO> countersignList, int type) {
        FlowTaskCountersignReq req = new FlowTaskCountersignReq();
        req.setAssigneeId(assigneeId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setCountersignList(countersignList);
        req.setId(Long.valueOf(taskId));
        req.setType(type);

        checkResponse(smartFlowUtils.countersign(req));
    }

    /**
     * 创建流程实例处理人
     */
    public FlowInstanceStartResp.FlowInstanceHandler createInstanceHandler(FlowTaskNextListResp.Candidate candidate) {
        FlowInstanceStartResp.FlowInstanceHandler handler = new FlowInstanceStartResp.FlowInstanceHandler();
        handler.setAssigneeId(candidate.getAssigneeId());
        handler.setAssigneeName(candidate.getAssigneeName());
        handler.setAssigneeOrgId(candidate.getAssigneeOrgId());
        handler.setAssigneeOrgName(candidate.getAssigneeOrgName());
        return handler;
    }

    /**
     * 检查API响应状态
     *
     * @param response API响应对象
     * @param <T>      响应类型
     * @return 校验通过的响应对象
     */
    private <T> T checkResponse(T response) {
        if (response == null) {
            throw new ServiceException("调用流程引擎失败，返回结果为空");
        }

        try {
            // 通过反射获取code和success属性值
            Integer code = (Integer) response.getClass().getMethod("getCode").invoke(response);
            Boolean success = (Boolean) response.getClass().getMethod("isSuccess").invoke(response);

            if (code == null || !code.equals(SUCCESS_CODE) || !success) {
                // 尝试获取错误消息
                String message = getErrorMessage(response);
                throw new ServiceException(message);
            }

            return response;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException("调用流程引擎失败，无法验证返回结果: " + e.getMessage());
        }
    }

    /**
     * 获取错误消息
     */
    private <T> String getErrorMessage(T response) {
        try {
            return (String) response.getClass().getMethod("getMessage").invoke(response);
        } catch (Exception ex) {
            return "调用流程引擎失败，状态异常";
        }
    }

    /**
     * 从JSON字符串解析FlowTaskCurrentResp对象
     */
    public FlowTaskCurrentResp parseTaskCurrent(String jsonStr) {
        return JSONUtil.toBean(jsonStr, FlowTaskCurrentResp.class);
    }

    /**
     * 从JSON字符串解析FlowTaskNextListResp对象
     */
    public FlowTaskNextListResp parseNextList(String jsonStr) {
        return JSONUtil.toBean(jsonStr, FlowTaskNextListResp.class);
    }

    /**
     * 根据任务ID获取策略
     *
     * @param staffId 处理人ID
     * @param taskId  任务ID
     * @return 策略响应
     */
    public FlowTaskStrategyResp getStrategy(String staffId, String taskId) {
        FlowTaskStrategyReq req = new FlowTaskStrategyReq();
        req.setAssigneeId(staffId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setFormKey(cecSmartFlowConfig.getParam().getFormKey());
        req.setId(taskId);
        return strategy(req);
    }

    /**
     * 根据任务ID获取下一节点列表
     *
     * @param staffId   处理人ID
     * @param taskId    任务ID
     * @param variables 变量
     * @return 下一节点列表响应
     */
    public FlowTaskNextListResp getNextList(String staffId, String taskId, TreeMap<String, Object> variables) {
        FlowTaskNextListReq req = new FlowTaskNextListReq();
        req.setAssigneeId(staffId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setFormKey(cecSmartFlowConfig.getParam().getFormKey());
        req.setId(Long.valueOf(taskId));
        req.setVariables(variables);
        return nextList(req);
    }

    /**
     * 审批通过任务
     *
     * @param taskId     任务ID
     * @param title      标题
     * @param assigneeId 处理人ID
     * @param nodeId     节点ID
     * @param nodeName   节点名称
     * @param comment    审批意见
     * @param variables  变量
     * @param nodeList   节点列表
     */
    public void approve(String taskId, String title, String assigneeId, String nodeId,
                        String nodeName, String comment, TreeMap<String, Object> variables,
                        List<FlowInstanceStartResp.FlowInstanceNode> nodeList) {
        FlowTaskApproveReq req = new FlowTaskApproveReq();
        req.setName(title);
        req.setId(Long.valueOf(taskId));
        req.setAssigneeId(assigneeId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setNodeId(nodeId);
        req.setNodeName(nodeName);
        // req.setComment(comment);
        req.setVariables(variables);
        req.setNodeList(nodeList);
        approve(req);
    }

    /**
     * 拒绝任务
     *
     * @param taskId     任务ID
     * @param assigneeId 处理人ID
     * @param comment    拒绝意见
     */
    public void reject(String taskId, String assigneeId, String comment) {
        FlowTaskRejectReq req = new FlowTaskRejectReq();
        req.setId(Long.valueOf(taskId));
        req.setAssigneeId(assigneeId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        // req.setComment(comment);
        reject(req);
    }

    /**
     * 对任务进行会签
     *
     * @param taskId          任务ID
     * @param assigneeId      处理人ID
     * @param countersignList 会签人列表
     * @param type            会签类型
     */
    public void countersign(String taskId, String assigneeId, List<FlowTaskHandlerReqVO> countersignList, int type) {
        FlowTaskCountersignReq req = new FlowTaskCountersignReq();
        req.setAssigneeId(assigneeId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setCountersignList(countersignList);
        req.setId(Long.valueOf(taskId));
        req.setType(type);
        checkResponse(smartFlowUtils.countersign(req));
    }

    /**
     * 调用smartFlowUtils.nextList
     */
    public FlowTaskNextListResp nextList(FlowTaskNextListReq req) {
        return checkResponse(smartFlowUtils.nextList(req));
    }

    /**
     * 调用smartFlowUtils.strategy
     */
    public FlowTaskStrategyResp strategy(FlowTaskStrategyReq req) {
        return checkResponse(smartFlowUtils.strategy(req));
    }

    /**
     * 调用smartFlowUtils.approve
     */
    public void approve(FlowTaskApproveReq req) {
        checkResponse(smartFlowUtils.approve(req));
    }

    /**
     * 调用smartFlowUtils.reject
     */
    public void reject(FlowTaskRejectReq req) {
        checkResponse(smartFlowUtils.reject(req));
    }

    /**
     * 回滚流程到IMPLEMENTING阶段
     *
     * @param taskId     任务ID
     * @param assigneeId 处理人ID
     * @param comment    回滚意见
     */
    public void rollback(String taskId, String assigneeId, String comment) {
        FlowTaskBackReq req = new FlowTaskBackReq();
        req.setId(Long.valueOf(taskId));
        req.setAssigneeId(assigneeId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        // req.setComment(comment);
        checkResponse(smartFlowUtils.back(req));
    }

    /**
     * 查询待回退节点列表
     *
     * @param staffId 处理人ID
     * @param taskId  任务ID
     * @return 待回退节点列表
     */
    public FlowTaskNextListResp getBackList(String staffId, String taskId) {
        FlowTaskBackListReq req = new FlowTaskBackListReq();
        req.setAssigneeId(staffId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setId(Long.valueOf(taskId));
        return checkResponse(smartFlowUtils.backList(req));
    }

    /**
     * 待回退节点流程回退
     *
     * @param taskId     任务ID
     * @param assigneeId 处理人ID
     * @param comment    回退意见
     * @param targetNode 目标节点
     */
    public void backToNode(String taskId, String assigneeId, String comment, FlowTaskNextListResp.FlowTaskNext targetNode) {
        FlowTaskBackReq req = new FlowTaskBackReq();
        req.setId(Long.valueOf(taskId));
        req.setAssigneeId(assigneeId);
        req.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
        req.setComment(comment);
        req.setNodeId(targetNode.getId());
        req.setNodeName(targetNode.getName());

        FlowInstanceStartResp.FlowInstanceNode node = new FlowInstanceStartResp.FlowInstanceNode();
        node.setNodeId(targetNode.getId());
        node.setNodeName(targetNode.getName());

        // 设置处理人列表
        List<FlowInstanceStartResp.FlowInstanceHandler> handlerList = new ArrayList<>();
        if (targetNode.getCandidateList() != null) {
            for (FlowTaskNextListResp.Candidate handler : targetNode.getCandidateList()) {
                handlerList.add(createInstanceHandler(handler));
            }
        }

        node.setHandlerList(handlerList);
        req.setNode(node);
        checkResponse(smartFlowUtils.back(req));
    }

    /**
     * 取消流程实例
     *
     * @param instanceId 流程实例ID
     * @param userId     用户ID
     * @param comment    取消原因
     */
    public void cancelFlow(String instanceId, String userId, String comment) {
        FlowInstanceCancelReq req = new FlowInstanceCancelReq();
        req.setId(Long.valueOf(instanceId));
        req.setUserId(userId);
        // req.setComment(comment);

        checkResponse(smartFlowUtils.cancelFlow(req));
    }
}
