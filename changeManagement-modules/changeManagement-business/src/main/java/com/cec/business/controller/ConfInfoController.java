package com.cec.business.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.cec.business.domain.bo.ConfInfoBo;
import com.cec.business.domain.enums.ConfInfoType;
import com.cec.business.domain.vo.ConfInfoVo;
import com.cec.business.service.IConfInfoService;
import com.cec.common.core.domain.R;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.core.validate.QueryGroup;
import com.cec.common.idempotent.annotation.RepeatSubmit;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.log.enums.LogTitleEnum;
import com.cec.common.web.core.BaseController;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 配置信息
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/confInfo")
public class ConfInfoController extends BaseController {

    private final IConfInfoService confInfoService;

    /**
     * 查询配置信息列表
     */
    @GetMapping("/list")
    public TableDataInfo<ConfInfoVo> list(@Validated(QueryGroup.class) ConfInfoBo bo, PageQuery pageQuery) {
        checkPermission(bo.getType(),"list");
        return confInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取配置信息详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<ConfInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        ConfInfoVo confInfoVo = confInfoService.queryById(id);
        checkPermission(confInfoVo.getType(),"detail");
        return R.ok(confInfoVo);
    }

    /**
     * 新增配置信息
     */
    @Log(title = LogTitleEnum.CONFIG_MANAGEMENT, businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ConfInfoBo bo) {
        checkPermission(bo.getType(),"edit");
        if (!confInfoService.checkNameUnique(bo)){
            String messageKey = switch (bo.getType()) {
                case CLASSIFICATION -> "conf.name.duplicate.classification";
                case NETWORK_BLOCKING_AREA -> "conf.name.duplicate.network.blocking.area";
                case CHANGE_LOCATION -> "conf.name.duplicate.change.location";
                case AFFECTED_DEVICE -> "conf.name.duplicate.affected.device";
                case MAINTENANCE_KIND -> "conf.name.duplicate.maintenance.kind";
            };
            String message = MessageUtils.message(messageKey);
            throw new ServiceException(message);
        }
        return toAjax(confInfoService.insertByBo(bo));
    }

    /**
     * 修改配置信息
     */
    @Log(title = LogTitleEnum.CONFIG_MANAGEMENT, businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ConfInfoBo bo) {
        checkPermission(bo.getType(),"edit");
        if (!confInfoService.checkNameUnique(bo)){
            String messageKey = switch (bo.getType()) {
                case CLASSIFICATION -> "conf.name.duplicate.classification";
                case NETWORK_BLOCKING_AREA -> "conf.name.duplicate.network.blocking.area";
                case CHANGE_LOCATION -> "conf.name.duplicate.change.location";
                case AFFECTED_DEVICE -> "conf.name.duplicate.affected.device";
                case MAINTENANCE_KIND -> "conf.name.duplicate.maintenance.kind";
            };
            String message = MessageUtils.message(messageKey);
            throw new ServiceException(message);
        }
        return toAjax(confInfoService.updateByBo(bo));
    }

    /**
     * 删除配置信息
     *
     * @param ids 主键串
     */
    @Log(title = LogTitleEnum.CONFIG_MANAGEMENT, businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        // 这里需要根据ids查询出对应的type，然后再进行权限校验
        if (ids.length > 0) {
            ConfInfoVo confInfoVo = confInfoService.queryById(ids[0]);
            if (confInfoVo != null) {
                checkPermission(confInfoVo.getType(),"edit");
            }
        }
        return toAjax(confInfoService.deleteWithValidByIds(List.of(ids), true));
    }

    private void checkPermission(ConfInfoType type, String operation) {
        if (type == null) {
            return;
        }
        String permission = switch (type) {
            case CLASSIFICATION -> "business:classification:";
            case NETWORK_BLOCKING_AREA -> "business:networkBlockingArea:";
            case CHANGE_LOCATION -> "business:changeLocation:";
            case AFFECTED_DEVICE -> "business:affectedDevice:";
            case MAINTENANCE_KIND -> "business:maintenanceKind:";
        };
        String permissionFinal = permission + operation;

        StpUtil.checkPermission(permissionFinal);
    }
}
