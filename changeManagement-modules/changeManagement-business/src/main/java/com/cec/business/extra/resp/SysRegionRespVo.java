package com.cec.business.extra.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 区域查询响应数据对象
 *
 * <AUTHOR>
 */
@Data
public class SysRegionRespVo {

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 0-正常，1-删除
     */
    private Integer enable;

    /**
     * 外部唯一id
     */
    private String externalId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmt8Create;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date gmt8Modified;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 排序
     */
    private Integer sortNo;

    /**
     * 是否启用: 0否; 1是
     */
    private Integer status;

    /**
     * 修改人
     */
    private Long updateUser;
} 