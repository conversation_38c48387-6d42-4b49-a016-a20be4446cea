package com.cec.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.enums.NetworkFreezeLevelEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.business.handler.JsonListLongTypeHandler;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.cec.system.domain.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 封网申请对象 cm_network_freeze_info
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cm_network_freeze_info", autoResultMap = true)
public class NetworkFreezeInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 封网名称
     */
    private String title;

    /**
     * 封网等级(1-一级 2-二级 3-三级)
     */
    private NetworkFreezeLevelEnum level;

    /**
     * 附件ids
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> fileIds;

    /**
     * 是否增加到邮件附件（1-是 2否）
     */
    private WhetherEnum isAttachment;

    /**
     * 通知电邮地址
     */
    private String notificationEmailTo;

    /**
     * 通知抄送电邮地址
     */
    private String notificationEmailCc;

    /**
     * 特殊备注
     */
    private String specialRemark;

    /**
     * 邮件内容
     */
    private String emailContent;

    /**
     * 封禁应用
     */
    private String freezeApps;

    /**
     * 申请人
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private UserVo requester;

}
