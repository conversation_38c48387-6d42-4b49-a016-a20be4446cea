package com.cec.business.extra.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户树实体
 *
 * <AUTHOR>
 */
@Data
public class UserTreeEntity {

    /**
     * 子节点
     */
    private List<UserTreeEntity> children;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 外部唯一id
     */
    private String externalId;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 职工头衔
     */
    private String jobTitle;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String joinDate;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 组织机构名称
     */
    private String orgChartName;

    /**
     * 组织机构id
     */
    private Long orgId;

    /**
     * 是否是父节点
     */
    private Boolean parent;

    /**
     * 父节点id
     */
    private Long parentId;

    /**
     * 父节点编号
     */
    private String parentNum;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 职工姓名
     */
    private String staffName;

    /**
     * 职工编号
     */
    private String staffNum;

    /**
     * 版本id
     */
    private Long versionId;
} 