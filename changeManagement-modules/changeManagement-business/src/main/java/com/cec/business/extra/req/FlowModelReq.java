package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowModelReq extends BaseReq implements Serializable {

    /**
     * 名称
     */
    private String name;

    /**
     * 当前页
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;


    @Override
    public String method() {
        return "/openapi/flow/model/list";
    }
}
