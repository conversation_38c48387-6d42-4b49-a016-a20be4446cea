package com.cec.business.service;

import com.cec.business.domain.bo.ApplicationManageBo;
import com.cec.business.domain.bo.CheckCategoryAndAppBo;
import com.cec.business.domain.vo.ApplicationManageVo;
import com.cec.business.domain.vo.CheckCategoryAndAppVo;
import com.cec.business.domain.vo.NetworkFreezeItemVo;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 应用管理Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IApplicationManageService {

    /**
     * 查询应用管理
     *
     * @param id 主键
     * @return 应用管理
     */
    ApplicationManageVo queryById(Long id);

    /**
     * 分页查询应用管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应用管理分页列表
     */
    TableDataInfo<ApplicationManageVo> queryPageList(ApplicationManageBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应用管理列表
     *
     * @param bo 查询条件
     * @return 应用管理列表
     */
    List<ApplicationManageVo> queryList(ApplicationManageBo bo);

    /**
     * 新增应用管理
     *
     * @param bo 应用管理
     * @return 是否新增成功
     */
    Boolean insertByBo(ApplicationManageBo bo);

    /**
     * 修改应用管理
     *
     * @param bo 应用管理
     * @return 是否修改成功
     */
    Boolean updateByBo(ApplicationManageBo bo);

    /**
     * 校验并批量删除应用管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 检查分类和应用关系
     * 根据分类IDs查找不存在的应用IDs，根据应用IDs查找不存在的分类IDs
     *
     * @param bo 包含分类IDs和应用IDs的请求参数
     * @return 返回不存在的分类IDs和应用IDs
     */
    CheckCategoryAndAppVo checkCategoryAndApplication(CheckCategoryAndAppBo bo);

    List<NetworkFreezeItemVo> queryList(Date planTimeStart, Date planTimeEnd, Long[] applicationIds);

    List<ApplicationManageVo> queryListById(Long[] ids);
}
