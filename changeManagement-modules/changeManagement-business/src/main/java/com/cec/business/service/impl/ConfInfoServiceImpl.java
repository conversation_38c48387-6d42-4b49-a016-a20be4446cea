package com.cec.business.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.cec.business.domain.bo.ConfInfoBo;
import com.cec.business.domain.vo.ConfInfoVo;
import com.cec.business.domain.ConfInfo;
import com.cec.business.mapper.ConfInfoMapper;
import com.cec.business.service.IConfInfoService;

import java.util.List;
import java.util.Collection;

/**
 * 配置信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RequiredArgsConstructor
@Service
public class ConfInfoServiceImpl implements IConfInfoService {

    private final ConfInfoMapper baseMapper;

    /**
     * 查询配置信息
     *
     * @param id 主键
     * @return 配置信息
     */
    @Override
    public ConfInfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询配置信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 配置信息分页列表
     */
    @Override
    public TableDataInfo<ConfInfoVo> queryPageList(ConfInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ConfInfo> lqw = buildQueryWrapper(bo);
        Page<ConfInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的配置信息列表
     *
     * @param bo 查询条件
     * @return 配置信息列表
     */
    @Override
    public List<ConfInfoVo> queryList(ConfInfoBo bo) {
        LambdaQueryWrapper<ConfInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ConfInfo> buildQueryWrapper(ConfInfoBo bo) {
        LambdaQueryWrapper<ConfInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ConfInfo::getId);
        lqw.eq(bo.getType() != null, ConfInfo::getType, bo.getType());
        lqw.like(StringUtils.isNotBlank(bo.getName()), ConfInfo::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), ConfInfo::getDescription, bo.getDescription());
        lqw.eq(bo.getStatus() != null, ConfInfo::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增配置信息
     *
     * @param bo 配置信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ConfInfoBo bo) {
        ConfInfo add = MapstructUtils.convert(bo, ConfInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean checkNameUnique(ConfInfoBo bo) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<ConfInfo>()
            .eq(ConfInfo::getName, bo.getName())
            .eq(ConfInfo::getType, bo.getType())
            .ne(ObjectUtil.isNotNull(bo.getId()), ConfInfo::getId, bo.getId()));
        return !exist;
    }

    /**
     * 修改配置信息
     *
     * @param bo 配置信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ConfInfoBo bo) {
        ConfInfo update = MapstructUtils.convert(bo, ConfInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ConfInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除配置信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
