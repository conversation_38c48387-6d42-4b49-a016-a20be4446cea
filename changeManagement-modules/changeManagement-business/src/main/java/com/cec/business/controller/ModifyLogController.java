package com.cec.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cec.business.domain.bo.ModifyLogBo;
import com.cec.business.domain.vo.ModifyLogVo;
import com.cec.business.service.IModifyLogService;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 变更修改记录
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/modifyLog")
public class ModifyLogController extends BaseController {

    private final IModifyLogService modifyLogService;

    /**
     * 查询变更修改记录列表
     */
    @SaCheckPermission("business:modifyLog:list")
    @GetMapping("/list")
    public TableDataInfo<ModifyLogVo> list(ModifyLogBo bo, PageQuery pageQuery) {
        return modifyLogService.queryPageList(bo, pageQuery);
    }

}
