package com.cec.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cec.business.domain.NetworkFreezeArea;
import com.cec.business.domain.NetworkFreezeRecord;
import com.cec.business.domain.enums.NetworkFreezeLevelEnum;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.system.domain.vo.UserVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 封网申请记录流水视图对象 cm_network_freeze_record
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = NetworkFreezeRecord.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NetworkFreezeRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 变更记录ID
     */
    @ExcelProperty(value = "变更记录ID")
    private Long freezeItemId;

    /**
     * 变更ID
     */
    @ExcelProperty(value = "封网ID")
    private Long freezeId;

    /**
     * 变更编号
     */
    @ExcelProperty(value = "变更编号")
    private String freezeCode;

    /**
     * 变更标题
     */
    @ExcelProperty(value = "变更标题")
    private String title;

    /**
     * 审批状态
     */
    @ExcelProperty(value = "审批状态")
    private ProcessorStatusEnum processorStatus;

    /**
     * 当前处理人
     */
    private UserVo processor;

    /**
     * 审批时间
     */
    @ExcelProperty(value = "审批时间")
    private Date processorTime;

    /**
     * 审批意见/备注
     */
    @ExcelProperty(value = "审批意见/备注")
    private String opinion;

    /**
     * 阶段(1-草稿/2-已提交/3已发布/4-已拒绝)
     */
    private NetworkFreezeStageEnum stage;

    /**
     * 封网等级(1-一级 2-二级 3-三级)
     */
    private NetworkFreezeLevelEnum level;

    /**
     * 封网地区和时间;存json数组
     */
    private List<NetworkFreezeArea> periodAndArea;

    /**
     * 封禁应用
     */
    private String freezeApps;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 处理顺序
     */
    private Integer processOrder;

}
