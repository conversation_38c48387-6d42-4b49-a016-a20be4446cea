package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 用户列表查询响应
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserResp extends BaseResp {

    /**
     * 请求追踪ID
     */
    private String traceId;

    /**
     * 用户列表数据
     */
    private List<UserInfo> data;

    /**
     * 用户信息
     */
    @Data
    public static class UserInfo {
        
        /**
         * 业务线
         */
        private Integer bizLine;
        
        /**
         * 联系方式
         */
        private String contact;
        
        /**
         * 电子邮箱
         */
        private String email;
        
        /**
         * 外部ID
         */
        private String externalId;
        
        /**
         * 外部组织ID
         */
        private String externalOrgId;
        
        /**
         * 传真
         */
        private String fax;
        
        /**
         * 等级IDs
         */
        private List<String> gradingLevelIds;
        
        /**
         * 国际化员工姓名
         */
        private String i18nStaffName;
        
        /**
         * ID
         */
        private Long id;
        
        /**
         * 职位
         */
        private String jobTitle;
        
        /**
         * 入职日期
         */
        private String joinDate;
        
        /**
         * 公司ID
         */
        private Integer kdCompanyId;
        
        /**
         * 公司名称
         */
        private String kdCompanyName;
        
        /**
         * 经理姓名
         */
        private String managerName;
        
        /**
         * 经理编号
         */
        private String managerNum;
        
        /**
         * 经理用户ID
         */
        private Long managerUserId;
        
        /**
         * 手机号码
         */
        private String mobilePhone;
        
        /**
         * 组织架构名称
         */
        private String orgChartName;
        
        /**
         * 组织ID
         */
        private Long orgId;
        
        /**
         * 区域ID
         */
        private Integer regionId;
        
        /**
         * 区域名称
         */
        private String regionName;
        
        /**
         * 备注
         */
        private String remark;
        
        /**
         * 员工账号
         */
        private String staffAccount;
        
        /**
         * 员工姓名
         */
        private String staffName;
        
        /**
         * 员工编号
         */
        private String staffNum;
        
        /**
         * 状态
         */
        private String status;
        
        /**
         * 时区
         */
        private String timeZone;
        
        /**
         * 版本ID
         */
        private Integer versionId;
    }
} 