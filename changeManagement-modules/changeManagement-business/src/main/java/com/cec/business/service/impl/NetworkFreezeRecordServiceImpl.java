package com.cec.business.service.impl;

import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.cec.business.domain.bo.NetworkFreezeRecordBo;
import com.cec.business.domain.vo.NetworkFreezeRecordVo;
import com.cec.business.domain.NetworkFreezeRecord;
import com.cec.business.mapper.NetworkFreezeRecordMapper;
import com.cec.business.service.INetworkFreezeRecordService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 封网申请记录流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RequiredArgsConstructor
@Service
public class NetworkFreezeRecordServiceImpl implements INetworkFreezeRecordService {

    private final NetworkFreezeRecordMapper baseMapper;

    /**
     * 查询封网申请记录流水
     *
     * @param id 主键
     * @return 封网申请记录流水
     */
    @Override
    public NetworkFreezeRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询封网申请记录流水列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 封网申请记录流水分页列表
     */
    @Override
    public TableDataInfo<NetworkFreezeRecordVo> queryPageList(NetworkFreezeRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<NetworkFreezeRecord> lqw = buildQueryWrapper(bo);
        Page<NetworkFreezeRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的封网申请记录流水列表
     *
     * @param bo 查询条件
     * @return 封网申请记录流水列表
     */
    @Override
    public List<NetworkFreezeRecordVo> queryList(NetworkFreezeRecordBo bo) {
        LambdaQueryWrapper<NetworkFreezeRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<NetworkFreezeRecord> buildQueryWrapper(NetworkFreezeRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<NetworkFreezeRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(NetworkFreezeRecord::getId);
        lqw.eq(bo.getFreezeItemId() != null, NetworkFreezeRecord::getFreezeItemId, bo.getFreezeItemId());
        lqw.eq(bo.getFreezeId() != null, NetworkFreezeRecord::getFreezeId, bo.getFreezeId());
        lqw.eq(StringUtils.isNotBlank(bo.getFreezeCode()), NetworkFreezeRecord::getFreezeCode, bo.getFreezeCode());
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), NetworkFreezeRecord::getTitle, bo.getTitle());
        lqw.eq(bo.getProcessorStatus() != null, NetworkFreezeRecord::getProcessorStatus, bo.getProcessorStatus());
        // lqw.eq(StringUtils.isNotBlank(bo.getProcessorId()), NetworkFreezeRecord::getProcessorId, bo.getProcessorId());
        lqw.apply(StringUtils.isNotBlank(bo.getProcessorName()), "JSON_EXTRACT(processor, '$.staffName') LIKE CONCAT('%', {0}, '%')", bo.getProcessorName());
        lqw.eq(StringUtils.isNotBlank(bo.getOpinion()), NetworkFreezeRecord::getOpinion, bo.getOpinion());
        return lqw;
    }

    /**
     * 新增封网申请记录流水
     *
     * @param bo 封网申请记录流水
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(NetworkFreezeRecordBo bo) {
        NetworkFreezeRecord add = MapstructUtils.convert(bo, NetworkFreezeRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改封网申请记录流水
     *
     * @param bo 封网申请记录流水
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(NetworkFreezeRecordBo bo) {
        NetworkFreezeRecord update = MapstructUtils.convert(bo, NetworkFreezeRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(NetworkFreezeRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除封网申请记录流水信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
