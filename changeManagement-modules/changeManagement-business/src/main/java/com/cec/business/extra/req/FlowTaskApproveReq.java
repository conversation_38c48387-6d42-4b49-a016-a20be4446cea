package com.cec.business.extra.req;

import com.cec.business.extra.resp.FlowInstanceStartResp;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.TreeMap;

/***
 * 执行任务
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowTaskApproveReq extends BaseReq implements Serializable {
    /**
     * 名称
     */
    private String name;
    /**
     * 处理人id	true	string
     */
    @NotEmpty(message = "处理人id不能为空")
    private String assigneeId;
    /**
     * 处理人部门id	true	string
     */
    @NotEmpty(message = "处理人部门id不能为空")
    private String assigneeOrgId;
    /**
     * 任务id	true	integer
     */
    @NotNull(message = "任务id不能为空")
    private Long id;
    /**
     * 步骤信息列表	true	object
     */
    @NotNull(message = "步骤信息列表不能为空")
    private List<FlowInstanceStartResp.FlowInstanceNode> nodeList;
    /**
     * 节点id	true	string
     */
    @NotEmpty(message = "节点id不能为空")
    private String nodeId;
    /**
     * 节点名称	true	string
     */
    @NotEmpty(message = "节点名称不能为空")
    private String nodeName;
    /**
     * 审批意见	false	string
     */
    private String comment;
    /**
     * 抄送人员信息列表
     */
    private List<FlowInstanceStartResp.CopyFor> copyForList;
    /**
     * 流程自定义表单数据	false	object
     */
    private TreeMap<String, Object> formData;
    /**
     * 流程执行变量	false	object
     */
    private TreeMap<String, Object> variables;

    @Override
    public String method() {
        return "/openapi/flow/task/approve";
    }
}
