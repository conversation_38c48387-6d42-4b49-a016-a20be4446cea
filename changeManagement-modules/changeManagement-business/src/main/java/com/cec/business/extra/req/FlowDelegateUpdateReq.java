package com.cec.business.extra.req;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/***
 * 新增委托信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowDelegateUpdateReq extends BaseReq implements Serializable {
    /**
     * 创建人id（上游服务）	ture	string
     */
    @NotEmpty(message = "创建人id不能为空")
    private String creatorId;
    /**
     * 创建人部门id（上游服务）	ture	string
     */
    @NotEmpty(message = "创建人部门id不能为空")
    private String creatorOrgId;
    /**
     * 结束时间	ture	date
     */
    @NotNull(message = "结束时间不能为空")
    private Date endTime;
    /**
     * 关联的表单id，多个逗号分割	false	string
     */
    private String modules;
    /**
     * 委托说明	false	string
     */
    private String remark;
    /**
     * 开始时间	ture	date
     */
    @NotNull(message = "开始时间不能为空")
    private Date startTime;
    /**
     * 委托人id	ture	string
     */
    @NotEmpty(message = "委托人id不能为空")
    private String userId;
    /**
     * 委托人部门id	ture	string
     */
    @NotEmpty(message = "委托人部门id不能为空")
    private String userOrgId;
    /**
     * 状态 0：正常，1：关闭
     */
    private Integer status;
    /**
     * 流程委托组列表	ture	array
     */
    @NotNull(message = "流程委托组列表不能为空")
    private List<FlowDelegateItem> flowDelegateItemList;

    @Data
    public static class FlowDelegateItem implements Serializable {
        /**
         * 流程委托id，关联flow_delegate
         */
        private Integer delegateId;
        /**
         * 流程委托组名称	ture	string
         */
        private String name;
        /**
         * 被委托人id	ture	string
         */
        private String receiverId;
        /**
         * 被委托人部门id	ture	string
         */
        private String receiverOrgId;
        /**
         * 发起者id列表	ture	string
         */
        private String starterId;
        /**
         * 类型：0 普通，1 默认	ture	integer
         */
        private Integer type;
    }


    @Override
    public String method() {
        return "/openapi/flow/delegate/update";
    }
}
