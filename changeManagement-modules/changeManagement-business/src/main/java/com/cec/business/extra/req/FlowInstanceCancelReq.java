package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/***
 * 发起流程实例
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowInstanceCancelReq extends BaseReq implements Serializable {

    /**
     * 流程实例id
     */
    @NotNull(message = "流程实例id不能为空")
    private Long id;
    /**
     * 发起人id
     */
    @NotEmpty(message = "发起人id不能为空")
    private String userId;
    /**
     * 取消原因
     */
    private String comment;
    /**
     * 流程自定义表单数据
     */
    private String formData;


    @Override
    public String method() {
        return "/openapi/flow/instance/cancel";
    }
}
