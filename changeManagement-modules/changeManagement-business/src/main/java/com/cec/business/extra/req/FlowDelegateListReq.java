package com.cec.business.extra.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/***
 * 新增委托信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowDelegateListReq extends BaseReq implements Serializable {
    /**
     * 结束时间	false	date
     */
    private Date endTime;
    /**
     * 排序	false	array
     */
    private OrderItem orderItemList;
    /**
     * 当前页码	false	integer
     */
    private Integer pageNum = 1;
    /**
     * 每页查询的条数	false	integer
     */
    private Integer pageSize = 10;
    /**
     * 状态 0：正常，1：关闭	false	integer
     */
    private Integer status;
    /**
     * 委托人id	false	string
     */
    private String userId;

    @Data
    public static class OrderItem implements Serializable {
        /**
         * 升序降序	false	boolean
         */
        private boolean asc;
        /**
         * 排序字段名	false	string
         */
        private String column;
    }

    @Override
    public String method() {
        return "/openapi/flow/delegate/list";
    }
}
