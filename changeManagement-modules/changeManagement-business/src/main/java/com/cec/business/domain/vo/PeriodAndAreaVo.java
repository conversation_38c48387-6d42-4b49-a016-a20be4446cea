package com.cec.business.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 封网地区和时间对象
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class PeriodAndAreaVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 封网地区ID
     */
    private List<String> areaId;

    /**
     * 封网地区名称
     */
    private List<String> areaName;

    /**
     * 封网地区英文
     */
    private List<String> areaNameEn;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
}
