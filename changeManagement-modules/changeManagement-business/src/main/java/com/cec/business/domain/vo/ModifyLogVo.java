package com.cec.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cec.business.domain.ModifyLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.cec.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 变更修改记录视图对象 cm_modify_log
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ModifyLog.class)
@EqualsAndHashCode(callSuper = true)
public class ModifyLogVo extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 变更编号
     */
    private String changeCode;

    /**
     * 修改字段
     */
    private String modifyFiled;

    /**
     * 修改字段_zh（简体中文名称）
     */
    private String filedNameZh;

    /**
     * 修改字段_us（英文名称）
     */
    private String filedNameUs;

    /**
     * 修改字段_tw（繁体中文名称）
     */
    private String filedNameTw;

    /**
     * 修改内容-旧
     */
    private String contentOld;

    /**
     * 修改内容-新
     */
    private String contentNew;

    /**
     * StaffId
     */
    private String staffId;

    /**
     * StaffName
     */
    private String staffName;

    /**
     * 备注
     */
    private String remark;

}
