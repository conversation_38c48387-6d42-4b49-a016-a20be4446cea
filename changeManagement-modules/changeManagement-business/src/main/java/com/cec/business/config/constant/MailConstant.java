package com.cec.business.config.constant;

public class MailConstant {

    /**
     * 变更开始时发邮件通知(通过回复之前通知邮件方式)
     */
    public static final String CHANGE_START_SUBJECT = """
        IT Notification: {$Emergency/Scheduled} Maintenance of {$SystemName} from {$StartDate} to {$EndDate} (GMT+8) (Reference Number: {$ChangeNumber})
        """;

    /**
     * 变更开始时发邮件通知(通过回复之前通知邮件方式)
     */
    public static final String CHANGE_START_SUBJECT_CN = """
        IT通知: {$SystemName} {$紧急/常规}维护(维护时间 {$StartDate}到{$EndDate} (GMT+8) ，参考编号：{$ChangeNumber})
        """;

    public static final String CHANGE_START_CONTENT = """
        Dear All,<br/><br/>

        Please be advised that the {$Emergency/Scheduled} maintenance starts now.<br/><br/>

        Thank you.<br/><br/><br/><br/>

        Best regards,<br/><br/>

        {$TeamName}<br/>
        Innovative Research & Development and Information Technology
        """;

    public static final String CHANGE_START_CONTENT_CN = """
        尊敬的用户,<br/><br/>

        {$SystemName}的{$紧急/常规}维护即将开始。<br/><br/>

        维护期间给您造成的不便，我们深表歉意。待维护完成后，我们会立刻通知您。<br/><br/>

        谢谢。<br/><br/><br/><br/>

        Best regards,<br><br>

        {$TeamName}<br>
        Innovative Research & Development and Information Technology
        """;

    /**
     * 变更结束后发邮件通知
     */
    public static final String CHANGE_END_SUBJECT = """
        IT Notification: {$Emergency/Scheduled} Maintenance of {$SystemName} from {$StartDate} to {$EndDate} (GMT+8) (Reference Number: {$ChangeNumber})
        """;

    /**
     * 变更结束后发邮件通知
     */
    public static final String CHANGE_END_SUBJECT_CN = """
        IT通知: {$SystemName} {$紧急/常规}维护(维护时间 {$StartDate}到{$EndDate} (GMT+8)，参考编号：{$ChangeNumber})
        """;

    public static final String CHANGE_END_CONTENT = """
        Dear All,<br/><br/>

        The {$Emergency/Scheduled} maintenance of {$SystemName} has been completed and services have been restored.<br/><br/>


        Thank you.<br/><br/><br/><br/>
        Best regards,<br/><br/>

        {$TeamName}<br/>
        Innovative Research & Development and Information Technology
        """;

    public static final String CHANGE_END_CONTENT_CN = """
        尊敬的用户,<br/><br/>

        {$SystemName}的{$紧急/常规}维护已完成。系统/服务已恢复正常状态。<br/><br/>

        谢谢。<br/><br/><br/><br/>

        Best regards,<br/><br/>

        {$TeamName}<br/>
        Innovative Research & Development and Information Technology
        """;

    /**
     * 变更延时时发邮件通知
     */
    public static final String CHANGE_DELAY_SUBJECT = """
        IT Notification: {$Emergency/Scheduled} Maintenance of {$SystemName} from {$StartDate} to {$EndDate} (GMT+8) (Reference Number: {$ChangeNumber})
        """;

    public static final String CHANGE_DELAY_SUBJECT_CN = """
        IT通知: {$SystemName} {$紧急/常规}维护(维护时间 {$StartDate}到{$EndDate} (GMT+8)，参考编号：{$ChangeNumber})
        """;

    public static final String CHANGE_DELAY_CONTENT = """
        Dear All,<br/><br/>

        Please be advised that the maintenance period of {$SystemName} has been extended to {$NewEndDate} due to {$ExtensionReason}<br/><br/>

        Sincere apologies for any inconvenience caused.<br/><br/>

        Thank you.<br/><br/><br/><br/>

        Best regards,<br/><br/>

        {$TeamName}<br/>
        Innovative Research & Development and Information Technology
        """;

    public static final String CHANGE_DELAY_CONTENT_CN = """
        尊敬的用户,<br/><br/>

        由于{$ExtensionReason}原因，{$SystemName}的维护时间已延长至{$NewEndDate}。<br/><br/>

        因此给您带来的不便，我们深表歉意。<br/><br/>

        谢谢.<br/><br/><br/><br/>

        Best regards,<br/><br/>

        {$TeamName}<br/>
        Innovative Research & Development and Information Technology
        """;

    /**
     * 变更取消时发邮件通知_默认
     */
    public static final String CHANGE_CANCEL_SUBJECT = """
        IT Notification: {$Emergency/Scheduled} Maintenance of {$SystemName} from {$StartDate} to {$EndDate} (GMT+8) (Reference Number: {$ChangeNumber})
        """;

    /**
     * 变更取消时发邮件通知_中文
     */
    public static final String CHANGE_CANCEL_SUBJECT_CN = """
        IT通知: {$SystemName} {$紧急/常规}维护(维护时间 {$StartDate}到{$EndDate} (GMT+8) ，参考编号：{$ChangeNumber})
        """;

    public static final String CHANGE_CANCEL_CONTENT = """
        Dear All,<br/><br/>

        Please be advised that the {$Emergency/Scheduled} maintenance has been cancelled.<br/><br/>

        Thank you.<br/><br/><br/><br/>

        Best regards,<br/><br/>

        {$TeamName}<br/>
        Innovative Research & Development and Information Technology
        """;

    public static final String CHANGE_CANCEL_CONTENT_CN = """
        尊敬的用户,<br/><br/>

        {$SystemName}的{$紧急/常规}维护已取消。系统/服务可正常使用。<br/><br/>

        谢谢。<br/><br/><br/><br/>

        {$TeamName}<br>
        Innovative Research & Development and Information Technology
        """;

    /**
     * 变更超过 7天未关闭时发送
     */
    public static final String CHANGE_OVER_7_DAYS_SUBJECT = """
        IT Notification: {$Emergency/Scheduled} Maintenance of {$SystemName} from {$StartDate} to {$EndDate} (Reference Number: {$ChangeNumber})
        """;

    public static final String CHANGE_OVER_7_DAYS_CONTENT = """
        Dear All,<br/><br/>

        Please note below {$Emergency/Scheduled} Maintenance created by {$Requestor} has exceeded 7 days.<br/><br/>

        {$ChangeNumberURL}<br/><br/>

        {$Current Date}<br/><br/><br/>

        This email is automatically generated. Please do not respond to this email address.<br/><br/>

        Sent from Change Management Portal.
        """;

    /**
     * 已审批主题_默认
     */
    public static final String CHANGE_APPROVED_SUBJECT = """
        IT Notification: {$Emergency/Scheduled} Maintenance of {$SystemName} from {$StartDate} to {$EndDate} (GMT+8) (Reference Number: {$ChangeNumber})
        """;

    /**
     * 已审批主题_中文
     */
    public static final String CHANGE_APPROVED_SUBJECT_CN = """
        IT通知: {$SystemName} {$紧急/常规}维护(维护时间：{$StartDate}到{$EndDate} (GMT+8) ，参考编号：{$ChangeNumber})
        """;

    public static final String FREEZE_SUBJECT = """
        IT Notification: {$NetworkFreezeLevel} Network Freeze ({$FrozenPeriodEn}，GMT+8) ———— {$NetworkFreezeLevelCN}封网通知（北京时间：{$FrozenPeriodCn}）(Reference Number: {$NetworkFreezeNo})
        """;

    /**
     * 变更待审批主题
     */
    public static final String CHANGE_TODO_SUBJECT = """
        Approval of {$Emergency/Scheduled} Maintenance submitted by {$Requestor}
        """;

    /**
     * 变更待审批内容
     */
    public static final String CHANGE_TODO_CONTENT = """
        Dear Sir/Madam,<br/><br/>

        Please approve below {$Emergency/Scheduled} Maintenance created by {$Requestor}.<br/><br/>

        {$ChangeNumberURL}<br/><br/><br/>


        {$Current Date}<br/><br/><br/><br/>

        ******************************************************************************************<br/>
        This email is automatically generated. Please do not respond to this email address.<br/>
        ******************************************************************************************<br/>
        Sent from Change Management Portal.
        """;

    /**
     * 封网待审批主题
     */
    public static final String FREEZE_TODO_SUBJECT = """
        Approval of {$NetworkFreezeLevel} Network Freeze submitted by {$Requestor}
        """;

    /**
     * 封网待审批内容
     */
    public static final String FREEZE_TODO_CONTENT = """
        Dear Sir/Madam,<br/><br/>

        Please approve below {$NetworkFreezeLevel} Network Freeze created by {$Requestor}.<br/><br/>

        {$NetworkFreezeURL}<br/><br/><br/>


        {$Current Date}<br/><br/><br/>

        ******************************************************************************************<br/>
        This email is automatically generated. Please do not respond to this email address.<br/>
        ******************************************************************************************<br/>
        Sent from Change Management Portal.
        """;

}
