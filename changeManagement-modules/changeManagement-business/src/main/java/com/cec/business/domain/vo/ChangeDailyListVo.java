package com.cec.business.domain.vo;

import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.system.domain.vo.UserVo;
import lombok.Data;

import java.util.Date;

/**
 * 日期变更列表VO
 */
@Data
public class ChangeDailyListVo {

    /**
     * 变更ID
     */
    private Long id;

    /**
     * 变更编号
     */
    private String changeCode;

    /**
     * 变更名称
     */
    private String title;

    /**
     * 变更阶段
     */
    private ChangeStageEnum stage;

    /**
     * 申请人团队
     */
    private String teamName;

    /**
     * 申请人
     */
    private UserVo requester;

    /**
     * 计划开始时间
     */
    private Date planTimeStart;

    /**
     * 计划结束时间
     */
    private Date planTimeEnd;
}
