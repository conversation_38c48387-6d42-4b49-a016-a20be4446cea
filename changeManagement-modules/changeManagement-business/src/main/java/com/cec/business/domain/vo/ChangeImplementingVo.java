package com.cec.business.domain.vo;

import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.system.domain.vo.UserVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 阶段变更中，待验证
 */
@Data
public class ChangeImplementingVo {

    /**
     * 变更项
     */
    List<ChangeRecordVo> changeRecordVoList;

    /**
     * 结果
     */
    private ProcessorStatusEnum result;

    @Data
    public static class ChangeRecordVo {

        /**
         * id
         */
        private Long recordId;

        /**
         * 变更项ID
         */
        private Long changeItemId;

        /**
         * 变更ID
         */
        private Long changeId;

        /**
         * 变更编号
         */
        private String changeCode;

        /**
         * 阶段
         * 1-草稿 2-已提交 3-待审批 4-已审批 5-实施中 6-已完成 7-已拒绝 8-已回滚 9-已取消 10-待验证
         */
        private ChangeStageEnum stage;

        /**
         * 审批状态
         * 1-发起 2-已审批 3-已拒绝 4-待审批 5-已实施 6-已回滚 7-开始变更 8-通过 9-不通过 10-已取消
         */
        private ProcessorStatusEnum processorStatus;

        /**
         * 当前处理人名字
         */
        private UserVo processor;

        /**
         * 审批时间
         */
        private Date processorTime;

        /**
         * 步骤
         */
        private String step;

        /**
         * 审批意见/备注
         */
        private String opinion;

        /**
         * 处理顺序
         */
        private Integer processOrder;

        /**
         * 节点名称
         */
        private String nodeName;

    }


}
