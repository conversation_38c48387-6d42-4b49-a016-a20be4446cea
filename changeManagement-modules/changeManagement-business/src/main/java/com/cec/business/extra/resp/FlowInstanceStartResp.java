package com.cec.business.extra.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 发起流程实例响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowInstanceStartResp extends BaseResp implements Serializable {
    /**
     * 响应数据
     */
    private String data;

    @Data
    public static class CopyFor {
        /**
         * 审批人id
         */
        private String assigneeId;
        /**
         * 审批人名称
         */
        private String assigneeName;
        /**
         * 审批人部门id
         */
        private String assigneeOrgId;
        /**
         * 审批人部门名称
         */
        private String assigneeOrgName;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FlowInstanceNode {
        /**
         * 审批人信息列表
         */
        private List<FlowInstanceHandler> handlerList;
        /**
         * 节点id
         */
        private String nodeId;
        /**
         * 节点名称
         */
        private String nodeName;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FlowInstanceHandler {
        /**
         * 审批人id
         */
        private String assigneeId;
        /**
         * 审批人名称
         */
        private String assigneeName;
        /**
         * 审批人部门id
         */
        private String assigneeOrgId;
        /**
         * 审批人部门名称
         */
        private String assigneeOrgName;
        /**
         * 委托人信息	false	object
         */
        private String delegator;
    }
}
