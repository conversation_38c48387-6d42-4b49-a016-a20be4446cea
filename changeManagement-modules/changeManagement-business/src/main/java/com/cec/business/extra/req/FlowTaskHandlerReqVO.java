package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * 审批人信息
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowTaskHandlerReqVO implements Serializable {
    /**
     * 审批人id
     */
    @NotEmpty(message = "审批人id不能为空")
    private String assigneeId;
    
    /**
     * 审批人名称
     */
    @NotEmpty(message = "审批人名称不能为空")
    private String assigneeName;
    
    /**
     * 审批人部门id
     */
    @NotEmpty(message = "审批人部门id不能为空")
    private String assigneeOrgId;
    
    /**
     * 审批人部门名称
     */
    @NotEmpty(message = "审批人部门名称不能为空")
    private String assigneeOrgName;
} 