package com.cec.business.service;

import com.cec.business.domain.vo.NetworkFreezeItemVo;
import com.cec.business.domain.bo.NetworkFreezeItemBo;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 封网申请记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface INetworkFreezeItemService {

    /**
     * 查询封网申请记录
     *
     * @param id 主键
     * @return 封网申请记录
     */
    NetworkFreezeItemVo queryById(Long id);

    /**
     * 分页查询封网申请记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 封网申请记录分页列表
     */
    TableDataInfo<NetworkFreezeItemVo> queryPageList(NetworkFreezeItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的封网申请记录列表
     *
     * @param bo 查询条件
     * @return 封网申请记录列表
     */
    List<NetworkFreezeItemVo> queryList(NetworkFreezeItemBo bo);

    /**
     * 新增封网申请记录
     *
     * @param bo 封网申请记录
     * @return 是否新增成功
     */
    Boolean insertByBo(NetworkFreezeItemBo bo);

    /**
     * 修改封网申请记录
     *
     * @param bo 封网申请记录
     * @return 是否修改成功
     */
    Boolean updateByBo(NetworkFreezeItemBo bo);

    /**
     * 校验并批量删除封网申请记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
