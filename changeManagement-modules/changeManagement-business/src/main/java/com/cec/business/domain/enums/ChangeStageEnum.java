package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 变更阶段枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ChangeStageEnum {
    /**
     * 草稿
     */
    DRAFT(1, "草稿"),
    /**
     * 计划中
     */
    SUBMITTED(2, "计划中"),
    /**
     * 待审批
     */
    PENDING_APPROVAL(3, "待审批"),
    /**
     * 已审批
     */
    APPROVED(4, "已审批"),
    /**
     * 实施中
     */
    IMPLEMENTING(5, "实施中"),
    /**
     * 已完成
     */
    COMPLETED(6, "已完成"),
    /**
     * 已拒绝
     */
    REJECTED(7, "已拒绝"),
    /**
     * 已回滚
     */
    ROLLED_BACK(8, "已回滚"),
    /**
     * 已取消
     */
    CANCELLED(9, "已取消"),
    /**
     * 待验证
     */
    PENDING_VERIFICATION(10, "待验证"),
    /**
     * 已延期
     */
    DELAYED(11, "已延期"),
    /**
     * 超过7天未关闭
     */
    OVER_7_DAYS(12, "超过7天未关闭"),
    ;

    @EnumValue
    private final Integer code;
    private final String info;

    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ChangeStageEnum findByCode(Integer code) {
        for (ChangeStageEnum stage : values()) {
            if (stage.getCode().equals(code)) {
                return stage;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static ChangeStageEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
