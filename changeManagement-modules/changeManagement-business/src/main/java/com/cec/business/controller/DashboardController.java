package com.cec.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cec.business.domain.bo.ChangeRecordBo;
import com.cec.business.domain.vo.CalendarEventVo;
import com.cec.business.domain.vo.ChangeDailyListVo;
import com.cec.business.domain.vo.ChangeItemVo;
import com.cec.business.domain.vo.ChangeRecordVo;
import com.cec.business.domain.vo.NetworkFreezeInfoVo;
import com.cec.business.service.IDashboardService;
import com.cec.common.core.domain.R;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 首页
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/dashboard")
public class DashboardController {

    private final IDashboardService dashboardService;

    /**
     * 获取封网信息
     */
    @SaCheckPermission("business:dashboard:networkFreeze")
    @GetMapping("/networkFreeze")
    public R<List<NetworkFreezeInfoVo>> getNetworkFreeze() {
        Date date = new Date();
        return R.ok(dashboardService.getNetworkFreezeInfo(date));
    }

    /**
     * 获取未完结变更
     */
    @SaCheckPermission("business:dashboard:changeUnFinished")
    @GetMapping("/changeUnFinished")
    public TableDataInfo<ChangeItemVo> getChangeUnFinished(PageQuery pageQuery) {
        return dashboardService.getChangeUnFinished(pageQuery);
    }

    /**
     * 获取待办列表
     */
    @SaCheckPermission("business:dashboard:changeTodo")
    @GetMapping("/changeTodo")
    public TableDataInfo<ChangeRecordVo> getChangeTodo(ChangeRecordBo bo, PageQuery pageQuery) {
        return dashboardService.getChangeTodo(bo, pageQuery);
    }

    /**
     * 获取变更封网日历
     * @param yearMonth 年月 yyyyMM
     */
    @SaCheckPermission("business:dashboard:calendar")
    @GetMapping("/calendar")
    public R<List<CalendarEventVo>> getCalendarEvents(@RequestParam(required = false) String yearMonth) {
        return R.ok(dashboardService.getCalendarEvents(yearMonth));
    }

    /**
     * 获取该日期的变更列表数据
     * @param date 日期 yyyy-MM-dd
     */
    @SaCheckPermission("business:dashboard:dailyChangeList")
    @GetMapping("/dailyChangeList")
    public R<List<ChangeDailyListVo>> getChangeDailyList(@RequestParam(required = false) String date) {
        return R.ok(dashboardService.getChangeDailyList(date));
    }
}
