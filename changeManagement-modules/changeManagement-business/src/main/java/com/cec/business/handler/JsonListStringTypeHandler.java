package com.cec.business.handler;

import com.cec.common.json.utils.JsonUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * String类型列表JSON格式TypeHandler
 * 用于处理List<String>和数据库JSON字符串之间的转换
 *
 * <AUTHOR>
 */
@MappedTypes(List.class)
public class JsonListStringTypeHandler extends BaseTypeHandler<List<String>> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JsonUtils.toJsonString(parameter));
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String values = rs.getString(columnName);
        return convertToList(values);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String values = rs.getString(columnIndex);
        return convertToList(values);
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String values = cs.getString(columnIndex);
        return convertToList(values);
    }

    private List<String> convertToList(String values) {
        if (values == null || values.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return JsonUtils.parseArray(values, String.class);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
}
