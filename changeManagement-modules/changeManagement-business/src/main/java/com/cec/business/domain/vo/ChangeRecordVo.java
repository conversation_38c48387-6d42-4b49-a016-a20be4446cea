package com.cec.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cec.business.domain.ChangeRecord;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.ComplexityEnum;
import com.cec.business.domain.enums.FallbackEnum;
import com.cec.business.domain.enums.ImportantUsersEnum;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.business.domain.enums.RiskLevelEnum;
import com.cec.business.domain.enums.SystemsAffectedNoEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.common.excel.annotation.ExcelDictFormat;
import com.cec.common.excel.convert.ExcelDictConvert;
import com.cec.system.domain.vo.UserVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;



/**
 * 变更申请记录流水视图对象 cm_change_record
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ChangeRecord.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChangeRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 变更记录ID
     */
    @ExcelProperty(value = "变更记录ID")
    private Long changeItemId;

    /**
     * 变更ID
     */
    @ExcelProperty(value = "变更ID")
    private Long changeId;

    /**
     * 变更编号
     */
    @ExcelProperty(value = "变更编号")
    private String changeCode;

    /**
     * 变更标题
     */
    @ExcelProperty(value = "变更标题")
    private String title;

    /**
     * 审批状态
     * 1-发起 2-已审批 3-已拒绝 4-待审批 5-已实施 6-已回滚 7-开始变更 8-通过 9-不通过 10-已取消
     */
    @ExcelProperty(value = "审批状态")
    private ProcessorStatusEnum processorStatus;

    /**
     * 当前处理人
     */
    private UserVo processor;

    /**
     * 审批时间
     */
    @ExcelProperty(value = "审批时间")
    private Date processorTime;

    /**
     * 变更组ID
     */
    @ExcelProperty(value = "变更组ID")
    private Long teamId;

    /**
     * 变更组名
     */
    @ExcelProperty(value = "变更组名")
    private String teamName;

    /**
     * 是否紧急变更（1-是 2-否）
     */
    @ExcelProperty(value = "是否紧急变更", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-是,2=-否")
    private WhetherEnum isUrgentChange;

    /**
     * 优先级(1-高 2-中 3-低)
     */
    @ExcelProperty(value = "优先级(1-高 2-中 3-低)")
    private LevelEnum priority;

    /**
     * 审批意见/备注
     */
    @ExcelProperty(value = "审批意见/备注")
    private String opinion;

    /**
     * 是否需要加签（1-是 2-否）
     */
    @ExcelProperty(value = "是否需要加签", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=-是,2=-否")
    private WhetherEnum isSealAddition;

    /**
     * 阶段
     * 1-草稿 2-已提交 3-待审批 4-已审批 5-实施中 6-已完成 7-已拒绝 8-已回滚 9-已取消 10-待验证
     */
    private ChangeStageEnum stage;

    /**
     * 步骤
     */
    private String step;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 会签类型（0-会签 1-或签）
     */
    private String countersignType;

    /**
     * 是否允许加签（0-否 1-是）
     */
    private String countersign;

    /**
     * smartFlow节点策略
     */
    private String strategy;

    /**
     * smartFlow待发送节点列表
     */
    private String nextList;

    /**
     * smartFlow当前任务信息
     */
    private String taskCurrent;

    /**
     * 是否网络冻结变更（1-是 2-否）
     */
    private WhetherEnum isNetworkFreezeChange;

    /**
     * 应用名称
     */
    private List<String> applicationName;

    /**
     * 地点名称
     */
    private String locationName;

    /**
     * 变更类型
     */
    private String changeType;

    /**
     * 申请人姓名
     */
    private UserVo requester;

    /**
     * 计划开始时间
     */
    private Date planTimeStart;

    /**
     * 计划结束时间
     */
    private Date planTimeEnd;

    /**
     * 受影响用户
     */
    private String affectedUser;

    /**
     * 受影响应用名称
     */
    private List<String> affectedApplicationName;

    /**
     * 受影响设备名称
     */
    private List<String> affectedDeviceName;

    /**
     * Team Leader List
     */
    private List<UserVo> teamLeaderList;

    /**
     * Tester List
     */
    private List<UserVo> testerList;

    /**
     * 变更审批人List
     */
    private List<UserVo> changeApproverList;

    /**
     * 服务提交团队List
     */
    private List<UserVo> serviceDeliveryTeamList;

    /**
     * 系统所有人List
     */
    private List<UserVo> applicationOwnerList;

    /**
     * 变更实施人List
     */
    private List<UserVo> changeImplementerList;

    /**
     * 变更验证人List
     */
    private List<UserVo> changeVerifierList;

    /**
     * 变更所有者List
     */
    private List<UserVo> changeOwnerList;

    /**
     * 部门负责人List
     */
    private List<UserVo> deptLeaderList;

    /**
     * 紧急变更核查人List
     */
    private List<UserVo> urgentChangeInspectorList;

    /**
     * 受影响系统数量
     */
    private SystemsAffectedNoEnum systemsAffectedNo;

    /**
     * 重要用户
     */
    private ImportantUsersEnum importantUsers;

    /**
     * 回退方案
     */
    private FallbackEnum fallback;

    /**
     * 复杂度
     */
    private ComplexityEnum complexity;

    /**
     * 风险等级
     */
    private RiskLevelEnum riskLevel;

    /**
     * 风险评分
     */
    private Integer riskScore;

    /**
     * 分类名称
     */
    private List<String> categoryNameList;

    /**
     * 节点名称
     */
    private String nodeName;
}
