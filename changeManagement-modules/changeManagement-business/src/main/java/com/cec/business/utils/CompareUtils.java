package com.cec.business.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cec.business.domain.bo.ModifyLogBo;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.core.utils.StringUtils;
import org.springframework.context.MessageSource;
import lombok.extern.slf4j.Slf4j;
import cn.hutool.json.JSONUtil;

import java.util.*;

/**
 * 对象比较工具类
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Slf4j
public class CompareUtils {
    /**
     * 默认忽略字段集合
     */
    private static final Set<String> DEFAULT_IGNORE_FIELDS = new HashSet<>(Arrays.asList(
            "id", "createTime", "updateTime", "createBy", "updateBy", "createDept"));

    /**
     * 比较两个对象的差异并转换为ModifyLogBo列表
     *
     * @param oldObj       旧对象
     * @param newObj       新对象
     * @param changeCode   变更编号
     * @param ignoreFields 忽略的字段集合
     * @param loginUser    当前登录用户（可为null）
     * @return 修改记录BO列表
     */
    public static List<ModifyLogBo> compareObjects(Object oldObj, Object newObj, String changeCode, Set<String> ignoreFields, LoginUser loginUser) {
        // 参数校验
        if (oldObj == null || newObj == null) {
            return Collections.emptyList();
        }

        List<ModifyLogBo> result = new ArrayList<>();
        Map<String, Object> oldMap = BeanUtil.beanToMap(oldObj);
        Map<String, Object> newMap = BeanUtil.beanToMap(newObj);

        // 合并忽略字段
        Set<String> allIgnoreFields = new HashSet<>(DEFAULT_IGNORE_FIELDS);
        if (CollUtil.isNotEmpty(ignoreFields)) {
            allIgnoreFields.addAll(ignoreFields);
        }

        // 获取并处理字段
        Set<String> fieldNames = mergeAndFilterFields(oldMap.keySet(), newMap.keySet(), allIgnoreFields);

        // 比较字段值并创建ModifyLogBo
        for (String fieldName : fieldNames) {
            Object oldValue = oldMap.get(fieldName);
            Object newValue = newMap.get(fieldName);

            // 优化值比较逻辑
            if (isValuesEqual(oldValue, newValue)) {
                continue;
            }

            // 创建并添加修改记录
            result.add(createModifyLog(fieldName, oldValue, newValue, changeCode, loginUser));
        }

        return result;
    }

    /**
     * 合并并过滤字段集合
     *
     * @param oldFields    旧对象字段集合
     * @param newFields    新对象字段集合
     * @param ignoreFields 忽略字段集合
     * @return 处理后的字段集合
     */
    private static Set<String> mergeAndFilterFields(Set<String> oldFields, Set<String> newFields, Set<String> ignoreFields) {
        Set<String> fieldNames = new HashSet<>();
        fieldNames.addAll(oldFields);
        fieldNames.addAll(newFields);
        fieldNames.removeAll(ignoreFields);
        return fieldNames;
    }

    /**
     * 判断两个值是否相等
     *
     * @param oldValue 旧值
     * @param newValue 新值
     * @return 是否相等
     */
    private static boolean isValuesEqual(Object oldValue, Object newValue) {
        // 两个值都为空视为相等
        if (oldValue == null && newValue == null) {
            return true;
        }

        // 处理集合类型
        if (oldValue instanceof Collection && newValue instanceof Collection) {
            return CollUtil.isEqualList((Collection<?>) oldValue, (Collection<?>) newValue);
        }

        // 处理数组类型
        if (oldValue != null && newValue != null && oldValue.getClass().isArray() && newValue.getClass().isArray()) {
            return ObjectUtil.equals(oldValue, newValue);
        }

        // 一般对象比较
        return Objects.equals(oldValue, newValue);
    }

    /**
     * 创建修改日志记录
     *
     * @param fieldName  字段名
     * @param oldValue   旧值
     * @param newValue   新值
     * @param changeCode 变更编号
     * @param loginUser  登录用户
     * @return 修改记录对象
     */
    private static ModifyLogBo createModifyLog(String fieldName, Object oldValue, Object newValue, String changeCode, LoginUser loginUser) {
        ModifyLogBo logBo = new ModifyLogBo();
        logBo.setChangeCode(changeCode);
        logBo.setModifyFiled(fieldName);

        // 适当处理不同类型转换为字符串
        logBo.setContentOld(formatValue(oldValue));
        logBo.setContentNew(formatValue(newValue));

        // 设置字段的多语言名称
        setMultiLanguageFieldNames(logBo, fieldName);

        // 设置用户信息
        setUserInfo(logBo, loginUser);

        return logBo;
    }

    /**
     * 格式化字段值为字符串
     *
     * @param value 字段值
     * @return 格式化后的字符串
     */
    private static String formatValue(Object value) {
        if (value == null) {
            return null;
        }

        // 处理枚举类型
        if (value.getClass().isEnum()) {
            return formatEnum((Enum<?>) value);
        }

        if (value instanceof Collection) {
            // 检查集合是否包含枚举类型
            Collection<?> collection = (Collection<?>) value;
            if (!collection.isEmpty() && collection.iterator().next().getClass().isEnum()) {
                List<Map<String, Object>> enumList = new ArrayList<>();
                for (Object item : collection) {
                    if (item instanceof Enum) {
                        enumList.add(getEnumAttributes((Enum<?>) item));
                    }
                }
                return JSONUtil.toJsonStr(enumList);
            }
            return CollUtil.join(collection, ",");
        }

        if (value.getClass().isArray()) {
            // 检查数组是否包含枚举类型
            Object[] array = (Object[]) value;
            if (array.length > 0 && array[0].getClass().isEnum()) {
                List<Map<String, Object>> enumList = new ArrayList<>();
                for (Object item : array) {
                    if (item instanceof Enum) {
                        enumList.add(getEnumAttributes((Enum<?>) item));
                    }
                }
                return JSONUtil.toJsonStr(enumList);
            }
            return Arrays.toString(array);
        }

        return value.toString();
    }

    /**
     * 格式化单个枚举值
     *
     * @param enumValue 枚举值
     * @return JSON字符串
     */
    private static String formatEnum(Enum<?> enumValue) {
        return JSONUtil.toJsonStr(getEnumAttributes(enumValue));
    }

    /**
     * 获取枚举的属性
     *
     * @param enumValue 枚举值
     * @return 包含枚举属性的Map
     */
    private static Map<String, Object> getEnumAttributes(Enum<?> enumValue) {
        Map<String, Object> result = new HashMap<>();
        result.put("name", enumValue.name());
        
        try {
            // 尝试获取code
            Object code = BeanUtil.getProperty(enumValue, "code");
            if (code != null) {
                result.put("code", code);
            }
            
            // 尝试获取info
            Object info = BeanUtil.getProperty(enumValue, "info");
            if (info != null) {
                result.put("info", info);
            }
        } catch (Exception e) {
            log.debug("获取枚举属性时出错: {}", e.getMessage());
        }
        
        return result;
    }

    /**
     * 设置用户信息
     *
     * @param logBo     修改记录业务对象
     * @param loginUser 登录用户信息
     */
    private static void setUserInfo(ModifyLogBo logBo, LoginUser loginUser) {
        if (loginUser != null) {
            BeanUtil.copyProperties(loginUser, logBo);
        } else {
            log.warn("登录用户信息为空，无法设置修改记录的用户信息");
        }
    }

    /**
     * 设置字段的多语言名称
     *
     * @param logBo     修改记录业务对象
     * @param fieldName 字段名
     */
    private static void setMultiLanguageFieldNames(ModifyLogBo logBo, String fieldName) {
        MessageSource messageSource = null;
        try {
            messageSource = SpringUtils.getBean(MessageSource.class);
        } catch (Exception e) {
            log.debug("无法获取MessageSource，将使用字段名作为显示名称：{}", e.getMessage());
        }

        // 如果没有获取到MessageSource，使用字段名作为显示名称
        if (messageSource == null) {
            setDefaultFieldNames(logBo, fieldName);
            return;
        }

        String baseCode = "entity." + fieldName;

        // 获取并设置各语言字段名
        logBo.setFiledNameZh(getLocalizedFieldName(messageSource, baseCode, fieldName, Locale.SIMPLIFIED_CHINESE));
        logBo.setFiledNameUs(getLocalizedFieldName(messageSource, baseCode, fieldName, Locale.US));
        logBo.setFiledNameTw(getLocalizedFieldName(messageSource, baseCode, fieldName, Locale.TRADITIONAL_CHINESE));
    }

    /**
     * 获取本地化字段名称
     *
     * @param messageSource 消息源
     * @param baseCode      基础编码
     * @param defaultName   默认名称
     * @param locale        语言环境
     * @return 本地化字段名称
     */
    private static String getLocalizedFieldName(MessageSource messageSource, String baseCode, String defaultName, Locale locale) {
        try {
            String localizedName = messageSource.getMessage(baseCode, null, null, locale);
            return StringUtils.isNotEmpty(localizedName) ? localizedName : defaultName;
        } catch (Exception e) {
            log.trace("获取字段本地化名称失败: {}, {}", baseCode, locale, e);
            return defaultName;
        }
    }

    /**
     * 设置默认字段名称（所有语言使用相同名称）
     *
     * @param logBo     修改记录业务对象
     * @param fieldName 字段名
     */
    private static void setDefaultFieldNames(ModifyLogBo logBo, String fieldName) {
        logBo.setFiledNameZh(fieldName);
        logBo.setFiledNameUs(fieldName);
        logBo.setFiledNameTw(fieldName);
    }
}
