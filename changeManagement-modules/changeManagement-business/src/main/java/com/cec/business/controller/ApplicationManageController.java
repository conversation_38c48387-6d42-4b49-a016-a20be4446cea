package com.cec.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.cec.business.domain.bo.ApplicationManageBo;
import com.cec.business.domain.bo.CheckCategoryAndAppBo;
import com.cec.business.domain.vo.ApplicationManageVo;
import com.cec.business.domain.vo.CheckCategoryAndAppVo;
import com.cec.business.domain.vo.NetworkFreezeItemVo;
import com.cec.business.service.IApplicationManageService;
import com.cec.common.core.domain.R;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.idempotent.annotation.RepeatSubmit;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.log.enums.LogTitleEnum;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.web.core.BaseController;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/applicationManage")
public class ApplicationManageController extends BaseController {

    private final IApplicationManageService applicationManageService;

    /**
     * 查询应用管理列表
     */
    @SaCheckPermission("business:applicationManage:list")
    @GetMapping("/list")
    public TableDataInfo<ApplicationManageVo> list(ApplicationManageBo bo, PageQuery pageQuery) {
        return applicationManageService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取应用管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:applicationManage:detail")
    @GetMapping("/{id}")
    public R<ApplicationManageVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long id) {
        return R.ok(applicationManageService.queryById(id));
    }

    /**
     * 获取应用管理列表信息
     *
     * @param ids 主键列表
     */
    @GetMapping("/info/{ids}")
    public R<List<ApplicationManageVo>> getListInfo(@NotEmpty(message = "主键不能为空")
                                                    @PathVariable Long[] ids) {
        return R.ok(applicationManageService.queryListById(ids));
    }

    /**
     * 新增应用管理
     */
    @SaCheckPermission("business:applicationManage:edit")
    @Log(title = LogTitleEnum.APPLICATION_MANAGEMENT, businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ApplicationManageBo bo) {
        return toAjax(applicationManageService.insertByBo(bo));
    }

    /**
     * 修改应用管理
     */
    @SaCheckPermission("business:applicationManage:edit")
    @Log(title = LogTitleEnum.APPLICATION_MANAGEMENT, businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ApplicationManageBo bo) {
        return toAjax(applicationManageService.updateByBo(bo));
    }

    /**
     * 删除应用管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:applicationManage:edit")
    @Log(title = LogTitleEnum.APPLICATION_MANAGEMENT, businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(applicationManageService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 检查分类和应用关系
     *
     * @param bo 请求参数，包含分类IDs和应用IDs
     * @return 返回不存在的分类IDs和应用IDs
     */
    @PostMapping("/check")
    public R<CheckCategoryAndAppVo> checkCategoryAndApplication(@RequestBody @Validated CheckCategoryAndAppBo bo) {
        return R.ok(applicationManageService.checkCategoryAndApplication(bo));
    }

    /**
     * 获取封网
     *
     * @param planTimeStart  计划开始时间
     * @param planTimeEnd    计划结束时间
     * @param applicationIds 应用IDs
     * @return 封网信息列表
     */
    @GetMapping("/freezeByApp")
    public R<List<NetworkFreezeItemVo>> freezeApp(@NotBlank(message = "计划开始时间不能为空") String planTimeStart,
                                                  @NotBlank(message = "计划结束时间不能为空") String planTimeEnd,
                                                  @NotEmpty(message = "应用IDs不能为空") Long[] applicationIds) {
        DateTime timeStart = DateUtil.parse(planTimeStart, "yyyy-MM-dd HH:mm:ss");
        DateTime timeEnd = DateUtil.parse(planTimeEnd, "yyyy-MM-dd HH:mm:ss");

        return R.ok(applicationManageService.queryList(timeStart, timeEnd, applicationIds));
    }
}
