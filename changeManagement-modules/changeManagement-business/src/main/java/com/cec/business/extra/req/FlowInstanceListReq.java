package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 发起流程实例
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowInstanceListReq extends BaseReq implements Serializable {

    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * search 模糊查询
     */
    private String search;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 发起人id
     */
    private String starterId;

    /**
     * 当前页
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 处理结果 -1等待中 0进行中 1已完成 2已拒绝 3已取消
     */
    private List<InstanceStatusEnum> statusList;

    @Getter
    public enum InstanceStatusEnum {
        /**
         * -1等待中 0进行中 1已完成 2已拒绝 3已取消
         */
        WAITING(-1),
        PROCESSING(0),
        COMPLETED(1),
        REJECTED(2),
        CANCELED(3);

        private final int value;

        InstanceStatusEnum(int value) {
            this.value = value;
        }

    }

    @Override
    public String method() {
        return "/openapi/flow/instance/list";
    }
}
