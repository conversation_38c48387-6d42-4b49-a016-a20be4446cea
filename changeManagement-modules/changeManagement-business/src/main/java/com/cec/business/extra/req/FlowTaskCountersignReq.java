package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 任务加减签请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowTaskCountersignReq extends BaseReq implements Serializable {
    /**
     * 处理人id
     */
    @NotEmpty(message = "处理人id不能为空")
    private String assigneeId;

    /**
     * 处理人部门id
     */
    @NotEmpty(message = "处理人部门id不能为空")
    private String assigneeOrgId;

    /**
     * 加签人信息列表
     */
    @NotEmpty(message = "加签人信息列表不能为空")
    @Valid
    private List<FlowTaskHandlerReqVO> countersignList;

    /**
     * 委托人信息
     */
    private FlowTaskHandlerReqVO delegator;

    /**
     * 任务id
     */
    @NotNull(message = "任务id不能为空")
    private Long id;

    /**
     * 操作类型 0：加签 1：减签
     */
    private Integer type;

    @Override
    public String method() {
        return "/openapi/flow/task/countersign";
    }
} 