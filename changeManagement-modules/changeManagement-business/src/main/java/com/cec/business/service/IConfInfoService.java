package com.cec.business.service;

import com.cec.business.domain.vo.ConfInfoVo;
import com.cec.business.domain.bo.ConfInfoBo;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 配置信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface IConfInfoService {

    /**
     * 查询配置信息
     *
     * @param id 主键
     * @return 配置信息
     */
    ConfInfoVo queryById(Long id);

    /**
     * 分页查询配置信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 配置信息分页列表
     */
    TableDataInfo<ConfInfoVo> queryPageList(ConfInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的配置信息列表
     *
     * @param bo 查询条件
     * @return 配置信息列表
     */
    List<ConfInfoVo> queryList(ConfInfoBo bo);

    /**
     * 新增配置信息
     *
     * @param bo 配置信息
     * @return 是否新增成功
     */
    Boolean insertByBo(ConfInfoBo bo);

    /**
     * 校验名称是否唯一
     *
     * @param bo 配置信息
     * @return 结果
     */
    Boolean checkNameUnique(ConfInfoBo bo);

    /**
     * 修改配置信息
     *
     * @param bo 配置信息
     * @return 是否修改成功
     */
    Boolean updateByBo(ConfInfoBo bo);

    /**
     * 校验并批量删除配置信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
