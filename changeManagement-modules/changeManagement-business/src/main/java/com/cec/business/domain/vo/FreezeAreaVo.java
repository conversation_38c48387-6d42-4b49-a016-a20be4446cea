package com.cec.business.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FreezeAreaVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 区域id
     */
    private String areaId;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 区域地区-英文
     */
    private String areaNameEn;
}
