package com.cec.business.service;

import com.cec.business.domain.bo.ChangeItemBo;
import com.cec.business.domain.bo.ChangeRecordBo2;
import com.cec.business.domain.vo.ChangeInfoVo;
import com.cec.business.domain.bo.ChangeInfoBo;
import com.cec.business.domain.vo.ChangeItemVo;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 变更申请Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IChangeInfoService {

    /**
     * 查询变更申请
     *
     * @param id 主键
     * @return 变更申请
     */
    ChangeInfoVo queryById(Long id);

    /**
     * 分页查询变更申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 变更申请分页列表
     */
    TableDataInfo<ChangeInfoVo> queryPageList(ChangeInfoBo bo, PageQuery pageQuery);

    /**
     * 分页查询变更申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 变更申请分页列表
     */
    TableDataInfo<ChangeItemVo> queryPageList(ChangeItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的变更申请列表
     *
     * @param bo 查询条件
     * @return 变更申请列表
     */
    List<ChangeInfoVo> queryList(ChangeInfoBo bo);

    /**
     * 新增变更申请
     *
     * @param bo 变更申请
     * @return 是否新增成功
     */
    Long insertByBo(ChangeInfoBo bo);

    /**
     * 修改变更申请
     *
     * @param bo 变更申请
     * @return 是否修改成功
     */
    Boolean updateByBo(ChangeInfoBo bo);

    /**
     * 校验并批量删除变更申请信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 审批
     *
     * @param bo 变更申请
     * @return 是否修改成功
     */
    void updateByBo(ChangeRecordBo2 bo);

    /**
     * 取消变更流程
     *
     * @param infoId 变更申请ID
     * @return 是否取消成功
     */
    Boolean cancelChange(Long infoId);

    /**
     * 获取未完成变更申请
     *
     * @param pageQuery 分页参数
     * @return 获取未完成变更申请
     */
    TableDataInfo<ChangeItemVo> getChangeUnFinished(PageQuery pageQuery);

}
