package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 查询任务处理策略
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FlowTaskStrategyResp extends BaseResp implements Serializable {
    /**
     * 查询任务处理策略
     */
    private FlowTaskStrategy data;

    @Data
    public static class FlowTaskStrategy implements Serializable {
        /**
         * 转交（指派）类型 0：不能转交 1：全组织架构人员 2：仅审批相关人员
         */
        private Integer assignType;
        /**
         * 回退类型 0：不能回退 1：回退至发起人 2：审批人自选
         */
        private Integer backType;
        /**
         * 是否需要填写审批意见 0：否 1：是
         */
        private Integer comment;
        /**
         * 是否允许加减签 0：否 1：是
         */
        private Integer countersign;
        /**
         * 会签类型 0：会签 1：或签
         */
        private Integer countersignType;
        /**
         * 表单可编辑字段列表
         */
        private List<String> editFieldList;
        /**
         * 处理者类型
         */
        private List<String> handlerTypeList;
        /**
         * 表单联系人
         */
        private String outerFormFieldName;
        /**
         * 表单只读字段列表
         */
        private List<String> readFieldList;
        /**
         * 是否需要签名 0：否 1：是
         */
        private Integer signature;
        /**
         * 节点类型 USER_TASK、TRANSACT、COPY_FOR等
         */
        private String type;

    }

}
