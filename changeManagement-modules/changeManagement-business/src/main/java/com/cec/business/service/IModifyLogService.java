package com.cec.business.service;

import com.cec.business.domain.vo.ModifyLogVo;
import com.cec.business.domain.bo.ModifyLogBo;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 变更修改记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IModifyLogService {

    /**
     * 查询变更修改记录
     *
     * @param id 主键
     * @return 变更修改记录
     */
    ModifyLogVo queryById(Long id);

    /**
     * 分页查询变更修改记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 变更修改记录分页列表
     */
    TableDataInfo<ModifyLogVo> queryPageList(ModifyLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的变更修改记录列表
     *
     * @param bo 查询条件
     * @return 变更修改记录列表
     */
    List<ModifyLogVo> queryList(ModifyLogBo bo);

    /**
     * 新增变更修改记录
     *
     * @param bo 变更修改记录
     * @return 是否新增成功
     */
    Boolean insertByBo(ModifyLogBo bo);

    /**
     * 修改变更修改记录
     *
     * @param bo 变更修改记录
     * @return 是否修改成功
     */
    Boolean updateByBo(ModifyLogBo bo);

    /**
     * 校验并批量删除变更修改记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量插入变更修改记录
     *
     * @param boList 变更修改记录列表
     * @return 是否批量插入成功
     */
    Boolean batchInsert(List<ModifyLogBo> boList);

    /**
     * 异步批量插入变更修改记录
     *
     * @param boList 变更修改记录列表
     * @return 异步执行结果
     */
    CompletableFuture<Boolean> asyncBatchInsert(List<ModifyLogBo> boList);

    /**
     * 比较并记录两个对象的差异
     *
     * @param oldObj 旧对象
     * @param newObj 新对象
     * @param code 变更ID
     * @param ignoreFields 忽略比较的字段集合
     * @return 异步执行结果
     */
    CompletableFuture<Boolean> compareAndLogDifferences(Object oldObj, Object newObj, String code, Set<String> ignoreFields);
    
    /**
     * 比较并记录两个对象的差异（包含用户信息）
     *
     * @param oldObj 旧对象
     * @param newObj 新对象
     * @param code 变更ID
     * @param ignoreFields 忽略比较的字段集合
     * @param loginUser 当前登录用户
     * @return 异步执行结果
     */
    CompletableFuture<Boolean> compareAndLogDifferences(Object oldObj, Object newObj, String code, Set<String> ignoreFields, LoginUser loginUser);
}
