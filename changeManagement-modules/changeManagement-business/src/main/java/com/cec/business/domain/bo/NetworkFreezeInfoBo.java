package com.cec.business.domain.bo;

import com.cec.business.domain.enums.NetworkFreezeLevelEnum;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.business.domain.vo.PeriodAndAreaVo;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 封网申请业务对象 cm_network_freeze_info
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class NetworkFreezeInfoBo {

    /**
     * ID
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 封网名称
     */
    @NotBlank(message = "封网名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 封网等级（1-一级 2-二级 3-三级）
     */
    @NotNull(message = "封网等级不能为空", groups = { AddGroup.class, EditGroup.class })
    private NetworkFreezeLevelEnum level;

    /**
     * 封网地区和时间;
     */
    private List<PeriodAndAreaVo> periodAndArea;

    /**
     * 附件ids
     */
    private List<Long> fileIds;

    /**
     * 是否增加到邮件附件（1-是 2否）
     */
    private WhetherEnum isAttachment;

    /**
     * 通知电邮地址
     */
    private List<String> notificationEmailTo;

    /**
     * 通知抄送电邮地址
     */
    private List<String> notificationEmailCc;

    /**
     * 特殊备注
     */
    private String specialRemark;

    /**
     * 邮件内容
     */
    private String emailContent;


    /**
     * 阶段(1-草稿/2-已提交/3已发布/4-已拒绝)
     */
    @NotNull(message = "阶段不能为空", groups = { AddGroup.class, EditGroup.class })
    private NetworkFreezeStageEnum stage;

    /**
     * 封禁应用
     */
    private String freezeApps;

    private String code;

}
