package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 批量查询任务下一节点请求的单个元素
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowTaskNextReqVO implements Serializable {
    /**
     * 处理人id
     */
    @NotEmpty(message = "处理人id不能为空")
    private String assigneeId;
    /**
     * 处理人部门id
     */
    @NotEmpty(message = "处理人部门id不能为空")
    private String assigneeOrgId;
    /**
     * 表单Key（任务id和表单Key不能同时为空）
     */
    private String formKey;
    /**
     * 任务id（任务id和表单Key不能同时为空）
     */
    private Long id;
    /**
     * 流程执行变量
     */
    private Map<String, Object> variables;
} 