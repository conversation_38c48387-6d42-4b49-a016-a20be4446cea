package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 受影响系统个数枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum SystemsAffectedNoEnum {
    /**
     * None
     */
    NONE(1, "None", 1),
    /**
     * 1 System
     */
    ONE_SYSTEM(2, "1 System", 2),
    /**
     * 2 Systems
     */
    TWO_SYSTEMS(3, "2 Systems", 3),
    /**
     * 3 or more Systems
     */
    THREE_OR_MORE_SYSTEMS(4, "3 or more Systems", 4);

    @EnumValue
    private final Integer code;
    private final String info;
    private final Integer score;

    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static SystemsAffectedNoEnum findByCode(Integer code) {
        for (SystemsAffectedNoEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static SystemsAffectedNoEnum findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
} 