package com.cec.business.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 配置信息类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ConfInfoType {
    /**
     * 分类
     */
    CLASSIFICATION(1, "分类"),
    /**
     * 封网地区
     */
    NETWORK_BLOCKING_AREA(2, "封网地区"),
    /**
     * 变更地点
     */
    CHANGE_LOCATION(3, "变更地点"),
    /**
     * 受影响设备
     */
    AFFECTED_DEVICE(4, "受影响设备"),
    /**
     * 维护种类
     */
    MAINTENANCE_KIND(5, "维护种类"),
    ;

    @EnumValue
    private final Integer code;
    private final String info;

    /**
     * 根据code获取枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ConfInfoType findByCode(Integer code) {
        for (ConfInfoType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据字符串code获取枚举
     */
    @JsonCreator
    public static ConfInfoType findByCode(String code) {
        if (code == null) {
            return null;
        }
        try {
            Integer codeInt = Integer.parseInt(code);
            return findByCode(codeInt);
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
