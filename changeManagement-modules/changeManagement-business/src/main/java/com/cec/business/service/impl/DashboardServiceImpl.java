package com.cec.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cec.business.domain.ChangeInfo;
import com.cec.business.domain.ChangeItem;
import com.cec.business.domain.NetworkFreezeArea;
import com.cec.business.domain.NetworkFreezeItem;
import com.cec.business.domain.bo.ChangeRecordBo;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.business.domain.vo.CalendarEventVo;
import com.cec.business.domain.vo.ChangeDailyListVo;
import com.cec.business.domain.vo.ChangeItemVo;
import com.cec.business.domain.vo.ChangeRecordVo;
import com.cec.business.domain.vo.NetworkFreezeInfoVo;
import com.cec.business.mapper.ChangeInfoMapper;
import com.cec.business.mapper.ChangeItemMapper;
import com.cec.business.mapper.NetworkFreezeAreaMapper;
import com.cec.business.mapper.NetworkFreezeInfoMapper;
import com.cec.business.mapper.NetworkFreezeItemMapper;
import com.cec.business.service.IChangeInfoService;
import com.cec.business.service.IChangeRecordService;
import com.cec.business.service.IDashboardService;
import com.cec.business.service.INetworkFreezeService;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Dashboard服务实现类
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class DashboardServiceImpl implements IDashboardService {

    private final IChangeInfoService changeInfoService;
    private final IChangeRecordService changeRecordService;
    private final INetworkFreezeService networkFreezeService;

    private final ChangeInfoMapper changeInfoMapper;
    private final ChangeItemMapper changeItemMapper;
    private final NetworkFreezeInfoMapper networkFreezeInfoMapper;
    private final NetworkFreezeAreaMapper networkFreezeAreaMapper;
    private final NetworkFreezeItemMapper networkFreezeItemMapper;

    /**
     * 获取封网信息
     */
    @Override
    public List<NetworkFreezeInfoVo> getNetworkFreezeInfo(Date date) {
        return networkFreezeService.getListNetworkFreezeInfoVo(date);
    }

    /**
     * 获取未完结变更
     */
    @Override
    public TableDataInfo<ChangeItemVo> getChangeUnFinished(PageQuery pageQuery) {
        return changeInfoService.getChangeUnFinished(pageQuery);
    }

    /**
     * 获取待办列表
     */
    @Override
    public TableDataInfo<ChangeRecordVo> getChangeTodo(ChangeRecordBo bo, PageQuery pageQuery) {
        bo.setProcessorStatus(ProcessorStatusEnum.PENDING);
        return changeRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取日历事件数据
     */
    @Override
    public List<CalendarEventVo> getCalendarEvents(String yearMonth) {
        // 解析年月参数
        LocalDate now = LocalDate.now();
        int[] yearMonthArray = parseYearMonth(yearMonth, now);
        int targetYear = yearMonthArray[0];
        int targetMonth = yearMonthArray[1];

        // 计算查询日期范围
        LocalDate monthStart = LocalDate.of(targetYear, targetMonth, 1);
        LocalDate monthEnd = monthStart.with(TemporalAdjusters.lastDayOfMonth());
        LocalDate startDate = monthStart.minusDays(10);
        LocalDate endDate = monthEnd.plusDays(10);

        Date start = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 查询并处理变更数据
        Map<LocalDate, CalendarEventVo> eventMap = new HashMap<>();
        processChangeEvents(start, end, startDate, endDate, eventMap);

        // 查询并处理封网数据
        processFreezeEvents(start, end, startDate, endDate, eventMap);

        return new ArrayList<>(eventMap.values());
    }

    private int[] parseYearMonth(String yearMonth, LocalDate now) {
        int targetYear = now.getYear();
        int targetMonth = now.getMonthValue();

        if (yearMonth != null && yearMonth.length() == 6) {
            try {
                int year = Integer.parseInt(yearMonth.substring(0, 4));
                int month = Integer.parseInt(yearMonth.substring(4, 6));

                if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12) {
                    targetYear = year;
                    targetMonth = month;
                }
            } catch (NumberFormatException e) {
                log.warn("Invalid yearMonth format: {}, using current date", yearMonth);
            }
        }

        return new int[]{targetYear, targetMonth};
    }

    private void processChangeEvents(Date start, Date end, LocalDate startDate, LocalDate endDate,
                                   Map<LocalDate, CalendarEventVo> eventMap) {
        // 查询变更信息
        LambdaQueryWrapper<ChangeInfo> changeInfoQuery = Wrappers.lambdaQuery(ChangeInfo.class)
            .le(ChangeInfo::getPlanTimeStart, end)
            .apply("IFNULL(delay_end_time, plan_time_end) >= {0}", start);

        List<ChangeInfo> changeInfoList = changeInfoMapper.selectList(changeInfoQuery);
        if (changeInfoList.isEmpty()) {
            return;
        }

        // 查询变更项目信息
        List<Long> changeIds = changeInfoList.stream().map(ChangeInfo::getId).toList();
        Map<Long, ChangeItem> changeItemMap = getChangeItemMap(changeIds);

        // 处理变更事件
        for (ChangeInfo changeInfo : changeInfoList) {
            ChangeItem item = changeItemMap.get(changeInfo.getId());
            if (item == null) {
                continue;
            }

            LocalDate changeStartDate = toLocalDate(changeInfo.getPlanTimeStart());
            LocalDate changeEndDate = toLocalDate(
                changeInfo.getDelayEndTime() != null ? changeInfo.getDelayEndTime() : changeInfo.getPlanTimeEnd()
            );

            if (changeStartDate == null || changeEndDate == null) {
                continue;
            }

            addChangeEventsToCalendar(changeInfo, item, changeStartDate, changeEndDate, startDate, endDate, eventMap);
        }
    }

    private void processFreezeEvents(Date start, Date end, LocalDate startDate, LocalDate endDate,
                                   Map<LocalDate, CalendarEventVo> eventMap) {
        // 查询封网区域信息
        LambdaQueryWrapper<NetworkFreezeArea> freezeAreaQuery = Wrappers.lambdaQuery(NetworkFreezeArea.class)
            .and(wrapper -> wrapper
                .and(w -> w.ge(NetworkFreezeArea::getStartTime, start).le(NetworkFreezeArea::getStartTime, end))
                .or(w -> w.ge(NetworkFreezeArea::getEndTime, start).le(NetworkFreezeArea::getEndTime, end))
                .or(w -> w.le(NetworkFreezeArea::getStartTime, start).ge(NetworkFreezeArea::getEndTime, end))
            );

        List<NetworkFreezeArea> freezeAreaList = networkFreezeAreaMapper.selectList(freezeAreaQuery);
        if (freezeAreaList.isEmpty()) {
            return;
        }

        // 获取有效的封网项目信息
        List<Long> freezeIds = freezeAreaList.stream().map(NetworkFreezeArea::getFreezeId).distinct().toList();
        Map<Long, NetworkFreezeItem> freezeItemMap = getFreezeItemMap(freezeIds);

        // 处理封网事件
        for (NetworkFreezeArea freezeArea : freezeAreaList) {
            NetworkFreezeItem item = freezeItemMap.get(freezeArea.getFreezeId());
            if (item == null) {
                continue; // 过滤掉不在freezeItems里的id
            }

            LocalDate freezeStartDate = toLocalDate(freezeArea.getStartTime());
            LocalDate freezeEndDate = toLocalDate(freezeArea.getEndTime());

            if (freezeStartDate == null || freezeEndDate == null) {
                continue;
            }

            addFreezeEventsToCalendar(freezeArea, item, freezeStartDate, freezeEndDate, startDate, endDate, eventMap);
        }
    }

    private Map<Long, ChangeItem> getChangeItemMap(List<Long> changeIds) {
        LambdaQueryWrapper<ChangeItem> changeItemQuery = Wrappers.lambdaQuery(ChangeItem.class)
            .in(ChangeItem::getChangeId, changeIds)
            .ne(ChangeItem::getStage, ChangeStageEnum.DRAFT);
        List<ChangeItem> changeItems = changeItemMapper.selectList(changeItemQuery);
        return changeItems.stream()
            .collect(Collectors.toMap(ChangeItem::getChangeId, item -> item, (a, b) -> a));
    }

    private Map<Long, NetworkFreezeItem> getFreezeItemMap(List<Long> freezeIds) {
        LambdaQueryWrapper<NetworkFreezeItem> freezeItemQuery = Wrappers.lambdaQuery(NetworkFreezeItem.class)
            .in(NetworkFreezeItem::getFreezeId, freezeIds)
            .in(NetworkFreezeItem::getStage, List.of(NetworkFreezeStageEnum.PUBLISHED));
        List<NetworkFreezeItem> freezeItems = networkFreezeItemMapper.selectList(freezeItemQuery);
        return freezeItems.stream()
            .collect(Collectors.toMap(NetworkFreezeItem::getFreezeId, item -> item, (a, b) -> a));
    }

    private void addChangeEventsToCalendar(ChangeInfo changeInfo, ChangeItem item,
                                         LocalDate startDate, LocalDate endDate,
                                         LocalDate rangeStart, LocalDate rangeEnd,
                                         Map<LocalDate, CalendarEventVo> eventMap) {
        LocalDate current = startDate;
        while (!current.isAfter(endDate)) {
            if (isInDateRange(current, rangeStart, rangeEnd)) {
                CalendarEventVo eventVo = eventMap.computeIfAbsent(current, this::createNewEventVo);

                CalendarEventVo.ChangeInfoEvent changeEvent = new CalendarEventVo.ChangeInfoEvent();
                changeEvent.setChangeInfoId(changeInfo.getId());
                changeEvent.setTitle(changeInfo.getTitle());
                changeEvent.setStage(item.getStage());

                eventVo.getChangeEvents().add(changeEvent);
            }
            current = current.plusDays(1);
        }
    }

    private void addFreezeEventsToCalendar(NetworkFreezeArea freezeArea, NetworkFreezeItem item,
                                         LocalDate startDate, LocalDate endDate,
                                         LocalDate rangeStart, LocalDate rangeEnd,
                                         Map<LocalDate, CalendarEventVo> eventMap) {
        LocalDate current = startDate;
        while (!current.isAfter(endDate)) {
            if (isInDateRange(current, rangeStart, rangeEnd)) {
                CalendarEventVo eventVo = eventMap.computeIfAbsent(current, this::createNewEventVo);

                CalendarEventVo.NetworkFreezeEvent freezeEvent = new CalendarEventVo.NetworkFreezeEvent();
                freezeEvent.setFreezeId(freezeArea.getFreezeId());
                freezeEvent.setStage(item.getStage());

                eventVo.getFreezeEvents().add(freezeEvent);
            }
            current = current.plusDays(1);
        }
    }

    /**
     * 判断日期是否在目标日期范围内（包含前后10天的扩展范围）
     */
    private boolean isInDateRange(LocalDate date, LocalDate startDate, LocalDate endDate) {
        return !date.isBefore(startDate) && !date.isAfter(endDate);
    }

    /**
     * 创建新的日历事件VO对象
     */
    private CalendarEventVo createNewEventVo(LocalDate date) {
        CalendarEventVo vo = new CalendarEventVo();
        vo.setDate(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        vo.setChangeEvents(new ArrayList<>());
        vo.setFreezeEvents(new ArrayList<>());
        return vo;
    }

    /**
     * 将Date转换为LocalDate
     */
    private LocalDate toLocalDate(Date date) {
        return date != null ? date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate() : null;
    }

    /**
     * 判断日期是否在目标年月内
     */
    private boolean isInMonth(LocalDate date, int year, int month) {
        return date.getYear() == year && date.getMonthValue() == month;
    }

    /**
     * 获取特定日期的变更列表数据
     */
    @Override
    public List<ChangeDailyListVo> getChangeDailyList(String dateStr) {
        Date date = null;
        try {
            // 解析日期字符串
            if (dateStr != null && !dateStr.isEmpty()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                date = sdf.parse(dateStr);
            } else {
                // 如果日期为空，默认使用当前日期
                date = new Date();
            }
        } catch (ParseException e) {
            log.warn("Invalid date format: {}, using current date", dateStr);
            date = new Date();
        }

        // 设置日期的开始时间和结束时间
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Date startDate = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(localDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().minusMillis(1));

        // 查询该日期范围内的变更信息
//        LambdaQueryWrapper<ChangeInfo> changeInfoQuery = Wrappers.lambdaQuery(ChangeInfo.class)
//            .and(wrapper -> wrapper
//                // 1开始时间落在 [start, end]
//                .and(w -> w.ge(ChangeInfo::getPlanTimeStart, startDate)
//                    .le(ChangeInfo::getPlanTimeStart, endDate))
//                // 2有效结束时间落在 [start, end]
//                .or(w -> w.apply("COALESCE(delay_end_time, plan_time_end) >= {0}", startDate)
//                    .apply("COALESCE(delay_end_time, plan_time_end) <= {0}", endDate))
//                // 3记录区间完全覆盖 [start, end]
//                .or(w -> w.le(ChangeInfo::getPlanTimeStart, startDate)
//                    .apply("COALESCE(delay_end_time, plan_time_end) >= {0}", endDate))
//            )


        LambdaQueryWrapper<ChangeInfo> changeInfoQuery = Wrappers.lambdaQuery(ChangeInfo.class)
            // plan_time_start <= end
            .le(ChangeInfo::getPlanTimeStart, endDate)
            // IFNULL(delay_end_time, plan_time_end) >= start
            .apply("IFNULL(delay_end_time, plan_time_end) >= {0}", startDate).orderByDesc(ChangeInfo::getId);

        List<ChangeInfo> changeInfoList = changeInfoMapper.selectList(changeInfoQuery);

        if (changeInfoList.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询对应的ChangeItem信息，获取变更编号和阶段
        List<Long> changeIds = changeInfoList.stream().map(ChangeInfo::getId).toList();
        Map<Long, ChangeItem> changeItemMap = Collections.emptyMap();

        LambdaQueryWrapper<ChangeItem> changeItemQuery = Wrappers.lambdaQuery(ChangeItem.class)
            .in(ChangeItem::getChangeId, changeIds)
            .ne(ChangeItem::getStage, ChangeStageEnum.DRAFT);
        List<ChangeItem> changeItems = changeItemMapper.selectList(changeItemQuery);
        changeItemMap = changeItems.stream()
            .collect(Collectors.toMap(ChangeItem::getChangeId, item -> item, (a, b) -> a));

        // 构建返回结果
        List<ChangeDailyListVo> resultList = new ArrayList<>(changeInfoList.size());

        for (ChangeInfo changeInfo : changeInfoList) {
            ChangeItem item = changeItemMap.get(changeInfo.getId());
            if (item == null) {
                continue;
            }

            ChangeDailyListVo vo = new ChangeDailyListVo();
            vo.setId(changeInfo.getId());
            vo.setChangeCode(item.getChangeCode());
            vo.setTitle(changeInfo.getTitle());
            vo.setStage(item.getStage());
            vo.setTeamName(changeInfo.getTeamName());
            vo.setRequester(changeInfo.getRequester());
            vo.setPlanTimeStart(changeInfo.getPlanTimeStart());
            vo.setPlanTimeEnd(changeInfo.getPlanTimeEnd());

            resultList.add(vo);
        }

        return resultList;
    }
}
