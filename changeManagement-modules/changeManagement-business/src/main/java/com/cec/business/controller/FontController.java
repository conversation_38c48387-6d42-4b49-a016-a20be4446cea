package com.cec.business.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 字体文件控制器
 * 提供字体文件下载服务
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/font")
@SaIgnore
public class FontController {

    /**
     * 下载字体文件
     * 返回钉钉伙伴字体文件流
     *
     * @return 字体文件流
     */
    @GetMapping()
    public ResponseEntity<Resource> downloadFont() {
        try {
            // 字体文件在classpath中的路径
            String fontPath = "font/dingliehuobanfont20241217-2.ttf";

            // 创建ClassPath资源对象
            Resource resource = new ClassPathResource(fontPath);

            // 检查资源是否存在
            if (!resource.exists()) {
                log.error("字体文件不存在: {}", fontPath);
                return ResponseEntity.notFound().build();
            }

            // 检查资源是否可读
            if (!resource.isReadable()) {
                log.error("字体文件不可读: {}", fontPath);
                return ResponseEntity.notFound().build();
            }

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"dingliehuobanfont20241217-2.ttf\"");
            headers.add(HttpHeaders.CONTENT_TYPE, "application/octet-stream");
            headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            headers.add(HttpHeaders.PRAGMA, "no-cache");
            headers.add(HttpHeaders.EXPIRES, "0");

            log.info("成功提供字体文件下载: {}", fontPath);

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(resource.contentLength())
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);

        } catch (Exception e) {
            log.error("下载字体文件时发生错误", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
