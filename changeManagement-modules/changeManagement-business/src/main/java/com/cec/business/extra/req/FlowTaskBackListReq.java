package com.cec.business.extra.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/***
 * 待回退节点列表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlowTaskBackListReq extends BaseReq implements Serializable {
    /**
     * 处理人id
     */
    @NotEmpty(message = "处理人id不能为空")
    private String assigneeId;
    /**
     * 处理人部门id
     */
    @NotEmpty(message = "处理人部门id不能为空")
    private String assigneeOrgId;
    /**
     * 任务id（任务id和表单Key不能同时为空）
     */
    @NotNull(message = "任务id不能为空")
    private Long id;

    @Override
    public String method() {
        return "/openapi/flow/task/back/list";
    }
}
