package com.cec.business.extra.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 获取待办任务列表响应
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlowTaskTodoListResp extends BaseResp {
    /**
     * 响应数据
     */
    private PageData data;
    
    /**
     * 分页数据
     */
    @Data
    public static class PageData {
        /**
         * 当前页
         */
        private Integer current;
        
        /**
         * 是否命中
         */
        private Boolean hitCount;
        
        /**
         * 总页数
         */
        private Integer pages;
        
        /**
         * 记录列表
         */
        private List<TaskRecord> records;
        
        /**
         * 是否进行count查询
         */
        private Boolean searchCount;
        
        /**
         * 每页大小
         */
        private Integer size;
        
        /**
         * 总记录数
         */
        private Integer total;
    }
    
    /**
     * 任务记录
     */
    @Data
    public static class TaskRecord {
        /**
         * 处理人ID
         */
        private Long assigneeId;
        
        /**
         * 处理人姓名
         */
        private String assigneeName;
        
        /**
         * 处理人组织ID
         */
        private Long assigneeOrgId;
        
        /**
         * 处理人组织名称
         */
        private String assigneeOrgName;
        
        /**
         * 业务ID
         */
        private Long businessId;
        
        /**
         * 审批意见
         */
        private String comment;
        
        /**
         * 创建用户
         */
        private Long createUser;
        
        /**
         * 委托人ID
         */
        private Long delegatorId;
        
        /**
         * 委托人姓名
         */
        private String delegatorName;
        
        /**
         * 委托人组织ID
         */
        private Long delegatorOrgId;
        
        /**
         * 委托人组织名称
         */
        private String delegatorOrgName;
        
        /**
         * 结束时间
         */
        private String endTime;
        
        /**
         * 表单ID
         */
        private Long formId;
        
        /**
         * 表单Key
         */
        private Long formKey;
        
        /**
         * 创建时间
         */
        private String gmt8Create;
        
        /**
         * 修改时间
         */
        private String gmt8Modified;
        
        /**
         * 任务ID
         */
        private Long id;
        
        /**
         * 流程实例ID
         */
        private Long instanceId;
        
        /**
         * 任务名称
         */
        private String name;
        
        /**
         * 节点ID
         */
        private String nodeId;
        
        /**
         * 节点名称
         */
        private String nodeName;
        
        /**
         * 前一个任务ID
         */
        private Long prevId;
        
        /**
         * 开始时间
         */
        private String startTime;
        
        /**
         * 发起人ID
         */
        private Long starterId;
        
        /**
         * 发起人姓名
         */
        private String starterName;
        
        /**
         * 发起人组织ID
         */
        private Long starterOrgId;
        
        /**
         * 发起人组织名称
         */
        private String starterOrgName;
        
        /**
         * 状态
         */
        private Integer status;
        
        /**
         * 任务类型
         */
        private Integer taskType;
        
        /**
         * 更新用户
         */
        private Long updateUser;
    }
} 