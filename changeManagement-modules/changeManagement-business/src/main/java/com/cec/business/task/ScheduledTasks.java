package com.cec.business.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cec.business.domain.ChangeItem;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.mapper.ChangeItemMapper;
import com.cec.business.service.EmailService;
import com.cec.common.core.utils.DateUtils;
import com.cec.common.redis.utils.RedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ScheduledTasks {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ChangeItemMapper changeItemMapper;
    @Resource
    private EmailService emailService;

    // @Scheduled(cron = "0/20 * * * * ?")
    public void executeTask() {
        String lockKey = "scheduled:task:executeTask";
        RLock lock = redissonClient.getLock(lockKey);
        long startTime = System.currentTimeMillis();

        try {
            log.debug("尝试获取定时任务锁: {}", lockKey);
            boolean isLocked = lock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                try {
                    log.info("成功获取到锁 [{}]，开始执行定时任务", lockKey);
                    // 在这里添加实际的定时任务逻辑

                    long executionTime = System.currentTimeMillis() - startTime;
                    log.info("定时任务执行完成，耗时: {}ms", executionTime);
                } catch (Exception e) {
                    log.error("定时任务执行异常", e);
                } finally {
                    // 释放锁
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                        log.debug("已释放锁: {}", lockKey);
                    }
                }
            }
        } catch (InterruptedException e) {
            log.error("获取分布式锁异常: {}", lockKey, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("定时任务锁处理过程中发生未预期异常", e);
        }
    }

    /**
     * 超时7天未关闭
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void executeTask1() {
        String lockKey = "scheduled:task:executeTask1";
        RLock lock = redissonClient.getLock(lockKey);
        long startTime = System.currentTimeMillis();

        try {
            log.debug("尝试获取定时任务锁: {}", lockKey);
            boolean isLocked = lock.tryLock(0, 30, TimeUnit.SECONDS);
            if (isLocked) {
                try {
                    log.info("成功获取到锁 [{}]，开始执行定时任务", lockKey);
                    task1();
                    long executionTime = System.currentTimeMillis() - startTime;
                    log.info("定时任务执行完成，耗时: {}ms", executionTime);
                } catch (Exception e) {
                    log.error("定时任务执行异常", e);
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                        log.debug("已释放锁: {}", lockKey);
                    }
                }
            }
        } catch (InterruptedException e) {
            log.error("获取分布式锁异常: {}", lockKey, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("定时任务锁处理过程中发生未预期异常", e);
        }
    }

    private void task1() {
        Date date = new Date();
        Date addDays = DateUtils.addDays(date, -7);

        List<ChangeItem> changeItems = changeItemMapper.selectList(new LambdaQueryWrapper<ChangeItem>()
            .notIn(ChangeItem::getStage, List.of(ChangeStageEnum.COMPLETED, ChangeStageEnum.REJECTED, ChangeStageEnum.ROLLED_BACK, ChangeStageEnum.CANCELLED))
            .lt(ChangeItem::getSubmitTime, addDays));

        if (!changeItems.isEmpty()) {
            changeItems.parallelStream().forEach(changeItem -> sendEmailAsync(ChangeStageEnum.OVER_7_DAYS, changeItem.getChangeId()));
        }
    }

    /**
     * 异步发送变更状态邮件
     *
     * @param stage    变更阶段
     * @param changeId 变更ID
     */
    private void sendEmailAsync(ChangeStageEnum stage, Long changeId) {
        CompletableFuture.runAsync(() -> {
            try {
                emailService.sendEmailByChangeInfoId(stage, changeId);
                log.info("异步发送变更超时状态邮件成功, stage: {}, changeId: {}", stage, changeId);
            } catch (Exception e) {
                log.error("异步发送变更超时状态邮件失败, stage: {}, changeId: {}", stage, changeId, e);
            }
        });
    }
}
