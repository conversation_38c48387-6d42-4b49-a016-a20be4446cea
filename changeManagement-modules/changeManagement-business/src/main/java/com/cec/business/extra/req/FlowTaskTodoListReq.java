package com.cec.business.extra.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 获取待办任务列表请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlowTaskTodoListReq extends BaseReq {
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 排序项列表
     */
    private List<OrderItem> orderItemList;
    
    /**
     * 页码
     */
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 搜索关键词
     */
    private String search;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 发起人ID
     */
    private String starterId;
    
    /**
     * 状态列表
     */
    private List<Integer> statusList;
    
    /**
     * 任务类型列表
     */
    private Integer taskTypeList;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 排序项内部类
     */
    @Data
    public static class OrderItem {
        /**
         * 是否升序
         */
        private Boolean asc;
        
        /**
         * 排序列名
         */
        private String column;
    }
    
    @Override
    public String method() {
        return "/openapi/flow/task/list/todo";
    }
} 