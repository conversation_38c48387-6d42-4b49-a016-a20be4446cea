package com.cec.business.service;

import com.cec.business.domain.vo.NetworkFreezeRecordVo;
import com.cec.business.domain.bo.NetworkFreezeRecordBo;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 封网申请记录流水Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface INetworkFreezeRecordService {

    /**
     * 查询封网申请记录流水
     *
     * @param id 主键
     * @return 封网申请记录流水
     */
    NetworkFreezeRecordVo queryById(Long id);

    /**
     * 分页查询封网申请记录流水列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 封网申请记录流水分页列表
     */
    TableDataInfo<NetworkFreezeRecordVo> queryPageList(NetworkFreezeRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的封网申请记录流水列表
     *
     * @param bo 查询条件
     * @return 封网申请记录流水列表
     */
    List<NetworkFreezeRecordVo> queryList(NetworkFreezeRecordBo bo);

    /**
     * 新增封网申请记录流水
     *
     * @param bo 封网申请记录流水
     * @return 是否新增成功
     */
    Boolean insertByBo(NetworkFreezeRecordBo bo);

    /**
     * 修改封网申请记录流水
     *
     * @param bo 封网申请记录流水
     * @return 是否修改成功
     */
    Boolean updateByBo(NetworkFreezeRecordBo bo);

    /**
     * 校验并批量删除封网申请记录流水信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
