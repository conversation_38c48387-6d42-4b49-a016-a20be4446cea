<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cec</groupId>
        <artifactId>changeManagement-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>changeManagement-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-translation</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cec</groupId>
            <artifactId>changeManagement-common-encrypt</artifactId>
        </dependency>

    </dependencies>

</project>
