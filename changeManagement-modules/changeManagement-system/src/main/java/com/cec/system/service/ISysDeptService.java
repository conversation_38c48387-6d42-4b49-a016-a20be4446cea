package com.cec.system.service;

import cn.hutool.core.lang.tree.Tree;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.system.domain.bo.SysDeptBo;
import com.cec.system.domain.vo.SysDeptVo;

import java.util.List;

/**
 * 分组管理 服务层
 *
 * <AUTHOR> Li
 */
public interface ISysDeptService {

    /**
     * 分页查询分组管理数据
     *
     * @param dept      分组信息
     * @param pageQuery 分页对象
     * @return 分组信息集合
     */
    TableDataInfo<SysDeptVo> selectPageDeptList(SysDeptBo dept, PageQuery pageQuery);

    /**
     * 查询分组管理数据
     *
     * @param dept 分组信息
     * @return 分组信息集合
     */
    List<SysDeptVo> selectDeptList(SysDeptBo dept);

    /**
     * 查询分组树结构信息
     *
     * @param dept 分组信息
     * @return 分组树信息集合
     */
    List<Tree<Long>> selectDeptTreeList(SysDeptBo dept);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 分组列表
     * @return 下拉树结构列表
     */
    List<Tree<Long>> buildDeptTreeSelect(List<SysDeptVo> depts);

    /**
     * 根据角色ID查询分组树信息
     *
     * @param roleId 角色ID
     * @return 选中分组列表
     */
    List<Long> selectDeptListByRoleId(Long roleId);

    /**
     * 根据分组ID查询信息
     *
     * @param deptId 分组ID
     * @return 分组信息
     */
    SysDeptVo selectDeptById(Long deptId);

    /**
     * 通过分组ID串查询分组
     *
     * @param deptIds 分组id串
     * @return 分组列表信息
     */
    List<SysDeptVo> selectDeptByIds(List<Long> deptIds);

    /**
     * 根据ID查询所有子分组数（正常状态）
     *
     * @param deptId 分组ID
     * @return 子分组数
     */
    long selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    boolean checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    boolean checkDeptNameUnique(SysDeptBo dept);

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    void checkDeptDataScope(Long deptId);

    /**
     * 新增保存部门信息
     *
     * @param bo 部门信息
     * @return 结果
     */
    int insertDept(SysDeptBo bo);

    /**
     * 修改保存部门信息
     *
     * @param bo 部门信息
     * @return 结果
     */
    int updateDept(SysDeptBo bo);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    int deleteDeptById(Long deptId);

    /**
     * 根据用户ID列表批量更新用户的部门ID
     *
     * @param deptId      部门ID
     * @param userIds     用户ID列表
     * @param needCompare 是否需要对比用户列表（true: 对比移除原部门不在列表中的用户，false: 仅添加列表中的用户到部门）
     * @return 更新的用户数量
     */
    int updateUserDeptByUserIds(Long deptId, List<Long> userIds, boolean needCompare);
}
