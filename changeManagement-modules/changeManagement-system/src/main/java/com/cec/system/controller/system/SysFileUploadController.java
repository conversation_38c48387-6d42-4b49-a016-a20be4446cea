//package com.cec.system.controller.system;
//
//import cn.dev33.satoken.annotation.SaCheckPermission;
//import cn.hutool.core.util.ObjectUtil;
//import com.cec.common.core.domain.R;
//import com.cec.common.core.validate.QueryGroup;
//import com.cec.common.log.annotation.Log;
//import com.cec.common.log.enums.BusinessType;
//import com.cec.common.mybatis.core.page.PageQuery;
//import com.cec.common.mybatis.core.page.TableDataInfo;
//import com.cec.common.web.core.BaseController;
//import com.cec.system.domain.bo.SysFileUploadBo;
//import com.cec.system.domain.vo.SysFileUploadInfoVo;
//import com.cec.system.domain.vo.SysFileUploadVo;
//import com.cec.system.service.ISysFileUploadService;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.constraints.NotEmpty;
//import lombok.RequiredArgsConstructor;
//import org.springframework.http.MediaType;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.IOException;
//import java.util.Arrays;
//import java.util.List;
//
///**
// * 文件上传下载控制层
// */
//@Validated
//@RequiredArgsConstructor
//@RestController
//@RequestMapping("/resource/file")
//public class SysFileUploadController extends BaseController {
//
//    private final ISysFileUploadService fileUploadService;
//
//    /**
//     * 查询文件上传下载列表
//     */
//    @SaCheckPermission("system:file:list")
//    @GetMapping("/list")
//    public TableDataInfo<SysFileUploadVo> list(@Validated(QueryGroup.class) SysFileUploadBo bo, PageQuery pageQuery) {
//        return fileUploadService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 根据ID查询文件列表
//     */
//    @SaCheckPermission("system:file:query")
//    @GetMapping("/listByIds/{fileIds}")
//    public R<List<SysFileUploadVo>> listByIds(@NotEmpty(message = "主键不能为空")
//                                              @PathVariable Long[] fileIds) {
//        List<SysFileUploadVo> list = fileUploadService.queryByIds(Arrays.asList(fileIds));
//        return R.ok(list);
//    }
//
//    /**
//     * 上传文件
//     */
//    @SaCheckPermission("system:file:upload")
//    @Log(title = "文件上传", businessType = BusinessType.INSERT)
//    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    public R<SysFileUploadInfoVo> upload(@RequestPart("file") MultipartFile file) {
//        if (ObjectUtil.isNull(file)) {
//            return R.fail("上传文件不能为空");
//        }
//        SysFileUploadInfoVo uploadVo = fileUploadService.upload(file);
//        return R.ok(uploadVo);
//    }
//
//    /**
//     * 下载文件
//     */
//    @SaCheckPermission("system:file:download")
//    @GetMapping("/download/{fileId}")
//    public void download(@PathVariable Long fileId, HttpServletResponse response) throws IOException {
//        fileUploadService.download(fileId, response);
//    }
//
//    /**
//     * 删除文件
//     */
//    @SaCheckPermission("system:file:remove")
//    @Log(title = "文件删除", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{fileIds}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//                          @PathVariable Long[] fileIds) {
//        return toAjax(fileUploadService.deleteWithValidByIds(List.of(fileIds), true));
//    }
//}
