package com.cec.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cec.common.core.config.CecSmartFlowConfig;
import com.cec.common.core.constant.SystemConstants;
import com.cec.common.core.domain.R;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.core.utils.StreamUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.core.validate.QueryGroup;
import com.cec.common.encrypt.annotation.ApiEncrypt;
import com.cec.common.excel.core.ExcelResult;
import com.cec.common.excel.utils.ExcelUtil;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.log.enums.LogTitleEnum;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.common.tenant.helper.TenantHelper;
import com.cec.common.web.core.BaseController;
import com.cec.system.domain.bo.SysDeptBo;
import com.cec.system.domain.bo.SysPostBo;
import com.cec.system.domain.bo.SysRoleBo;
import com.cec.system.domain.bo.SysUserBo;
import com.cec.system.domain.event.SysUserAllEvent;
import com.cec.system.domain.vo.SysRoleVo;
import com.cec.system.domain.vo.SysUserExportVo;
import com.cec.system.domain.vo.SysUserImportVo;
import com.cec.system.domain.vo.SysUserInfoVo;
import com.cec.system.domain.vo.SysUserVo;
import com.cec.system.domain.vo.UserInfoVo;
import com.cec.system.listener.SysUserImportListener;
import com.cec.system.service.ISysDeptService;
import com.cec.system.service.ISysPostService;
import com.cec.system.service.ISysRoleService;
import com.cec.system.service.ISysUserService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR> Li
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {

    private final ISysUserService userService;
    private final ISysRoleService roleService;
    private final ISysPostService postService;
    private final ISysDeptService deptService;
    private final CecSmartFlowConfig cecSmartFlowConfig;

    /**
     * 密码校验正则，包含大小写字母、数字、特殊符号，长度8-20
     */
    private static final String PASSWORD_PATTERN = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[`~!@#$%^&*()\\-=_+\\[\\]{}|;:'\",.<>/?])[A-Za-z\\d`~!@#$%^&*()\\-=_+\\[\\]{}|;:'\",.<>/?]{8,20}$";

    /**
     * staffId校验正则，必须是字母开头，如果有数字必须在字母后面
     */
    private static final String STAFFID_PATTERN = "^[a-zA-Z]+[a-zA-Z0-9]*$";

    /**
     * 获取用户列表
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/list")
    public TableDataInfo<SysUserVo> list(@Validated(QueryGroup.class) SysUserBo user, PageQuery pageQuery) {
        return userService.selectPageUserList(user, pageQuery);
    }

    /**
     * 导出用户列表
     */
    @Log(title = LogTitleEnum.USER_MANAGEMENT, businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:user:export")
    @PostMapping("/export")
    public void export(SysUserBo user, HttpServletResponse response) {
        List<SysUserExportVo> list = userService.selectUserExportList(user);
        ExcelUtil.exportExcel(list, "用户数据", SysUserExportVo.class, response);
    }

    /**
     * 同步全量用户到CM（非增量）
     */
    // @Log(title = LogTitleEnum.USER_MANAGEMENT, businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:user:edit")
    @PostMapping("/sync")
    public R<Void> sync() {
        Boolean status = cecSmartFlowConfig.getSyncSmartFlowAll();
        log.info("同步全量用户到CM（非增量）: {}", status);
        if (status) {
            List<SysUserVo> sysUserVos = userService.selectUserList(new SysUserBo());
            if (CollUtil.isNotEmpty(sysUserVos)){
                SpringUtils.context().publishEvent(SysUserAllEvent.builder().users(sysUserVos).build());
            }
        }
        return R.ok();
    }

    /**
     * 导入数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(title = LogTitleEnum.USER_MANAGEMENT, businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:user:edit")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        // 导入基本数据时，isRelationshipPhase设为false，不处理关系
        ExcelResult<SysUserImportVo> result = ExcelUtil.importExcel(file.getInputStream(), SysUserImportVo.class, new SysUserImportListener(Boolean.FALSE, Boolean.TRUE));
        return R.ok(result.getAnalysis());
    }

    /**
     * 导入用户关系数据
     *
     * @param file 导入文件
     */
    @Log(title = LogTitleEnum.USER_MANAGEMENT, businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:user:edit")
    @PostMapping(value = "/importRelationships", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importRelationships(@RequestPart("file") MultipartFile file) throws Exception {
        ExcelResult<SysUserImportVo> result = ExcelUtil.importExcel(file.getInputStream(), SysUserImportVo.class, new SysUserImportListener(false, true));
        return R.ok(result.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    @SaCheckPermission("system:user:edit")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "用户数据", SysUserImportVo.class, response);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public R<UserInfoVo> getInfo() {
        UserInfoVo userInfoVo = new UserInfoVo();
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (TenantHelper.isEnable() && LoginHelper.isSuperAdmin()) {
            // 超级管理员 如果重新加载用户信息需清除动态租户
            TenantHelper.clearDynamic();
        }
        SysUserVo user = userService.selectUserById(loginUser.getUserId());
        if (ObjectUtil.isNull(user)) {
            return R.fail("没有权限访问用户数据!");
        }
        userInfoVo.setUser(user);
        userInfoVo.setPermissions(loginUser.getMenuPermission());
        userInfoVo.setRoles(loginUser.getRolePermission());
        return R.ok(userInfoVo);
    }

    /**
     * 根据用户编号获取详细信息
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:user:detail")
    @GetMapping(value = {"/", "/{userId}"})
    public R<SysUserInfoVo> getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        SysUserInfoVo userInfoVo = new SysUserInfoVo();
        if (ObjectUtil.isNotNull(userId)) {
            userService.checkUserDataScope(userId);
            SysUserVo sysUser = userService.selectUserById(userId);
            userInfoVo.setUser(sysUser);
            userInfoVo.setRoleIds(roleService.selectRoleListByUserId(userId));
            Long deptId = sysUser.getDeptId();
            if (ObjectUtil.isNotNull(deptId)) {
                SysPostBo postBo = new SysPostBo();
                postBo.setDeptId(deptId);
                userInfoVo.setPosts(postService.selectPostList(postBo));
                userInfoVo.setPostIds(postService.selectPostListByUserId(userId));
            }
        }
        SysRoleBo roleBo = new SysRoleBo();
        roleBo.setStatus(SystemConstants.NORMAL);
        List<SysRoleVo> roles = roleService.selectRoleList(roleBo);
        userInfoVo.setRoles(LoginHelper.isSuperAdmin(userId) ? roles : StreamUtils.filter(roles, r -> !r.isSuperAdmin()));
        return R.ok(userInfoVo);
    }

    /**
     * 新增用户
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = LogTitleEnum.USER_MANAGEMENT, businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysUserBo user) {
        if (!userService.checkUserNameUnique(user)) {
            return R.fail(MessageUtils.message("user.add.fail.username.exist", user.getStaffId()));
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return R.fail(MessageUtils.message("user.add.fail.email.exist", user.getStaffId()));
        }

        // 校验staffId规则
        String staffId = user.getStaffId();
        if (StringUtils.isNotEmpty(staffId) && !staffId.matches(STAFFID_PATTERN)) {
            throw new ServiceException(MessageUtils.message("user.staffid.rule.invalid"));
        }

        // 校验密码规则
        String password = user.getPassword();
        if (!password.matches(PASSWORD_PATTERN)) {
            throw new ServiceException(MessageUtils.message("user.password.rule.invalid"));
        }
        user.setPassword(BCrypt.hashpw(user.getPassword()));
        return toAjax(userService.insertUser(user, 0));
    }

    /**
     * 修改用户
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = LogTitleEnum.USER_MANAGEMENT, businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysUserBo user) {
        userService.checkUserAllowed(user.getUserId());
        userService.checkUserDataScope(user.getUserId());
        if (!userService.checkUserNameUnique(user)) {
            return R.fail(MessageUtils.message("user.edit.fail.username.exist", user.getStaffId()));
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return R.fail(MessageUtils.message("user.edit.fail.email.exist", user.getStaffId()));
        }

        // 校验staffId规则
        String staffId = user.getStaffId();
        if (StringUtils.isNotEmpty(staffId) && !staffId.matches(STAFFID_PATTERN)) {
            throw new ServiceException(MessageUtils.message("user.staffid.rule.invalid"));
        }

        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     *
     * @param userIds 角色ID串
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = LogTitleEnum.USER_MANAGEMENT, businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public R<Void> remove(@PathVariable Long[] userIds) {
        if (ArrayUtil.contains(userIds, LoginHelper.getUserId())) {
            return R.fail("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 根据用户ID串批量获取用户基础信息
     *
     * @param userIds 用户ID串
     * @param deptId  部门ID
     */
    @SaCheckPermission("system:user:query")
    @GetMapping("/optionselect")
    public R<List<SysUserVo>> optionselect(@RequestParam(required = false) Long[] userIds,
                                           @RequestParam(required = false) Long deptId) {
        return R.ok(userService.selectUserByIds(ArrayUtil.isEmpty(userIds) ? null : List.of(userIds), deptId));
    }

    /**
     * 重置密码
     */
    @ApiEncrypt
    @SaCheckPermission("system:user:edit")
    @Log(title = LogTitleEnum.USER_MANAGEMENT, businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public R<Void> resetPwd(@RequestBody SysUserBo user) {
        userService.checkUserAllowed(user.getUserId());
        userService.checkUserDataScope(user.getUserId());
        // 校验密码规则
        String password = user.getPassword();
        if (!password.matches(PASSWORD_PATTERN)) {
            throw new ServiceException(MessageUtils.message("user.password.rule.invalid"));
        }
        user.setPassword(BCrypt.hashpw(user.getPassword()));
        return toAjax(userService.resetUserPwd(user.getUserId(), user.getPassword()));
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = LogTitleEnum.USER_MANAGEMENT, businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysUserBo user) {
        userService.checkUserAllowed(user.getUserId());
        userService.checkUserDataScope(user.getUserId());
        return toAjax(userService.updateUserStatus(user.getUserId(), user.getStatus()));
    }

    /**
     * 根据用户编号获取授权角色
     *
     * @param userId 用户ID
     */
    @SaCheckPermission("system:user:query")
    @GetMapping("/authRole/{userId}")
    public R<SysUserInfoVo> authRole(@PathVariable Long userId) {
        userService.checkUserDataScope(userId);
        SysUserVo user = userService.selectUserById(userId);
        List<SysRoleVo> roles = roleService.selectRolesAuthByUserId(userId);
        SysUserInfoVo userInfoVo = new SysUserInfoVo();
        userInfoVo.setUser(user);
        userInfoVo.setRoles(LoginHelper.isSuperAdmin(userId) ? roles : StreamUtils.filter(roles, r -> !r.isSuperAdmin()));
        return R.ok(userInfoVo);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户Id
     * @param roleIds 角色ID串
     */
    @SaCheckPermission("system:user:edit")
    @Log(title = LogTitleEnum.USER_MANAGEMENT, businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public R<Void> insertAuthRole(Long userId, Long[] roleIds) {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return R.ok();
    }

    /**
     * 获取部门树列表
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/deptTree")
    public R<List<Tree<Long>>> deptTree(SysDeptBo dept) {
        return R.ok(deptService.selectDeptTreeList(dept));
    }

    /**
     * 获取部门下的所有用户信息
     */
    @SaCheckPermission("system:user:list")
    @GetMapping("/list/dept/{deptId}")
    public R<List<SysUserVo>> listByDept(@PathVariable @NotNull Long deptId) {
        return R.ok(userService.selectUserListByDept(deptId));
    }

}
