package com.cec.system.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.common.core.constant.SystemConstants;
import com.cec.common.tenant.core.TenantEntity;
import com.cec.system.domain.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_user", autoResultMap = true)
public class SysUser extends TenantEntity {

    /**
     * 用户ID
     */
    @TableId(value = "user_id")
    private Long userId;

    /**
     * StaffId
     */
    private String staffId;

    /**
     * StaffName
     */
    private String staffName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户类型（sys_user系统用户）
     */
    private String userType;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户性别
     */
    private String sex;

    /**
     * 用户头像
     */
    private Long avatar;

    /**
     * 密码
     */
    @TableField(
        insertStrategy = FieldStrategy.NOT_EMPTY,
        updateStrategy = FieldStrategy.NOT_EMPTY,
        whereStrategy = FieldStrategy.NOT_EMPTY
    )
    private String password;

    /**
     * 帐号状态（1正常 0停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 对应Team leader
     */
    @TableField(value = "team_leader", updateStrategy = FieldStrategy.ALWAYS, typeHandler = JacksonTypeHandler.class)
    private UserVo teamLeader;

    /**
     * 对应Team Manager
     */
    @TableField(value = "team_manager", updateStrategy = FieldStrategy.ALWAYS, typeHandler = JacksonTypeHandler.class)
    private UserVo teamManager;

    /**
     * 直接领导
     */
    @TableField(value = "direct_leader", updateStrategy = FieldStrategy.ALWAYS, typeHandler = JacksonTypeHandler.class)
    private UserVo directLeader;

    /**
     * 创建方式（0-新增 1-excel导入）
     */
    private Integer createType;

    /**
     * 同步SmartFlow状态 1-同步成功
     */
    private Integer syncStatus;

    @TableField(exist = false)
    private UserVo userVo;


    public UserVo getUserVo() {
        return new UserVo(this.userId, this.staffId, this.staffName);
    }

    public SysUser(Long userId) {
        this.userId = userId;
    }

    public boolean isSuperAdmin() {
        return SystemConstants.SUPER_ADMIN_ID.equals(this.userId);
    }

}
