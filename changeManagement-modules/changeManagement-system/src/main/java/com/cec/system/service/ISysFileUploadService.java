package com.cec.system.service;

import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.system.domain.bo.SysFileUploadBo;
import com.cec.system.domain.vo.SysFileUploadInfoVo;
import com.cec.system.domain.vo.SysFileUploadVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

/**
 * 文件上传下载服务层
 */
public interface ISysFileUploadService {

    /**
     * 查询文件上传下载列表
     *
     * @param fileUpload 文件上传下载分页查询对象
     * @param pageQuery 分页查询实体类
     * @return 结果
     */
    TableDataInfo<SysFileUploadVo> queryPageList(SysFileUploadBo fileUpload, PageQuery pageQuery);

    /**
     * 根据一组文件ID获取对应的文件信息列表
     *
     * @param fileIds 一组文件ID集合
     * @return 包含SysFileUploadVo对象的列表
     */
    List<SysFileUploadVo> queryByIds(Collection<Long> fileIds);

    /**
     * 根据文件ID获取文件详细信息
     *
     * @param fileId 文件ID
     * @return 文件信息对象
     */
    SysFileUploadVo getById(Long fileId);

    /**
     * 上传文件
     *
     * @param file 要上传的MultipartFile对象
     * @return 上传后的文件信息
     */
    SysFileUploadInfoVo upload(MultipartFile file);

    /**
     * 上传文件
     *
     * @param file 要上传的File对象
     * @return 上传后的文件信息
     */
    SysFileUploadInfoVo upload(File file);

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     * @param response HttpServletResponse对象
     * @throws IOException IO异常
     */
    void download(Long fileId, HttpServletResponse response) throws IOException;

    /**
     * 删除文件
     *
     * @param ids 文件ID集合
     * @param isValid 是否需要验证
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

} 