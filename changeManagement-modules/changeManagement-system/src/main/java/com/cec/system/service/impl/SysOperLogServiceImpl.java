package com.cec.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.core.utils.ObjectUtils;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.core.utils.ip.AddressUtils;
import com.cec.common.log.event.OperLogEvent;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.system.domain.SysOperLog;
import com.cec.system.domain.SysUser;
import com.cec.system.domain.bo.SysOperLogBo;
import com.cec.system.domain.bo.SysOperLogBo2;
import com.cec.system.domain.vo.SysOperLogVo;
import com.cec.system.mapper.SysOperLogMapper;
import com.cec.system.mapper.SysUserMapper;
import com.cec.system.service.ISysOperLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysOperLogServiceImpl implements ISysOperLogService {

    private final SysOperLogMapper baseMapper;

    /**
     * 操作日志记录
     *
     * @param operLogEvent 操作日志事件
     */
    @Async
    @EventListener
    public void recordOper(OperLogEvent operLogEvent) {
        SysOperLogBo operLog = BeanUtil.copyProperties(operLogEvent, SysOperLogBo.class,"operName");

        SysUser sysUser = SpringUtils.getBean(SysUserMapper.class).selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getStaffId, operLogEvent.getOperName()));
        operLog.setOperName(sysUser.getUserVo());
        // 远程查询操作地点
        operLog.setOperLocation(AddressUtils.getRealAddressByIP(operLog.getOperIp()));
        insertOperlog(operLog);
    }

    @Override
    public TableDataInfo<SysOperLogVo> selectPageOperLogList(SysOperLogBo2 operLog, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOperLog> lqw = buildQueryWrapper(operLog);
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            lqw.orderByDesc(SysOperLog::getOperId);
        }
        Page<SysOperLogVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    private LambdaQueryWrapper<SysOperLog> buildQueryWrapper(SysOperLogBo2 operLog) {
        return new LambdaQueryWrapper<SysOperLog>()
            .like(StringUtils.isNotBlank(operLog.getTitle()), SysOperLog::getTitle, operLog.getTitle())
            .in(CollUtil.isNotEmpty(operLog.getTitleList()), SysOperLog::getTitle, operLog.getTitleList())
            .eq(operLog.getBusinessType() != null,
                SysOperLog::getBusinessType, operLog.getBusinessType())
            .eq(operLog.getStatus() != null,
                SysOperLog::getStatus, operLog.getStatus())
            // .like(StringUtils.isNotBlank(operLog.getOperName()), SysOperLog::getOperName, operLog.getOperName())
            .apply(ObjectUtils.isNotNull(operLog.getUserId()),"JSON_EXTRACT(oper_name, '$.userId') = {0} ", operLog.getUserId())
            .gt(StringUtils.isNotBlank(operLog.getOperTimeStart()), SysOperLog::getOperTime, operLog.getOperTimeStart())
            .lt(StringUtils.isNotBlank(operLog.getOperTimeEnd()), SysOperLog::getOperTime, operLog.getOperTimeEnd());
    }

    /**
     * 新增操作日志
     *
     * @param bo 操作日志对象
     */
    @Override
    public void insertOperlog(SysOperLogBo bo) {
        SysOperLog operLog = MapstructUtils.convert(bo, SysOperLog.class);
        operLog.setOperTime(new Date());
        baseMapper.insert(operLog);
    }

    /**
     * 查询系统操作日志集合
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    @Override
    public List<SysOperLogVo> selectOperLogList(SysOperLogBo operLog) {
        LambdaQueryWrapper<SysOperLog> lqw = buildQueryWrapper(operLog);
        return baseMapper.selectVoList(lqw.orderByDesc(SysOperLog::getOperId));
    }

    private LambdaQueryWrapper<SysOperLog> buildQueryWrapper(SysOperLogBo operLog) {
        return null;
    }

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    @Override
    public int deleteOperLogByIds(Long[] operIds) {
        return baseMapper.deleteByIds(Arrays.asList(operIds));
    }

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    @Override
    public SysOperLogVo selectOperLogById(Long operId) {
        return baseMapper.selectVoById(operId);
    }

    /**
     * 清空操作日志
     */
    @Override
    public void cleanOperLog() {
        baseMapper.delete(new LambdaQueryWrapper<>());
    }
}
