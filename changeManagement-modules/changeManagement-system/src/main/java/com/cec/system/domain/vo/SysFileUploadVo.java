package com.cec.system.domain.vo;

import com.cec.common.translation.annotation.Translation;
import com.cec.common.translation.constant.TransConstant;
import com.cec.system.domain.SysFileUpload;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 文件上传下载视图对象 sys_file_upload
 */
@Data
@AutoMapper(target = SysFileUpload.class)
public class SysFileUploadVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 文件后缀名
     */
    private String fileSuffix;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 存储路径
     */
    private String storagePath;

    /**
     * 子目录日期(格式：yyyy-MM-dd)
     */
    private String directoryDate;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建者名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
} 