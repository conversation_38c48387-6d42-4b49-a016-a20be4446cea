package com.cec.system.domain.bo;

import lombok.Data;

import java.util.List;

/**
 * 操作日志记录业务对象 sys_oper_log
 *
 * <AUTHOR>
 * @date 2023-02-07
 */

@Data
public class SysOperLogBo2 {

    /**
     * 管理日志
     * APPLICATION_MANAGEMENT-模块管理
     * CONFIG_MANAGEMENT-配置管理
     * TEAM_MANAGEMENT-分组管理
     * FILE_STORAGE-文件存储
     * ROLE_MANAGEMENT-角色管理
     * USER_MANAGEMENT-用户管理
     * LOGIN_LOG-登录日志
     * STATISTICS-统计日志
     * <br/>
     * 用户日志
     * CHANGE_MANAGEMENT-变更管理
     * CHANGE_APPROVAL-变更审批
     * CHANGE_CANCEL-变更取消
     * NETWORK_FREEZE_MANAGEMENT-封网管理
     * NETWORK_FREEZE_APPROVAL-封网审批
     * CHANGE_DELAY-延期变更
     */
    private String title;


    private List<String> titleList;

    /**
     * 业务类型（0其它 1新增 2修改 3删除 4授权 5导出 6导入）
     */
    private Integer businessType;

    /**
     * 操作人员
     */
    private Long userId;

    /**
     * 操作状态（0正常 1异常）
     */
    private Integer status;

    /**
     * 操作时间起
     */
    private String operTimeStart;

    /**
     * 操作时间止
     */
    private String operTimeEnd;

}
