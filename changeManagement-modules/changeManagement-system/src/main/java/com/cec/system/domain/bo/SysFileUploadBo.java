package com.cec.system.domain.bo;

import com.cec.common.mybatis.core.domain.BaseEntity;
import com.cec.system.domain.SysFileUpload;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件上传下载业务对象 sys_file_upload
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysFileUpload.class, reverseConvertGenerate = false)
public class SysFileUploadBo extends BaseEntity {

    /**
     * 文件主键ID
     */
    private Long fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 文件后缀名
     */
    private String fileSuffix;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 存储路径
     */
    private String storagePath;

    /**
     * 子目录日期(格式：yyyy-MM-dd)
     */
    private String directoryDate;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 备注
     */
    private String remark;
} 