package com.cec.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cec.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件上传下载对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_file_upload")
public class SysFileUpload extends TenantEntity {

    /**
     * 文件主键ID
     */
    @TableId(value = "file_id")
    private Long fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 文件后缀名
     */
    private String fileSuffix;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 存储路径
     */
    private String storagePath;

    /**
     * 子目录日期(格式：yyyy-MM-dd)
     */
    private String directoryDate;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 备注
     */
    private String remark;
} 