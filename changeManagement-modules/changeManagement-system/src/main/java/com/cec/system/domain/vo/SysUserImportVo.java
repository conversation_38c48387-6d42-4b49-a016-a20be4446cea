package com.cec.system.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户对象导入VO
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
// @Accessors(chain = true) // 导入不允许使用 会找不到set方法
public class SysUserImportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * StaffId
     */
    @ExcelProperty(index = 0,value = "StaffId")
    @ColumnWidth(20)
    @HeadFontStyle(fontHeightInPoints = 12) // 表头字体大小
    @ContentFontStyle(fontHeightInPoints = 12) // 内容字体大小
    private String staffId;

    /**
     * StaffName
     */
    @ExcelProperty(index = 1,value = "StaffName")
    @ColumnWidth(20)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    private String staffName;

    /**
     * 用户邮箱
     */
    @ExcelProperty(index = 2,value = "Email")
    @ColumnWidth(40)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    private String email;

    /**
     * 帐号状态（0正常 1停用）
     */
    @ExcelProperty(value = "IsActive(0-disable 1-enable)", index = 3)
    @ColumnWidth(40)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    private String status;

    /**
     * leaderStaffId
     */
    @ExcelProperty(value = "Leader Staff Id", index = 4)
    @ColumnWidth(40)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    private String leaderStaffId;

    /**
     * managerStaffId
     */
    @ExcelProperty(value = "Manager Staff Id", index = 5)
    @ColumnWidth(40)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    private String managerStaffId;

    /**
     * Direct Leader Staff Id（smartFlow需要这个参数）
     */
    @ExcelProperty(value = "Direct Leader Staff Id", index = 6)
    @ColumnWidth(40)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    private String directLeaderStaffId;

}
