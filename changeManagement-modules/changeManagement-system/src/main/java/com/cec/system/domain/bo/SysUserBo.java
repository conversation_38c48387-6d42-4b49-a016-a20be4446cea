package com.cec.system.domain.bo;

import com.cec.common.core.constant.SystemConstants;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.core.validate.ImportGroup;
import com.cec.common.core.xss.Xss;
import com.cec.system.domain.SysUser;
import com.cec.system.domain.vo.UserVo;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户信息业务对象 sys_user
 *
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
@AutoMapper(target = SysUser.class, reverseConvertGenerate = false)
public class SysUserBo {

    /**
     * 用户ID
     */
    @NotNull(message = "{user.id.not.null}", groups = {EditGroup.class})
    private Long userId;

    /**
     * 账号ID
     */
    @NotNull(message = "{user.staffid.not.null}", groups = {AddGroup.class, EditGroup.class, ImportGroup.class})
    @Size(min = 0, max = 20, message = "{user.staffid.length.valid}", groups = {AddGroup.class, EditGroup.class, ImportGroup.class})
    private String staffId;

    /**
     * 账号名称
     */
    @Xss(message = "{user.username.xss.invalid}", groups = {AddGroup.class, EditGroup.class, ImportGroup.class})
    @NotBlank(message = "{user.username.not.blank}", groups = {AddGroup.class, EditGroup.class, ImportGroup.class})
    @Size(min = 0, max = 50, message = "{user.username.length.valid}", groups = {AddGroup.class, EditGroup.class, ImportGroup.class})
    private String staffName;

    /**
     * 用户类型（sys_user系统用户）
     */
    private String userType;

    /**
     * 用户邮箱
     */
    @Email(message = "{user.email.not.valid}", groups = {AddGroup.class, EditGroup.class, ImportGroup.class})
    @Size(min = 0, max = 50, message = "{length.not.valid}", groups = {AddGroup.class, EditGroup.class, ImportGroup.class})
    private String email;

    /**
     * 密码
     */
    @NotBlank(message = "{user.password.not.blank}", groups = {AddGroup.class})
    private String password;

    /**
     * 帐号状态（1正常 0停用）
     */
    @Pattern(regexp = "^[01]$", message = "状态只能是0或1", groups = {AddGroup.class, EditGroup.class, ImportGroup.class})
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 角色组(新增修改使用/查询多选)
     */
    private Long[] roleIds;

    /**
     * 部门ID(查询多选)
     */
    private Long[] deptIds;

    /**
     * 角色ID（查询使用）
     */
    private Long roleId;

    /**
     * 部门ID
     */
//    @NotNull(message = "部门不能为空", groups = {AddGroup.class, EditGroup.class})
    private String deptId;

    /**
     * 对应Team leader
     */
    private UserVo teamLeader;

    /**
     * 对应Team Manager
     */
    private UserVo teamManager;

    /**
     * 直接领导
     */
    private UserVo directLeader;

    /**
     * 排除不查询的用户(工作流用)
     */
    private String excludeUserIds;

    private Long createBy;

    private Long updateBy;

    public SysUserBo(Long userId) {
        this.userId = userId;
    }

    public boolean isSuperAdmin() {
        return SystemConstants.SUPER_ADMIN_ID.equals(this.userId);
    }

}
