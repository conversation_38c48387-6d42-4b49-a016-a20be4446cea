package com.cec.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.ServletUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.system.domain.SysFileUpload;
import com.cec.system.domain.bo.SysFileUploadBo;
import com.cec.system.domain.vo.SysFileUploadInfoVo;
import com.cec.system.domain.vo.SysFileUploadVo;
import com.cec.system.mapper.SysFileUploadMapper;
import com.cec.system.service.ISysFileUploadService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 文件上传下载服务实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysFileUploadServiceImpl extends ServiceImpl<SysFileUploadMapper, SysFileUpload> implements ISysFileUploadService {

    @Value("${file.path}")
    private String uploadPath;

    @Value("${file.prefix}")
    private String filePrefix;

    /**
     * 允许上传的文件类型
     */
    private static final String[] ALLOWED_EXTENSIONS = {
        // 图片
        "bmp", "gif", "jpg", "jpeg", "png",
        // 文档
        "doc", "docx", "xls", "xlsx", "ppt", "pptx", "html", "htm", "txt", "pdf",
        // 压缩文件
        "zip", "rar", "gz", "7z",
        // 音视频
        "mp3", "mp4", "avi"
    };

    @Override
    public TableDataInfo<SysFileUploadVo> queryPageList(SysFileUploadBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysFileUpload> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoPage(pageQuery.build(), lqw);
    }

    private LambdaQueryWrapper<SysFileUpload> buildQueryWrapper(SysFileUploadBo bo) {
        LambdaQueryWrapper<SysFileUpload> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), SysFileUpload::getFileName, bo.getFileName());
        lqw.like(StringUtils.isNotBlank(bo.getOriginalName()), SysFileUpload::getOriginalName, bo.getOriginalName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileSuffix()), SysFileUpload::getFileSuffix, bo.getFileSuffix());
        lqw.eq(StringUtils.isNotBlank(bo.getContentType()), SysFileUpload::getContentType, bo.getContentType());
        lqw.orderByDesc(SysFileUpload::getCreateTime);
        return lqw;
    }

    @Override
    public List<SysFileUploadVo> queryByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return CollUtil.newArrayList();
        }
        return baseMapper.selectVoList(
            Wrappers.lambdaQuery(SysFileUpload.class).in(SysFileUpload::getFileId, ids));
    }

    @Override
    public SysFileUploadVo getById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysFileUploadInfoVo upload(MultipartFile file) {
        try {
            return doUpload(file.getOriginalFilename(), file.getContentType(), file.getSize(), file.getInputStream());
        } catch (IOException e) {
            throw new ServiceException("文件上传失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysFileUploadInfoVo upload(File file) {
        try {
            String contentType = MediaType.APPLICATION_OCTET_STREAM_VALUE;
            return doUpload(file.getName(), contentType, file.length(), new FileInputStream(file));
        } catch (IOException e) {
            throw new ServiceException("文件上传失败");
        }
    }

    /**
     * 上传文件
     */
    private SysFileUploadInfoVo doUpload(String originalName, String contentType, long size, InputStream inputStream) {
        // 获取文件后缀
        String fileSuffix = StrUtil.DOT + FileNameUtil.extName(originalName);
        if (StrUtil.isEmpty(fileSuffix) || fileSuffix.equals(StrUtil.DOT)) {
            throw new ServiceException("文件后缀不能为空");
        }
        // 判断大小
        if (size > 500 * 1024 * 1024) {
            throw new ServiceException("文件不能超过500MB");
        }
        // 判断文件类型
        if (!StrUtil.containsAnyIgnoreCase(contentType, MediaType.APPLICATION_OCTET_STREAM_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE)
            && !isAllowedExtension(FileNameUtil.extName(originalName))) {
            throw new ServiceException("文件类型不支持上传");
        }

        // 保存到本地
        String fileName = IdUtil.fastSimpleUUID() + fileSuffix;
        String directoryDate = LocalDate.now().toString();
        String visiblePath = StrUtil.format("{}/{}/{}", filePrefix, directoryDate, fileName);
        String storagePath = StrUtil.format("{}/{}/{}", uploadPath, directoryDate, fileName);

        try {
            // 创建目录
            String dirPath = Paths.get(storagePath).getParent().toString();
            FileUtil.mkdir(dirPath);

            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(storagePath)) {
                byte[] buffer = new byte[1024];
                int len;
                while ((len = inputStream.read(buffer)) > 0) {
                    fos.write(buffer, 0, len);
                }
            }

            SysFileUpload entity = new SysFileUpload();
            entity.setFileId(IdUtil.getSnowflakeNextId());
            entity.setFileName(fileName);
            entity.setOriginalName(originalName);
            entity.setFileSuffix(FileNameUtil.extName(originalName));
            entity.setFileSize(size);
            entity.setFilePath(visiblePath);
            entity.setStoragePath(storagePath);
            entity.setDirectoryDate(directoryDate);
            entity.setContentType(contentType);
            entity.setDownloadCount(0);
            baseMapper.insert(entity);

            SysFileUploadInfoVo uploadVo = new SysFileUploadInfoVo();
            uploadVo.setFileId(entity.getFileId().toString());
            uploadVo.setFileName(entity.getOriginalName());
            uploadVo.setFilePath(entity.getFilePath());
            uploadVo.setFileSize(entity.getFileSize());

            return uploadVo;
        } catch (Exception e) {
            log.error("保存文件异常", e);
            throw new ServiceException("保存文件异常");
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("关闭输入流异常", e);
            }
        }
    }

    /**
     * 检查文件扩展名是否允许上传
     *
     * @param extension 扩展名
     * @return true允许，false禁止
     */
    private boolean isAllowedExtension(String extension) {
        for (String allowedExtension : ALLOWED_EXTENSIONS) {
            if (allowedExtension.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void download(Long fileId, HttpServletResponse response) throws IOException {
        SysFileUpload fileUpload = baseMapper.selectById(fileId);
        if (ObjectUtil.isNull(fileUpload)) {
            throw new ServiceException("文件不存在");
        }
        String storagePath = fileUpload.getStoragePath();
        // 文件不存在
        if (!FileUtil.exist(storagePath)) {
            throw new ServiceException("文件不存在");
        }

        // 更新下载次数
        fileUpload.setDownloadCount(fileUpload.getDownloadCount() + 1);
        baseMapper.updateById(fileUpload);

        response.setContentType(fileUpload.getContentType());
        response.setHeader("Content-Disposition", "attachment;filename=" + ServletUtils.urlEncode(fileUpload.getOriginalName()));

        // 设置响应头
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Length", String.valueOf(fileUpload.getFileSize()));
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

        // 写入响应
        try (InputStream in = new FileInputStream(storagePath);
             OutputStream out = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
            out.flush();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些校验
        }

        AtomicReference<Boolean> result = new AtomicReference<>(true);
        List<SysFileUpload> fileUploads = baseMapper.selectBatchIds(ids);
        fileUploads.forEach(fileUpload -> {
            String storagePath = fileUpload.getStoragePath();
            if (FileUtil.exist(storagePath)) {
                try {
                    FileUtil.del(storagePath);
                } catch (Exception e) {
                    result.set(false);
                    log.error("删除文件失败", e);
                }
            }
        });

        if (result.get()) {
            return baseMapper.deleteBatchIds(ids) > 0;
        }
        return false;
    }
}
