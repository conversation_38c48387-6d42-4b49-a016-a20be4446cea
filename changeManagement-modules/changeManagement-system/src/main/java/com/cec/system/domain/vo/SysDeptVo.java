package com.cec.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cec.common.excel.annotation.ExcelDictFormat;
import com.cec.common.excel.convert.ExcelDictConvert;
import com.cec.system.domain.SysDept;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 分组视图对象 sys_dept
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
public class SysDeptVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分组id
     */
    @ExcelProperty(value = "分组id")
    private Long deptId;

    /**
     * 父分组id
     */
    private Long parentId;

    /**
     * 父分组名称
     */
    private String parentName;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 分组名称
     */
    @ExcelProperty(value = "分组名称")
    private String deptName;

    /**
     * 分组类别编码
     */
    @ExcelProperty(value = "分组类别编码")
    private String deptCategory;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 负责人ID
     */
    private Long leader;

    /**
     * 负责人
     */
    @ExcelProperty(value = "负责人")
    private String leaderName;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String phone;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
    private String email;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 部门状态（0正常 1停用）
     */
    @ExcelProperty(value = "部门状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 子部门
     */
    private List<SysDept> children = new ArrayList<>();

    /**
     * 部门成员
     */
    List<SysUserVo> userList;

    /**
     * 分组类型
     */
    private List<String> deptType;

    /**
     * 邮件签名
     */
    private String emailSign;

}
