package com.cec.system.controller.monitor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.LogTitleEnum;
import com.cec.common.web.core.BaseController;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.core.domain.R;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.excel.utils.ExcelUtil;
import com.cec.system.domain.bo.SysOperLogBo;
import com.cec.system.domain.bo.SysOperLogBo2;
import com.cec.system.domain.vo.SysOperLogVo;
import com.cec.system.service.ISysOperLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * 操作日志记录
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/monitor/operlog")
public class SysOperlogController extends BaseController {

    private final ISysOperLogService operLogService;

    private final static List<String> MGT_LOG =
        List.of(LogTitleEnum.CHANGE_MANAGEMENT.getCode(), LogTitleEnum.CHANGE_APPROVAL.getCode(),
            LogTitleEnum.CHANGE_CANCEL.getCode(), LogTitleEnum.CHANGE_DELAY.getCode(),
            LogTitleEnum.NETWORK_FREEZE_APPROVAL.getCode(), LogTitleEnum.NETWORK_FREEZE_MANAGEMENT.getCode());

    private final static List<String> OPER_LOG =
        List.of(LogTitleEnum.APPLICATION_MANAGEMENT.getCode(), LogTitleEnum.CONFIG_MANAGEMENT.getCode(),
            LogTitleEnum.STATISTICS.getCode(), LogTitleEnum.TEAM_MANAGEMENT.getCode(),
            LogTitleEnum.FILE_STORAGE.getCode(), LogTitleEnum.ROLE_MANAGEMENT.getCode(),
            LogTitleEnum.USER_MANAGEMENT.getCode(), LogTitleEnum.LOGIN_LOG.getCode(), LogTitleEnum.OTHER.getCode()
        );

    /**
     * 获取用户日志记录列表
     */
    @SaCheckPermission("monitor:modifyLog:list")
    @GetMapping("/opt/list")
    public TableDataInfo<SysOperLogVo> list(SysOperLogBo2 operLog, PageQuery pageQuery) {
        if (StringUtils.isBlank(operLog.getTitle())) {
            operLog.setTitleList(MGT_LOG);
        } else if (!MGT_LOG.contains(operLog.getTitle())) {
            return TableDataInfo.build();
        }
        return operLogService.selectPageOperLogList(operLog, pageQuery);
    }

    /**
     * 获取管理日志列表
     */
    @SaCheckPermission("monitor:operLog:list")
    @GetMapping("/mgt/list")
    public TableDataInfo<SysOperLogVo> list2(SysOperLogBo2 operLog, PageQuery pageQuery) {
        if (StringUtils.isBlank(operLog.getTitle())) {
            operLog.setTitleList(OPER_LOG);
        } else if (!OPER_LOG.contains(operLog.getTitle())) {
            return TableDataInfo.build();
        }
        return operLogService.selectPageOperLogList(operLog, pageQuery);
    }

    /**
     * 导出操作日志记录列表
     */
    @Log(title = LogTitleEnum.OTHER, businessType = BusinessType.EXPORT)
    @SaCheckPermission("monitor:operlog:export")
    @PostMapping("/export")
    public void export(SysOperLogBo operLog, HttpServletResponse response) {
        List<SysOperLogVo> list = operLogService.selectOperLogList(operLog);
        ExcelUtil.exportExcel(list, "操作日志", SysOperLogVo.class, response);
    }

    /**
     * 批量删除操作日志记录
     *
     * @param operIds 日志ids
     */
    @Log(title = LogTitleEnum.OTHER, businessType = BusinessType.DELETE)
    @SaCheckPermission("monitor:operlog:remove")
    @DeleteMapping("/{operIds}")
    public R<Void> remove(@PathVariable Long[] operIds) {
        return toAjax(operLogService.deleteOperLogByIds(operIds));
    }

    /**
     * 清理操作日志记录
     */
    @Log(title = LogTitleEnum.OTHER, businessType = BusinessType.CLEAN)
    @SaCheckPermission("monitor:operlog:remove")
    @DeleteMapping("/clean")
    public R<Void> clean() {
        operLogService.cleanOperLog();
        return R.ok();
    }
}
