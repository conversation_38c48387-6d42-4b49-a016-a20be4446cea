package com.cec.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.convert.Convert;
import com.cec.common.core.domain.R;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.log.enums.LogTitleEnum;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.web.core.BaseController;
import com.cec.system.domain.bo.SysDeptBo;
import com.cec.system.domain.vo.SysDeptVo;
import com.cec.system.service.ISysDeptService;
import com.cec.system.service.ISysPostService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 分组信息
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends BaseController {

    private final ISysDeptService deptService;
    private final ISysPostService postService;

    /**
     * 获取分组列表（分页查询，只查询parent_id=100的部门）
     */
    @SaCheckPermission("system:dept:list")
    @GetMapping("/list")
    public TableDataInfo<SysDeptVo> list(SysDeptBo dept, PageQuery pageQuery) {
        // 固定查询parent_id=100的部门
        dept.setParentId(100L);
        return deptService.selectPageDeptList(dept, pageQuery);
    }

    /**
     * 查询分组列表（排除节点）
     *
     * @param deptId 分组ID
     */
    @SaCheckPermission("system:dept:query")
    @GetMapping("/list/exclude/{deptId}")
    public R<List<SysDeptVo>> excludeChild(@PathVariable(value = "deptId", required = false) Long deptId) {
        List<SysDeptVo> depts = deptService.selectDeptList(new SysDeptBo());
        depts.removeIf(d -> d.getDeptId().equals(deptId)
            || StringUtils.splitList(d.getAncestors()).contains(Convert.toStr(deptId)));
        return R.ok(depts);
    }

    /**
     * 根据分组编号获取详细信息
     *
     * @param deptId 分组ID
     */
    @SaCheckPermission("system:dept:detail")
    @GetMapping(value = "/{deptId}")
    public R<SysDeptVo> getInfo(@PathVariable Long deptId) {
        deptService.checkDeptDataScope(deptId);
        return R.ok(deptService.selectDeptById(deptId));
    }

    /**
     * 新增分组
     */
    @SaCheckPermission("system:dept:edit")
    @Log(title = LogTitleEnum.TEAM_MANAGEMENT, businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated @RequestBody SysDeptBo dept) {
        if (!deptService.checkDeptNameUnique(dept)) {
            return R.fail(MessageUtils.message("dept.name.duplicate", dept.getDeptName()));
        }
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改分组
     */
    @SaCheckPermission("system:dept:edit")
    @Log(title = LogTitleEnum.TEAM_MANAGEMENT, businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated @RequestBody SysDeptBo dept) {
        Long deptId = dept.getDeptId();
        deptService.checkDeptDataScope(deptId);
        /*if (!deptService.checkDeptNameUnique(dept)) {
            return R.fail(MessageUtils.message("dept.update.name.duplicate", dept.getDeptName()));
        } else if (dept.getParentId().equals(deptId)) {
            return R.fail(MessageUtils.message("dept.update.self", dept.getDeptName()));
        } else if (StringUtils.equals(SystemConstants.DISABLE, dept.getStatus())) {
            if (deptService.selectNormalChildrenDeptById(deptId) > 0) {
                return R.fail(MessageUtils.message("dept.has.child"));
            } else if (deptService.checkDeptExistUser(deptId)) {
                return R.fail(MessageUtils.message("dept.has.user"));
            }
        }*/
        if (!deptService.checkDeptNameUnique(dept)) {
            return R.fail(MessageUtils.message("dept.name.duplicate", dept.getDeptName()));
        }
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除分组
     *
     * @param deptId 分组ID
     */
    @SaCheckPermission("system:dept:edit")
    @Log(title = LogTitleEnum.TEAM_MANAGEMENT, businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public R<Void> remove(@PathVariable Long deptId) {
        List<String> list = List.of("1920403120731209729", "1920403156194050049", "1920403189165473793", "1923317788601065473");
        if (list.contains(deptId.toString())) {
            return R.warn(MessageUtils.message("dept.init.not.allow.delete"));
        }
        if (deptService.hasChildByDeptId(deptId)) {
            return R.warn(MessageUtils.message("dept.has.child"));
        }
        if (deptService.checkDeptExistUser(deptId)) {
            return R.warn(MessageUtils.message("dept.has.user"));
        }
        if (postService.countPostByDeptId(deptId) > 0) {
            return R.warn(MessageUtils.message("dept.has.post"));
        }
        deptService.checkDeptDataScope(deptId);
        return toAjax(deptService.deleteDeptById(deptId));
    }

    /**
     * 获取分组选择框列表
     *
     * @param deptIds 分组ID串
     */
    @SaCheckPermission("system:dept:query")
    @GetMapping("/optionselect")
    public R<List<SysDeptVo>> optionselect(@RequestParam(required = false) Long[] deptIds) {
        return R.ok(deptService.selectDeptByIds(deptIds == null ? null : List.of(deptIds)));
    }

}
