package com.cec.system.domain.bo;

import com.cec.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 分组业务对象 sys_dept
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SysDeptBo extends BaseEntity {

    /**
     * 分组id
     */
    private Long deptId;

    /**
     * 父分组ID
     */
    private Long parentId;

    /**
     * 分组名称
     */
    @NotBlank(message = "{dept.name.not.blank}")
    @Size(min = 0, max = 100, message = "{dept.name.length}")
    private String deptName;

    /**
     * 分组类别编码
     */
    @Size(min = 0, max = 100, message = "{dept.category.length}")
    private String deptCategory;

    /**
     * 显示顺序
     */
    @NotNull(message = "{dept.order.not.null}")
    private Integer orderNum;

    /**
     * 负责人
     */
    private Long leader;

    /**
     * 联系电话
     */
    @Size(min = 0, max = 11, message = "{dept.phone.length}")
    private String phone;

    /**
     * 邮箱
     */
    @Email(message = "{dept.email.format}")
    @Size(min = 0, max = 50, message = "{dept.email.length}")
    private String email;

    /**
     * 部门状态（0正常 1停用）
     */
    private String status;

    /**
     * 归属部门id（部门树）
     */
    private Long belongDeptId;

    /**
     * 部门描述
     */
    @Size(max = 500, message = "{dept.description.length}")
    private String description;

    /**
     * 邮件签名
     */
    @Size(max = 500, message = "{dept.email.sign.length}")
    private String emailSign;


    /**
     * 用户ids
     */
    private List<Long> userIdList;

    /**
     * 分组类型
     */
    private List<String> deptType;
}
