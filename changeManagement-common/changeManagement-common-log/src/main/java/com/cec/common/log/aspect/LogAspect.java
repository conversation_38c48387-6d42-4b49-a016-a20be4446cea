package com.cec.common.log.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.utils.ServletUtils;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.json.utils.JsonUtils;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessStatus;
import com.cec.common.log.event.OperLogEvent;
import com.cec.common.satoken.utils.LoginHelper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.http.HttpMethod;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.Map;
import java.util.StringJoiner;

/**
 * 操作日志记录处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@Aspect
@AutoConfiguration
public class LogAspect {

    /**
     * 排除敏感属性字段
     */
    public static final String[] EXCLUDE_PROPERTIES = {"password", "oldPassword", "newPassword", "confirmPassword",
        "emailContent", "delayFiles"};


    /**
     * 计时 key
     */
    private static final ThreadLocal<StopWatch> KEY_CACHE = new ThreadLocal<>();

    /**
     * 处理请求前执行
     */
    @Before(value = "@annotation(controllerLog)")
    public void doBefore(JoinPoint joinPoint, Log controllerLog) {
        StopWatch stopWatch = new StopWatch();
        KEY_CACHE.set(stopWatch);
        stopWatch.start();
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Log controllerLog, Object jsonResult) {
        handleLog(joinPoint, controllerLog, null, jsonResult);
    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Log controllerLog, Exception e) {
        handleLog(joinPoint, controllerLog, e, null);
    }

    protected void handleLog(final JoinPoint joinPoint, Log controllerLog, final Exception e, Object jsonResult) {
        try {

            // *========数据库日志=========*//
            OperLogEvent operLog = new OperLogEvent();
            operLog.setTenantId(LoginHelper.getTenantId());
            operLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            // 请求的地址
            String ip = ServletUtils.getClientIP();
            operLog.setOperIp(ip);
            operLog.setOperUrl(StringUtils.substring(ServletUtils.getRequest().getRequestURI(), 0, 255));
            LoginUser loginUser = LoginHelper.getLoginUser();
            operLog.setOperName(loginUser.getUsername());
            operLog.setDeptName(loginUser.getDeptName());

            if (e != null) {
                operLog.setStatus(BusinessStatus.FAIL.ordinal());
                operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 3800));
            }
            // 设置方法名称
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            operLog.setMethod(className + "." + methodName + "()");
            // 设置请求方式
            operLog.setRequestMethod(ServletUtils.getRequest().getMethod());
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, controllerLog, operLog, jsonResult);
            // 设置消耗时间
            StopWatch stopWatch = KEY_CACHE.get();
            stopWatch.stop();
            operLog.setCostTime(stopWatch.getDuration().toMillis());
            // 发布事件保存数据库
            SpringUtils.context().publishEvent(operLog);
        } catch (Exception exp) {
            // 记录本地异常日志
            log.error("异常信息:{}", exp.getMessage());
        } finally {
            KEY_CACHE.remove();
        }
    }

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param log     日志
     * @param operLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, Log log, OperLogEvent operLog, Object jsonResult) throws Exception {
        // 设置action动作
        operLog.setBusinessType(log.businessType().ordinal());
        // 设置标题
        operLog.setTitle(log.title().getCode());
        // 设置操作人类别
        operLog.setOperatorType(log.operatorType().ordinal());
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData()) {
            // 获取参数的信息，传入到数据库中。
            setRequestValue(joinPoint, operLog, log.excludeParamNames());
        }
        // 是否需要保存response，参数和值
        if (log.isSaveResponseData() && ObjectUtil.isNotNull(jsonResult)) {
            operLog.setJsonResult(JsonUtils.toJsonString(jsonResult));
        }
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param operLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(JoinPoint joinPoint, OperLogEvent operLog, String[] excludeParamNames) throws Exception {
        Map<String, String> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
        String requestMethod = operLog.getRequestMethod();
        if (MapUtil.isEmpty(paramsMap) && StringUtils.equalsAny(requestMethod, HttpMethod.PUT.name(), HttpMethod.POST.name(), HttpMethod.DELETE.name())) {
            String jsonOriginal = argsArrayToString(joinPoint.getArgs(), excludeParamNames);
            operLog.setOperParamOriginal(jsonOriginal);
            // 处理JSON：1.去掉包含Id字段但不包括staffId的内容 2.移除HTML标签 3.去掉引号
            String processedJson = processJsonForParam(jsonOriginal);
            operLog.setOperParam(processedJson);
        } else {
            MapUtil.removeAny(paramsMap, EXCLUDE_PROPERTIES);
            MapUtil.removeAny(paramsMap, excludeParamNames);
            String jsonOriginal = JsonUtils.toJsonString(paramsMap);
            operLog.setOperParamOriginal(jsonOriginal);
            // 处理JSON：1.去掉包含Id字段但不包括staffId的内容 2.移除HTML标签 3.去掉引号
            String processedJson = processJsonForParam(jsonOriginal);
            operLog.setOperParam(processedJson);
        }
    }

    /**
     * 处理JSON参数
     * 1. 去掉包含Id字段但不包括staffId的内容
     * 2. 移除HTML标签但保留内容
     * 3. 去掉JSON中的引号
     *
     * @param json 原始JSON字符串
     * @return 处理后的字符串
     */
    private String processJsonForParam(String json) {
        if (StringUtils.isEmpty(json)) {
            return json;
        }

        try {
            // 递归处理JSON，移除Id字段和空字段
            Object processedObj = processJsonObject(JsonUtils.parseMap(json));
            // 转换回字符串
            // json = JsonUtils.toJsonString(processedObj);
            json = JSONUtil.toJsonPrettyStr(processedObj);
            // 移除HTML标签但保留内容
            json = json.replaceAll("<[^>]*>", "");
            // 去掉JSON中的引号
            json = json.replaceAll("\"", "");
            return json;
        } catch (Exception e) {
            // log.error("处理JSON参数异常", e);
            return json;
        }
    }

    /**
     * 递归处理JSON对象，移除Id字段和空字段
     *
     * @param obj JSON对象
     * @return 处理后的对象
     */
    @SuppressWarnings("unchecked")
    private Object processJsonObject(Object obj) {
        if (obj == null) {
            return null;
        }

        // 处理Map类型
        if (obj instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) obj;

            // 处理枚举类型 {code:x,info:y} 格式
            if (isEnumFormat(map)) {
                return map.get("info");
            }

            Map<String, Object> result = MapUtil.newHashMap();

            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                // 跳过Id字段（除staffId外）
                if ((key.contains("Id") || key.equals("id")) && !key.equals("staffId")) {
                    continue;
                }

                // 跳过空值
                if (value == null || (value instanceof String && StringUtils.isEmpty((String) value))) {
                    continue;
                }

                // 递归处理嵌套对象
                result.put(key, processJsonObject(value));
            }

            return result;
        }

        // 处理List类型
        if (obj instanceof Collection) {
            Collection<Object> collection = (Collection<Object>) obj;
            Collection<Object> result = new java.util.ArrayList<>();

            for (Object item : collection) {
                // 递归处理集合中的每个元素
                Object processedItem = processJsonObject(item);
                if (processedItem != null) {
                    result.add(processedItem);
                }
            }

            return result;
        }

        // 其他类型直接返回
        return obj;
    }

    /**
     * 判断是否为枚举格式 {code:x,info:y}
     *
     * @param map 要检查的Map
     * @return 是否为枚举格式
     */
    private boolean isEnumFormat(Map<String, Object> map) {
        if (map.containsKey("code") && map.containsKey("info")) {
            return true;
        }
        return false;
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray, String[] excludeParamNames) {
        StringJoiner params = new StringJoiner(" ");
        if (ArrayUtil.isEmpty(paramsArray)) {
            return params.toString();
        }
        for (Object o : paramsArray) {
            if (ObjectUtil.isNotNull(o)) {
                // 提取上传文件的文件名
                if (o instanceof MultipartFile) {
                    MultipartFile file = (MultipartFile) o;
                    params.add("fileName: " + file.getOriginalFilename());
                    continue;
                } else if (o instanceof MultipartFile[]) {
                    MultipartFile[] files = (MultipartFile[]) o;
                    StringJoiner fileNames = new StringJoiner(",", "fileNames: [", "]");
                    for (MultipartFile file : files) {
                        fileNames.add(file.getOriginalFilename());
                    }
                    params.add(fileNames.toString());
                    continue;
                }

                if (!isFilterObject(o)) {
                    String str = JsonUtils.toJsonString(o);
                    Dict dict = JsonUtils.parseMap(str);
                    if (MapUtil.isNotEmpty(dict)) {
                        MapUtil.removeAny(dict, EXCLUDE_PROPERTIES);
                        MapUtil.removeAny(dict, excludeParamNames);
                        str = JsonUtils.toJsonString(dict);
                    }
                    params.add(str);
                }
            }
        }
        return params.toString();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType() != MultipartFile.class &&
                   MultipartFile.class.isAssignableFrom(clazz.getComponentType());
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Object value : map.values()) {
                return value instanceof MultipartFile;
            }
        }
        return o instanceof HttpServletRequest || o instanceof HttpServletResponse
               || o instanceof BindingResult;
    }
}
