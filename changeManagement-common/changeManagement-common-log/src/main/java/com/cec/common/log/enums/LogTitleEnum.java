package com.cec.common.log.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日志模块枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum LogTitleEnum {
    /**
     * 应用管理
     */
    APPLICATION_MANAGEMENT("APPLICATION_MANAGEMENT", "应用管理"),
    /**
     * 配置管理
     */
    CONFIG_MANAGEMENT("CONFIG_MANAGEMENT", "配置管理"),
    /**
     * 变更管理
     */
    CHANGE_MANAGEMENT("CHANGE_MANAGEMENT", "变更管理"),
    /**
     * 统计
     */
    STATISTICS("STATISTICS", "统计"),
    /**
     * 变更审批
     */
    CHANGE_APPROVAL("CHANGE_APPROVAL", "变更审批"),
    /**
     * 变更取消
     */
    CHANGE_CANCEL("CHANGE_CANCEL", "变更取消"),
    /**
     * 封网管理
     */
    NETWORK_FREEZE_MANAGEMENT("NETWORK_FREEZE_MANAGEMENT", "封网管理"),
    /**
     * 封网审批
     */
    NETWORK_FREEZE_APPROVAL("NETWORK_FREEZE_APPROVAL", "封网审批"),
    /**
     * 分组管理
     */
    TEAM_MANAGEMENT("TEAM_MANAGEMENT", "分组管理"),
    /**
     * 文件存储
     */
    FILE_STORAGE("FILE_STORAGE", "文件存储"),
    /**
     * 角色管理
     */
    ROLE_MANAGEMENT("ROLE_MANAGEMENT", "角色管理"),
    /**
     * 用户管理
     */
    USER_MANAGEMENT("USER_MANAGEMENT", "用户管理"),
    /**
     * 登陆日志
     */
    LOGIN_LOG("LOGIN_LOG", "登陆日志"),
    /**
     * 变更延期记录
     */
    CHANGE_DELAY("CHANGE_DELAY", "变更延期"),
    /**
     * 其他
     */
    OTHER("OTHER", "其他"),;

    private final String code;
    private final String info;

}
