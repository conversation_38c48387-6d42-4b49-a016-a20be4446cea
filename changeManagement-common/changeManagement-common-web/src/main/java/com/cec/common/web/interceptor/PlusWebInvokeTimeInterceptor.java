package com.cec.common.web.interceptor;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.json.utils.JsonUtils;
import com.cec.common.web.filter.RepeatedlyRequestWrapper;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.io.BufferedReader;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * web的调用时间统计拦截器
 *
 * <AUTHOR>
 * @since 3.3.0
 */
@Slf4j
public class PlusWebInvokeTimeInterceptor implements HandlerInterceptor {

    private final static ThreadLocal<StopWatch> KEY_CACHE = new ThreadLocal<>();
    private final static String SENSITIVE_FIELD = "password";
    private final static String MASK_VALUE = "******";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String url = request.getMethod() + " " + request.getRequestURI();

        // 打印请求参数
        if (isJsonRequest(request)) {
            String jsonParam = "";
            String requestClass = request.getClass().getSimpleName();
            String contentType = request.getContentType();

            log.debug("[PLUS]处理JSON请求: {}, 请求类型: {}, ContentType: {}", url, requestClass, contentType);

            try {
                if (request instanceof RepeatedlyRequestWrapper) {
                    BufferedReader reader = request.getReader();
                    jsonParam = IoUtil.read(reader);
                    log.debug("[PLUS]成功从RepeatedlyRequestWrapper读取参数: {} 字符", jsonParam.length());
                } else {
                    // 如果不是RepeatedlyRequestWrapper，记录详细信息用于调试
                    log.warn("[PLUS]请求未被RepeatableFilter包装: {}, 请求类型: {}, ContentType: {}",
                            url, requestClass, contentType);

                    // 尝试从请求体读取（这会消耗流，但用于调试）
                    if (request.getContentLength() > 0) {
                        try {
                            BufferedReader reader = request.getReader();
                            jsonParam = IoUtil.read(reader);
                            log.debug("[PLUS]直接读取到参数: {} 字符", jsonParam.length());
                        } catch (Exception readEx) {
                            log.warn("[PLUS]直接读取请求体失败: {}", readEx.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("[PLUS]读取JSON参数失败: {}, 错误: {}", url, e.getMessage());
                jsonParam = "";
            }

            // 对包含密码的参数进行脱敏处理
            String maskedParam = maskSensitiveInfo(jsonParam);
            log.info("[PLUS]开始请求 => URL[{}],参数类型[json],参数:[{}]", url, maskedParam);
        } else {
            Map<String, String[]> parameterMap = request.getParameterMap();
            if (MapUtil.isNotEmpty(parameterMap)) {
                // 对参数Map中的敏感信息进行脱敏
                Map<String, String[]> maskedMap = new HashMap<>(parameterMap);
                if (maskedMap.containsKey(SENSITIVE_FIELD)) {
                    maskedMap.put(SENSITIVE_FIELD, new String[] { MASK_VALUE });
                }
                String parameters = JsonUtils.toJsonString(maskedMap);
                log.info("[PLUS]开始请求 => URL[{}],参数类型[param],参数:[{}]", url, parameters);
            } else {
                log.info("[PLUS]开始请求 => URL[{}],无参数", url);
            }
        }

        StopWatch stopWatch = new StopWatch();
        KEY_CACHE.set(stopWatch);
        stopWatch.start();

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        StopWatch stopWatch = KEY_CACHE.get();
        if (ObjectUtil.isNotNull(stopWatch)) {
            stopWatch.stop();
            log.info("[PLUS]结束请求 => URL[{}],耗时:[{}]毫秒", request.getMethod() + " " + request.getRequestURI(), stopWatch.getDuration().toMillis());
            KEY_CACHE.remove();
        }
    }

    /**
     * 判断本次请求的数据类型是否为json
     *
     * @param request request
     * @return boolean
     */
    private boolean isJsonRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        if (contentType != null) {
            return StringUtils.startsWithIgnoreCase(contentType, MediaType.APPLICATION_JSON_VALUE);
        }
        return false;
    }

    /**
     * 对敏感信息进行脱敏处理
     *
     * @param json JSON字符串
     * @return 脱敏后的JSON字符串
     */
    private String maskSensitiveInfo(String json) {
        if (StringUtils.isEmpty(json)) {
            return json;
        }

        // 使用正则表达式匹配并替换password字段的值
        String pattern = "\"" + SENSITIVE_FIELD + "\"\\s*:\\s*\"([^\"]+)\"";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(json);

        if (m.find()) {
            return m.replaceAll("\"" + SENSITIVE_FIELD + "\":\"" + MASK_VALUE + "\"");
        }

        return json;
    }
}
