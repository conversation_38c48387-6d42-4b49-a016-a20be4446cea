package com.cec.common.web.filter;

import com.cec.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * Repeatable 过滤器
 *
 * <AUTHOR>
 */
@Slf4j
public class RepeatableFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
        throws IOException, ServletException {
        ServletRequest requestWrapper = null;

        if (request instanceof HttpServletRequest httpRequest) {
            String contentType = httpRequest.getContentType();
            String method = httpRequest.getMethod();

            // 判断是否为JSON请求且需要包装
            boolean isJsonRequest = StringUtils.startsWithIgnoreCase(contentType, MediaType.APPLICATION_JSON_VALUE);
            boolean needsWrapper = isJsonRequest && ("POST".equals(method) || "PUT".equals(method) || "PATCH".equals(method));

            if (needsWrapper) {
                try {
                    requestWrapper = new RepeatedlyRequestWrapper(httpRequest, response);
                } catch (Exception e) {
                    log.error("[RepeatableFilter] 包装请求失败: {} {}, 错误: {}", method, httpRequest.getRequestURI(), e.getMessage());
                }
            }
        }

        if (null == requestWrapper) {
            chain.doFilter(request, response);
        } else {
            chain.doFilter(requestWrapper, response);
        }
    }

    @Override
    public void destroy() {

    }
}
