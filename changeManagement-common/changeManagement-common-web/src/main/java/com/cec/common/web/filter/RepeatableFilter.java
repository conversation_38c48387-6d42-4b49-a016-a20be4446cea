package com.cec.common.web.filter;

import com.cec.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * Repeatable 过滤器
 *
 * <AUTHOR>
 */
@Slf4j
public class RepeatableFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("[RepeatableFilter] 过滤器已初始化");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
        throws IOException, ServletException {
        ServletRequest requestWrapper = null;

        if (request instanceof HttpServletRequest httpRequest) {
            String contentType = httpRequest.getContentType();
            String method = httpRequest.getMethod();
            String uri = httpRequest.getRequestURI();

            log.debug("[RepeatableFilter] 处理请求: {} {}, ContentType: {}", method, uri, contentType);

            // 判断是否为JSON请求且需要包装
            boolean isJsonRequest = StringUtils.startsWithIgnoreCase(contentType, MediaType.APPLICATION_JSON_VALUE);
            boolean needsWrapper = isJsonRequest && ("POST".equals(method) || "PUT".equals(method) || "PATCH".equals(method));

            if (needsWrapper) {
                try {
                    requestWrapper = new RepeatedlyRequestWrapper(httpRequest, response);
                    log.debug("[RepeatableFilter] 成功包装请求: {} {}", method, uri);
                } catch (Exception e) {
                    log.error("[RepeatableFilter] 包装请求失败: {} {}, 错误: {}", method, uri, e.getMessage());
                }
            } else {
                log.debug("[RepeatableFilter] 跳过包装: {} {}, isJson: {}, needsWrapper: {}", method, uri, isJsonRequest, needsWrapper);
            }
        }

        if (null == requestWrapper) {
            chain.doFilter(request, response);
        } else {
            chain.doFilter(requestWrapper, response);
        }
    }

    @Override
    public void destroy() {

    }
}
