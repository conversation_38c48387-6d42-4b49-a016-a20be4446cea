<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cec</groupId>
        <artifactId>changeManagement-backend</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>changeManagement-common-bom</module>
        <module>changeManagement-common-core</module>
        <module>changeManagement-common-doc</module>
        <module>changeManagement-common-social</module>
        <module>changeManagement-common-excel</module>
        <module>changeManagement-common-idempotent</module>
        <module>changeManagement-common-log</module>
        <module>changeManagement-common-mail</module>
        <module>changeManagement-common-mybatis</module>
        <module>changeManagement-common-oss</module>
        <module>changeManagement-common-ratelimiter</module>
        <module>changeManagement-common-redis</module>
        <module>changeManagement-common-satoken</module>
        <module>changeManagement-common-security</module>
        <module>changeManagement-common-web</module>
        <module>changeManagement-common-translation</module>
        <module>changeManagement-common-sensitive</module>
        <module>changeManagement-common-json</module>
        <module>changeManagement-common-encrypt</module>
        <module>changeManagement-common-tenant</module>
    </modules>

    <artifactId>changeManagement-common</artifactId>
    <packaging>pom</packaging>

    <description>
        common 通用模块
    </description>

</project>
