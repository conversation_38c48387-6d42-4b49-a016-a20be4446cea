package com.cec.common.core.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
 */

@Data
@Component
@Slf4j
@ConfigurationProperties(prefix = "cec-smartflow")
public class CecSmartFlowConfig {

    /***
     * smartFlow域名
     */
    private String smartFlowDomain;

    /***
     * smartFlow登录账号
     */
    private String smartFlowAccount;

    /***
     * smartFlow登录密码
     */
    private String smartFlowPwd;

    /***
     * smartFlow接口秘钥
     */
    private String appSecret;

    /**
     * smartFlow参数配置
     */
    private Param param;

    /**
     * 是否同步所有用户数据数据
     */
    private Boolean syncSmartFlowAll = false;

    @Data
    public static class Param {
        /**
         * 部门ID
         */
        private String deptId;

        /**
         * 部门名称
         */
        private String deptName;

        /**
         * 表单Key
         */
        private String formKey;

        /**
         * 业务线
         */
        private Integer bizLine;

        /**
         * 外部组织ID
         */
        private String externalOrgId;

        /**
         * 外部区域ID
         */
        private String externalRegionId;

        /**
         * 区域
         */
        private String region;
    }
}
