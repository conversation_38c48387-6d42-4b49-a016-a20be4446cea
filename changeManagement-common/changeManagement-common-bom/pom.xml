<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cec</groupId>
    <artifactId>changeManagement-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>
        changeManagement-common-bom common依赖项
    </description>

    <properties>
        <revision>5.3.1</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 幂等 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 邮件服务 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-mail</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- OSS -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-ratelimiter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- satoken -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-social</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 翻译模块 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-translation</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 脱敏模块 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-sensitive</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 序列化模块 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-json</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库加解密模块 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-encrypt</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 租户模块 -->
            <dependency>
                <groupId>com.cec</groupId>
                <artifactId>changeManagement-common-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>
