package com.cec.common.excel.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义列宽策略，同时考虑表头和内容的宽度
 *
 * <AUTHOR>
 */
public class CustomColumnWidthStrategy extends AbstractColumnWidthStyleStrategy {

    // 记录每列的最大宽度
    private final Map<Integer, Integer> columnWidthMap = new HashMap<>();
    
    // 表头宽度系数，根据需要调整
    private static final int HEAD_WIDTH_COEFFICIENT = 2;
    
    // 内容宽度系数，根据需要调整
    private static final int CONTENT_WIDTH_COEFFICIENT = 1;
    
    // 最小列宽
    private static final int MIN_COLUMN_WIDTH = 15;
    
    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, 
                                 Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        int columnIndex = cell.getColumnIndex();
        
        if (isHead) {
            // 计算表头宽度：字符数 * 系数 (中文字符需要更宽)
            String headText = head.getHeadNameList().get(0);
            int headWidth = calculateWidth(headText, HEAD_WIDTH_COEFFICIENT);
            
            // 如果当前表头宽度大于已记录的最大宽度，则更新
            if (!columnWidthMap.containsKey(columnIndex) || headWidth > columnWidthMap.get(columnIndex)) {
                columnWidthMap.put(columnIndex, headWidth);
            }
        } else {
            // 处理内容宽度
            if (cellDataList != null && !cellDataList.isEmpty()) {
                WriteCellData<?> cellData = cellDataList.get(0);
                if (cellData != null && cellData.getStringValue() != null) {
                    int contentWidth = calculateWidth(cellData.getStringValue(), CONTENT_WIDTH_COEFFICIENT);
                    
                    // 如果内容宽度大于已记录的最大宽度，则更新
                    if (!columnWidthMap.containsKey(columnIndex) || contentWidth > columnWidthMap.get(columnIndex)) {
                        columnWidthMap.put(columnIndex, contentWidth);
                    }
                }
            }
        }
        
        // 设置列宽，如果已经有记录值则使用，否则使用最小宽度
        Integer width = columnWidthMap.getOrDefault(columnIndex, MIN_COLUMN_WIDTH);
        // Excel列宽单位是1/256个字符宽度
        writeSheetHolder.getSheet().setColumnWidth(columnIndex, width * 256);
    }
    
    /**
     * 计算字符串的显示宽度
     * 
     * @param text 文本内容
     * @param coefficient 宽度系数
     * @return 计算后的宽度值
     */
    private int calculateWidth(String text, int coefficient) {
        if (text == null || text.isEmpty()) {
            return MIN_COLUMN_WIDTH;
        }
        
        int width = 0;
        for (char c : text.toCharArray()) {
            // 中文和特殊字符宽度算2个单位，ASCII字符算1个单位
            if (c > 256) {
                width += 2;
            } else {
                width += 1;
            }
        }
        
        // 应用系数并设定最小宽度
        width = width * coefficient;
        return Math.max(width, MIN_COLUMN_WIDTH);
    }
} 