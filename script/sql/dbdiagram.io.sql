

Table "cm_application_manage" {
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "update_time" datetime [default: NULL, note: '更新时间']
  "id" bigint [pk, not null, increment]
  "application_name" varchar(200) [default: NULL, note: '应用名称']
  "business_owner" json [note: 'BusinessOwner']
  "team_leader" json [note: 'TeamLeader']
  "team_id" bigint [default: NULL, note: 'Team_ID']
  "team_name" varchar(100) [default: NULL, note: 'Team_Name']
  "category_id" bigint [default: NULL, note: '分类id']
  "category_name" varchar(100) [default: NULL, note: '分类']
  "is_key_project" tinyint [default: NULL, note: '核心项目（1-是 2-否）']
  "is_external_system" tinyint [default: NULL, note: '外部项目（1-是 2-否）']
  "maintenance_level" tinyint [default: NULL, note: '维护影响等级（1-高 2-中 3-低)）']
  "status" tinyint [default: NULL, note: '状态（1-可用 2-非可用）']
  "simple_check_list_ids" varchar(255) [default: NULL, note: '简单检查表ids']
  "full_check_list_ids" varchar(255) [default: NULL, note: '全量检查表ids']
  "freeze_area_list" json [note: '封网地区']
  Note: '应用管理表'
}

Table "cm_change_delay" {
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "update_time" datetime [default: NULL, note: '更新时间']
  "id" bigint [pk, not null, increment]
  "change_id" bigint [default: NULL, note: '变更id']
  "schedule_end_time" datetime [default: NULL, note: '新结束时间']
  "remark" varchar(255) [default: NULL, note: '备注']
  "delay_files" varchar(255) [default: NULL, note: '延期文件']
  Note: '变更延期记录'
}

Table "cm_change_info" {
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "update_time" datetime [default: NULL, note: '更新时间']
  "id" bigint [pk, not null, increment]
  "title" varchar(200) [not null, note: '变更标题']
  "requester" json [not null, note: '申请人']
  "team_id" bigint [default: NULL, note: '变更组ID']
  "team_name" varchar(100) [default: NULL, note: '变更组名']
  "is_urgent_change" tinyint [default: NULL, note: '是否紧急变更（1-是 2-否）']
  "change_type" varchar(50) [default: NULL, note: '变更类型']
  "is_network_freeze_change" tinyint [default: NULL, note: '是否封网变更（1-是 2-否）']
  "category_ids" varchar(200) [default: NULL, note: '分类id']
  "category_name_list" varchar(200) [default: NULL, note: '分类名称']
  "application_ids" varchar(200) [default: NULL, note: '应用/系统id']
  "application_name" text [note: '应用/系统名称']
  "location_id" bigint [default: NULL, note: '地点id']
  "location_name" varchar(100) [default: NULL, note: '地点名']
  "src" varchar(200) [default: NULL, note: '相关SRC/MSP']
  "priority" tinyint [default: NULL, note: '优先级(1-高 2-中 3-低)']
  "plan_time_start" datetime [default: NULL, note: '计划开始时间']
  "plan_time_end" datetime [default: NULL, note: '计划结束时间']
  "delay_end_time" datetime [default: NULL, note: '延期结束时间']
  "temporary_url" varchar(200) [default: NULL, note: '临时访问链接']
  "change_reason" text [default: NULL, note: '变更原因']
  "change_description" text [default: NULL, note: '变更描述']
  "file_url" varchar(255) [default: NULL, note: '附件地址']
  "affected_description" text [default: NULL, note: '影响描述']
  "affected_time" int [default: NULL, note: '影响时间']
  "affected_time_type" tinyint [default: NULL, note: '影响时间类型(1-分钟 2-小时)']
  "affected_user" varchar(5000) [default: NULL, note: '影响用户']
  "affected_application_ids" varchar(255) [default: NULL, note: '影响系统ids']
  "affected_application_name" varchar(255) [default: NULL, note: '影响系统名称']
  "affected_device_ids" varchar(255) [default: NULL, note: '影响设备ids']
  "affected_device_name" varchar(255) [default: NULL, note: '影响设备名']
  "notification_email" varchar(255) [default: NULL, note: '通知电邮地址']
  "notification_cc_email" varchar(255) [default: NULL, note: '通知电邮抄送地址']
  "team_leader_list" json [note: 'Team Leader List']
  "tester_list" json [note: 'Tester List']
  "change_approver_list" json [note: '变更审批人list']
  "service_delivery_team_list" json [note: '服务提交团队list']
  "application_owner_list" json [note: '系统所有人list']
  "change_implementer_list" json [note: '变更实施人list']
  "change_verifier_list" json [note: '变更验证人list']
  "change_owner_list" json [note: '变更所有者list']
  "dept_leader_list" json [note: '部门负责人list']
  "urgent_change_inspector_list" json [note: '紧急变更核查人list']
  "systems_affected_no" int [default: NULL, note: '受影响系统个数']
  "important_users" int [default: NULL, note: '受影响重要用户']
  "fallback" int [default: NULL, note: '回退/回滚']
  "complexity" int [default: NULL, note: '变更复杂性/实施经验']
  "risk_level" tinyint [default: NULL, note: '风险等级(1-低 2-中 3-高 4-不可接受)']
  "risk_score" int [default: NULL, note: '风险得分']
  "request_doc" text [note: '需求文档']
  "request_doc_file_ids" text [note: '需求文档附件ids']
  "test_doc" text [note: '测试文档']
  "test_doc_file_ids" text [note: '测试档附件ids']
  "code_review" text [note: '代码复查']
  "code_review_file_ids" text [note: '代码复查附件ids']
  "roll_out_plan" text [note: '上线计划']
  "backout_plan" text [note: '回退计划']
  "check_list" text [note: '上线检查清单']
  "review_check_list" text [note: '核查清单']
  "email_content" text [note: '邮件内容']
  "special_remark" varchar(500) [default: NULL, note: '特殊备注']
  "freeze_info" json [note: '封网信息']

  Note: '变更申请表'
}

Table "cm_change_item" {
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "update_time" datetime [default: NULL, note: '更新时间']
  "id" bigint [pk, not null, increment]
  "change_id" bigint [default: NULL, note: '变更ID']
  "change_code" varchar(200) [default: NULL, note: '变更编号']
  "title" varchar(200) [default: NULL, note: '变更标题']
  "stage" tinyint [default: NULL, note: '阶段']
  "processor_list" json [note: '当前处理人List']
  "team_id" bigint [default: NULL, note: '变更组ID']
  "team_name" varchar(100) [default: NULL, note: '变更组名']
  "is_urgent_change" tinyint [default: NULL, note: '是否紧急变更（1-是 2-否）']
  "priority" tinyint [default: NULL, note: '优先级(1-高 2-中 3-低)']
  "instance_id" varchar(20) [default: NULL, note: '流程实例id']
  "variables" text [note: 'smartFlow variables']
  "complete_time" datetime [default: NULL, note: '完成时间']
  "submit_time" datetime [default: NULL, note: '提交时间']
  "requester" json [note: '申请人']
  "is_network_freeze_change" tinyint [default: NULL, note: '是否封网变更（1-是 2-否）']
  "location_id" bigint [default: NULL, note: '地点id']
  "location_name" varchar(100) [default: NULL, note: '地点名']
  Note: '变更申请记录表'
}

Table "cm_change_record" {
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "update_time" datetime [default: NULL, note: '更新时间']
  "id" bigint [pk, not null, increment]
  "change_item_id" bigint [default: NULL, note: '变更记录ID']
  "change_id" bigint [default: NULL, note: '变更ID']
  "change_code" varchar(200) [default: NULL, note: '变更编号']
  "title" varchar(200) [default: NULL, note: '变更标题']
  "stage" tinyint [default: NULL, note: '阶段']
  "processor_status" tinyint [default: NULL, note: '审批状态']
  "process_order" int [default: NULL, note: '顺序']
  "processor" json [note: '当前处理人']
  "processor_time" datetime [default: NULL, note: '审批时间']
  "team_id" bigint [default: NULL, note: '变更组ID']
  "team_name" varchar(100) [default: NULL, note: '变更组名']
  "is_urgent_change" tinyint [default: NULL, note: '是否紧急变更（1-是 2-否）']
  "priority" tinyint [default: NULL, note: '优先级(1-高 2-中 3-低)']
  "opinion" varchar(2000) [default: NULL, note: '审批意见/备注']
  "is_seal_addition" tinyint [default: NULL, note: '是否需要加签（1-是 2-否）']
  "step" varchar(500) [default: NULL, note: '变更/回滚步骤']
  "task_id" varchar(20) [default: NULL, note: '任务id']
  "countersign_type" tinyint [default: NULL, note: '会签类型（0-会签 1-或签）']
  "countersign" tinyint [default: NULL, note: '是否允许加签（0-否 1-是）']
  "strategy" text [note: 'smartFlow节点策略']
  "next_list" text [note: 'smartFlow待发送节点列表']
  "task_current" text [note: 'smartFlow当前任务信息']
  "is_network_freeze_change" tinyint [default: NULL, note: '是否封网变更（1-是 2-否）']
  "requester" json [note: '申请人']
  "location_id" bigint [default: NULL, note: '地点id']
  "location_name" varchar(100) [default: NULL, note: '地点名']
  "node_name" varchar(100) [default: NULL, note: '节点名称']
  "is_or_sign_processed" tinyint [default: NULL, note: '或签是否已被处理（1-是 2-否）']
  Note: '变更申请记录流水表'
}

Table "cm_conf_info" {
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "update_time" datetime [default: NULL, note: '更新时间']
  "id" bigint [pk, not null, increment]
  "type" tinyint [default: NULL, note: '类型（1-分类 2-封网地区 3- 变更地点 4-受影响设备 5-维护种类）']
  "name" varchar(100) [default: NULL, note: '名称']
  "description" varchar(500) [default: NULL, note: '描述']
  "status" tinyint [default: NULL, note: '状态（1-可用 2-非可用)']
  "name_en" varchar(100) [default: NULL, note: '名称-英文']
  Note: '配置信息'
}

Table "cm_modify_log" {
  "id" bigint [pk, not null, note: '主键']
  "change_code" varchar(50) [default: NULL, note: '变更编号']
  "modify_filed" varchar(50) [default: NULL, note: '修改字段']
  "filed_name_zh" varchar(200) [default: NULL, note: '修改字段_zh']
  "filed_name_us" varchar(200) [default: NULL, note: '修改字段_us']
  "filed_name_tw" varchar(200) [default: NULL, note: '修改字段_tw']
  "content_old" text [note: '修改内容-旧']
  "content_new" text [note: '修改内容-新']
  "staff_id" varchar(20) [not null, note: 'StaffId']
  "staff_name" varchar(30) [not null, note: 'StaffName']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "remark" varchar(500) [default: NULL, note: '备注']
  Note: '变更修改记录'
}

Table "cm_network_freeze_area" {
  "id" bigint [pk, not null, note: '主键']
  "freeze_id" bigint [default: NULL, note: '封网Id']
  "start_time" datetime [default: NULL, note: '封网开始时间']
  "end_time" datetime [default: NULL, note: '封网结束时间']
  "area_id" varchar(500) [default: NULL, note: '封网地区Id数组']
  "area_name" varchar(1000) [default: NULL, note: '封网地区']
  "area_name_en" varchar(1000) [default: NULL, note: '封网地区-英文']
  Note: '封网时间地区'
}

Table "cm_network_freeze_info" {
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "update_time" datetime [default: NULL, note: '更新时间']
  "id" bigint [pk, not null, increment]
  "title" varchar(200) [not null, note: '封网名称']
  "level" tinyint [not null, note: '封网等级']
  "freeze_apps" text [note: '封禁应用']
  "file_ids" varchar(255) [default: NULL, note: '附件ids']
  "is_attachment" tinyint [default: NULL, note: '是否增加到邮件附件（1-是 2否）']
  "notification_email_to" text [note: '通知电邮地址']
  "notification_email_cc" text [note: '通知抄送电邮地址']
  "special_remark" varchar(500) [default: NULL, note: '特殊备注']
  "email_content" text [note: '邮件内容']
  "requester" json [note: '申请人']
  Note: '封网申请表'
}

Table "cm_network_freeze_item" {
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "update_time" datetime [default: NULL, note: '更新时间']
  "id" bigint [pk, not null, increment]
  "freeze_id" bigint [default: NULL, note: '变更ID']
  "freeze_code" varchar(200) [default: NULL, note: '变更编号']
  "title" varchar(200) [not null, note: '封网名称']
  "level" tinyint [not null, note: '封网等级']
  "stage" tinyint [default: NULL, note: '阶段']
  "processor_list" json [note: '当前处理人List']
  "instance_id" varchar(20) [default: NULL, note: '流程实例id']
  "variables" varchar(255) [default: NULL, note: 'smartFlow variables']
  "freeze_start_time" datetime [default: NULL, note: '封网开始时间']
  "freeze_end_time" datetime [default: NULL, note: '封网结束时间']
  Note: '封网申请记录表'
}

Table "cm_network_freeze_record" {
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "update_time" datetime [default: NULL, note: '更新时间']
  "id" bigint [pk, not null, increment]
  "freeze_item_id" bigint [default: NULL, note: '封网记录ID']
  "freeze_id" bigint [default: NULL, note: '封网ID']
  "freeze_code" varchar(200) [default: NULL, note: '封网编号']
  "title" varchar(200) [default: NULL, note: '变更标题']
  "level" tinyint [not null, note: '封网等级']
  "stage" tinyint [default: NULL, note: '阶段']
  "processor_status" tinyint [default: NULL, note: '审批状态']
  "processor" json [note: '当前处理人']
  "processor_time" datetime [default: NULL, note: '审批时间']
  "opinion" varchar(2000) [default: NULL, note: '审批意见/备注']
  "process_order" int [default: NULL, note: '顺序']
  "task_id" varchar(20) [default: NULL, note: '任务id']
  "countersign_type" tinyint [default: NULL, note: '会签类型（0-会签 1-或签）']
  "strategy" text [note: 'smartFlow节点策略']
  "next_list" text [note: 'smartFlow待发送节点列表']
  "task_current" text [note: 'smartFlow当前任务信息']
  "node_name" varchar(100) [default: NULL, note: '节点名']
  Note: '封网申请记录流水表'
}

Table "sys_client" {
  "id" bigint [pk, not null, note: 'id']
  "client_id" varchar(64) [default: NULL, note: '客户端id']
  "client_key" varchar(32) [default: NULL, note: '客户端key']
  "client_secret" varchar(255) [default: NULL, note: '客户端秘钥']
  "grant_type" varchar(255) [default: NULL, note: '授权类型']
  "device_type" varchar(32) [default: NULL, note: '设备类型']
  "active_timeout" int [default: 1800, note: 'token活跃超时时间']
  "timeout" int [default: 604800, note: 'token固定超时']
  "status" char(1) [default: '0', note: '状态（0正常 1停用）']
  "del_flag" char(1) [default: '0', note: '删除标志（0代表存在 1代表删除）']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  Note: '系统授权表'
}

Table "sys_config" {
  "config_id" bigint [pk, not null, note: '参数主键']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "config_name" varchar(100) [default: '', note: '参数名称']
  "config_key" varchar(100) [default: '', note: '参数键名']
  "config_value" varchar(500) [default: '', note: '参数键值']
  "config_type" char(1) [default: 'N', note: '系统内置（Y是 N否）']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "remark" varchar(500) [default: NULL, note: '备注']
  Note: '参数配置表'
}

Table "sys_dept" {
  "dept_id" bigint [pk, not null, note: '部门id']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "parent_id" bigint [default: 0, note: '父部门id']
  "ancestors" varchar(500) [default: '', note: '祖级列表']
  "dept_name" varchar(100) [default: '', note: '部门名称']
  "dept_category" varchar(100) [default: NULL, note: '部门类别编码']
  "order_num" int [default: 0, note: '显示顺序']
  "leader" bigint [default: NULL, note: '负责人']
  "phone" varchar(11) [default: NULL, note: '联系电话']
  "email" varchar(50) [default: NULL, note: '邮箱']
  "description" varchar(200) [default: NULL, note: '分组描述']
  "email_sign" varchar(500) [default: NULL, note: '邮件签名']
  "dept_type" varchar(300) [default: NULL, note: '分组类型']
  "status" char(1) [default: '0', note: '部门状态（0正常 1停用）']
  "del_flag" char(1) [default: '0', note: '删除标志（0代表存在 1代表删除）']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  Note: '部门表'
}

Table "sys_dict_data" {
  "dict_code" bigint [pk, not null, note: '字典编码']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "dict_sort" int [default: 0, note: '字典排序']
  "dict_label" varchar(100) [default: '', note: '字典标签']
  "dict_value" varchar(100) [default: '', note: '字典键值']
  "dict_type" varchar(100) [default: '', note: '字典类型']
  "css_class" varchar(100) [default: NULL, note: '样式属性（其他样式扩展）']
  "list_class" varchar(100) [default: NULL, note: '表格回显样式']
  "is_default" char(1) [default: 'N', note: '是否默认（Y是 N否）']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "remark" varchar(500) [default: NULL, note: '备注']
  Note: '字典数据表'
}

Table "sys_dict_type" {
  "dict_id" bigint [pk, not null, note: '字典主键']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "dict_name" varchar(100) [default: '', note: '字典名称']
  "dict_type" varchar(100) [default: '', note: '字典类型']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "remark" varchar(500) [default: NULL, note: '备注']

  Indexes {
    (tenant_id, dict_type) [type: btree, unique, name: "tenant_id"]
  }
  Note: '字典类型表'
}

Table "sys_file_upload" {
  "file_id" bigint [pk, not null, note: '文件主键ID']
  "tenant_id" varchar(20) [default: NULL, note: '租户编号']
  "file_name" varchar(255) [not null, default: '', note: '文件名']
  "original_name" varchar(255) [not null, default: '', note: '原始文件名']
  "file_suffix" varchar(10) [not null, default: '', note: '文件后缀名']
  "file_size" bigint [default: 0, note: '文件大小（字节）']
  "file_path" varchar(500) [not null, note: '文件路径']
  "storage_path" varchar(500) [not null, note: '存储路径']
  "directory_date" varchar(10) [default: '', note: '子目录日期(格式：yyyy-MM-dd)']
  "content_type" varchar(100) [default: '', note: '文件类型']
  "download_count" int [default: 0, note: '下载次数']
  "remark" varchar(500) [default: '', note: '备注']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "update_time" datetime [default: NULL, note: '更新时间']
  Note: '文件上传下载表'
}

Table "sys_logininfor" {
  "info_id" bigint [pk, not null, note: '访问ID']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "user_name" varchar(50) [default: '', note: '用户账号']
  "client_key" varchar(32) [default: '', note: '客户端']
  "device_type" varchar(32) [default: '', note: '设备类型']
  "ipaddr" varchar(128) [default: '', note: '登录IP地址']
  "login_location" varchar(255) [default: '', note: '登录地点']
  "browser" varchar(50) [default: '', note: '浏览器类型']
  "os" varchar(50) [default: '', note: '操作系统']
  "status" char(1) [default: '0', note: '登录状态（0成功 1失败）']
  "msg" varchar(255) [default: '', note: '提示消息']
  "login_time" datetime [default: NULL, note: '访问时间']

  Indexes {
    status [type: btree, name: "idx_sys_logininfor_s"]
    login_time [type: btree, name: "idx_sys_logininfor_lt"]
  }
  Note: '系统访问记录'
}

Table "sys_menu" {
  "menu_id" bigint [pk, not null, note: '菜单ID']
  "menu_name" varchar(50) [not null, note: '菜单名称']
  "parent_id" bigint [default: 0, note: '父菜单ID']
  "order_num" int [default: 0, note: '显示顺序']
  "path" varchar(200) [default: '', note: '路由地址']
  "component" varchar(255) [default: NULL, note: '组件路径']
  "query_param" varchar(255) [default: NULL, note: '路由参数']
  "is_frame" int [default: 1, note: '是否为外链（0是 1否）']
  "is_cache" int [default: 0, note: '是否缓存（0缓存 1不缓存）']
  "menu_type" char(1) [default: '', note: '菜单类型（M目录 C菜单 F按钮）']
  "visible" char(1) [default: '0', note: '显示状态（0显示 1隐藏）']
  "status" char(1) [default: '0', note: '菜单状态（0正常 1停用）']
  "perms" varchar(100) [default: NULL, note: '权限标识']
  "icon" varchar(100) [default: '#', note: '菜单图标']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "remark" varchar(500) [default: '', note: '备注']
  Note: '菜单权限表'
}

Table "sys_notice" {
  "notice_id" bigint [pk, not null, note: '公告ID']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "notice_title" varchar(50) [not null, note: '公告标题']
  "notice_type" char(1) [not null, note: '公告类型（1通知 2公告）']
  "notice_content" longblob [note: '公告内容']
  "status" char(1) [default: '0', note: '公告状态（0正常 1关闭）']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "remark" varchar(255) [default: NULL, note: '备注']
  Note: '通知公告表'
}

Table "sys_oper_log" {
  "oper_id" bigint [pk, not null, note: '日志主键']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "title" varchar(50) [default: '', note: '模块标题']
  "business_type" int [default: 0, note: '业务类型（0其它 1新增 2修改 3删除）']
  "method" varchar(100) [default: '', note: '方法名称']
  "request_method" varchar(10) [default: '', note: '请求方式']
  "operator_type" int [default: 0, note: '操作类别（0其它 1后台用户 2手机端用户）']
  "oper_name" json [note: '操作人员']
  "dept_name" varchar(50) [default: '', note: '部门名称']
  "oper_url" varchar(255) [default: '', note: '请求URL']
  "oper_ip" varchar(128) [default: '', note: '主机地址']
  "oper_location" varchar(255) [default: '', note: '操作地点']
  "oper_param_original" longtext [note: '请求参数']
  "oper_param" longtext [note: '请求参数']
  "json_result" mediumtext [note: '返回参数']
  "status" int [default: 0, note: '操作状态（0正常 1异常）']
  "error_msg" varchar(4000) [default: '', note: '错误消息']
  "oper_time" datetime [default: NULL, note: '操作时间']
  "cost_time" bigint [default: 0, note: '消耗时间']

  Indexes {
    business_type [type: btree, name: "idx_sys_oper_log_bt"]
    status [type: btree, name: "idx_sys_oper_log_s"]
    oper_time [type: btree, name: "idx_sys_oper_log_ot"]
  }
  Note: '操作日志记录'
}

Table "sys_oss" {
  "oss_id" bigint [pk, not null, note: '对象存储主键']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "file_name" varchar(255) [not null, default: '', note: '文件名']
  "original_name" varchar(255) [not null, default: '', note: '原名']
  "file_suffix" varchar(10) [not null, default: '', note: '文件后缀名']
  "url" varchar(500) [not null, note: 'URL地址']
  "ext1" text [note: '扩展字段']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_time" datetime [default: NULL, note: '创建时间']
  "create_by" bigint [default: NULL, note: '上传人']
  "update_time" datetime [default: NULL, note: '更新时间']
  "update_by" bigint [default: NULL, note: '更新人']
  "service" varchar(20) [not null, default: 'minio', note: '服务商']
  Note: 'OSS对象存储表'
}

Table "sys_oss_config" {
  "oss_config_id" bigint [pk, not null, note: '主键']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "config_key" varchar(20) [not null, default: '', note: '配置key']
  "access_key" varchar(255) [default: '', note: 'accessKey']
  "secret_key" varchar(255) [default: '', note: '秘钥']
  "bucket_name" varchar(255) [default: '', note: '桶名称']
  "prefix" varchar(255) [default: '', note: '前缀']
  "endpoint" varchar(255) [default: '', note: '访问站点']
  "domain" varchar(255) [default: '', note: '自定义域名']
  "is_https" char(1) [default: 'N', note: '是否https（Y=是,N=否）']
  "region" varchar(255) [default: '', note: '域']
  "access_policy" char(1) [not null, default: '1', note: '桶权限类型(0=private 1=public 2=custom)']
  "status" char(1) [default: '1', note: '是否默认（0=是,1=否）']
  "ext1" varchar(255) [default: '', note: '扩展字段']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "remark" varchar(500) [default: NULL, note: '备注']
  Note: '对象存储配置表'
}

Table "sys_post" {
  "post_id" bigint [pk, not null, note: '岗位ID']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "dept_id" bigint [not null, note: '部门id']
  "post_code" varchar(64) [not null, note: '岗位编码']
  "post_category" varchar(100) [default: NULL, note: '岗位类别编码']
  "post_name" varchar(50) [not null, note: '岗位名称']
  "post_sort" int [not null, note: '显示顺序']
  "status" char(1) [not null, note: '状态（0正常 1停用）']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "remark" varchar(500) [default: NULL, note: '备注']
  Note: '岗位信息表'
}

Table "sys_role" {
  "role_id" bigint [pk, not null, note: '角色ID']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "role_name" varchar(100) [not null, note: '角色名称']
  "role_key" varchar(100) [not null, note: '角色权限字符串']
  "role_sort" int [not null, note: '显示顺序']
  "data_scope" char(1) [default: '1', note: '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限 6：部门及以下或本人数据权限）']
  "menu_check_strictly" tinyint(1) [default: 1, note: '菜单树选择项是否关联显示']
  "dept_check_strictly" tinyint(1) [default: 1, note: '部门树选择项是否关联显示']
  "status" char(1) [not null, note: '角色状态（0正常 1停用）']
  "description" varchar(500) [default: NULL, note: '描述']
  "del_flag" char(1) [default: '0', note: '删除标志（0代表存在 1代表删除）']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "remark" varchar(500) [default: NULL, note: '备注']
  Note: '角色信息表'
}

Table "sys_role_dept" {
  "role_id" bigint [not null, note: '角色ID']
  "dept_id" bigint [not null, note: '部门ID']

  Indexes {
    (role_id, dept_id) [pk, type: btree]
  }
  Note: '角色和部门关联表'
}

Table "sys_role_menu" {
  "role_id" bigint [not null, note: '角色ID']
  "menu_id" bigint [not null, note: '菜单ID']

  Indexes {
    (role_id, menu_id) [pk, type: btree]
  }
  Note: '角色和菜单关联表'
}

Table "sys_social" {
  "id" bigint [pk, not null, note: '主键']
  "user_id" bigint [not null, note: '用户ID']
  "tenant_id" varchar(20) [default: '000000', note: '租户id']
  "auth_id" varchar(255) [not null, note: '平台+平台唯一id']
  "source" varchar(255) [not null, note: '用户来源']
  "open_id" varchar(255) [default: NULL, note: '平台编号唯一id']
  "user_name" varchar(30) [not null, note: '登录账号']
  "nick_name" varchar(30) [default: '', note: '用户昵称']
  "email" varchar(255) [default: '', note: '用户邮箱']
  "avatar" varchar(500) [default: '', note: '头像地址']
  "access_token" varchar(255) [not null, note: '用户的授权令牌']
  "expire_in" int [default: NULL, note: '用户的授权令牌的有效期，部分平台可能没有']
  "refresh_token" varchar(255) [default: NULL, note: '刷新令牌，部分平台可能没有']
  "access_code" varchar(255) [default: NULL, note: '平台的授权信息，部分平台可能没有']
  "union_id" varchar(255) [default: NULL, note: '用户的 unionid']
  "scope" varchar(255) [default: NULL, note: '授予的权限，部分平台可能没有']
  "token_type" varchar(255) [default: NULL, note: '个别平台的授权信息，部分平台可能没有']
  "id_token" varchar(2000) [default: NULL, note: 'id token，部分平台可能没有']
  "mac_algorithm" varchar(255) [default: NULL, note: '小米平台用户的附带属性，部分平台可能没有']
  "mac_key" varchar(255) [default: NULL, note: '小米平台用户的附带属性，部分平台可能没有']
  "code" varchar(255) [default: NULL, note: '用户的授权code，部分平台可能没有']
  "oauth_token" varchar(255) [default: NULL, note: 'Twitter平台用户的附带属性，部分平台可能没有']
  "oauth_token_secret" varchar(255) [default: NULL, note: 'Twitter平台用户的附带属性，部分平台可能没有']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "del_flag" char(1) [default: '0', note: '删除标志（0代表存在 1代表删除）']
  Note: '社会化关系表'
}

Table "sys_tenant" {
  "id" bigint [pk, not null, note: 'id']
  "tenant_id" varchar(20) [not null, note: '租户编号']
  "contact_user_name" varchar(20) [default: NULL, note: '联系人']
  "contact_phone" varchar(20) [default: NULL, note: '联系电话']
  "company_name" varchar(30) [default: NULL, note: '企业名称']
  "license_number" varchar(30) [default: NULL, note: '统一社会信用代码']
  "address" varchar(200) [default: NULL, note: '地址']
  "intro" varchar(200) [default: NULL, note: '企业简介']
  "domain" varchar(200) [default: NULL, note: '域名']
  "remark" varchar(200) [default: NULL, note: '备注']
  "package_id" bigint [default: NULL, note: '租户套餐编号']
  "expire_time" datetime [default: NULL, note: '过期时间']
  "account_count" int [default: `-1`, note: '用户数量（-1不限制）']
  "status" char(1) [default: '0', note: '租户状态（0正常 1停用）']
  "del_flag" char(1) [default: '0', note: '删除标志（0代表存在 1代表删除）']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  Note: '租户表'
}

Table "sys_tenant_package" {
  "package_id" bigint [pk, not null, note: '租户套餐id']
  "package_name" varchar(20) [default: NULL, note: '套餐名称']
  "menu_ids" varchar(3000) [default: NULL, note: '关联菜单id']
  "remark" varchar(200) [default: NULL, note: '备注']
  "menu_check_strictly" tinyint(1) [default: 1, note: '菜单树选择项是否关联显示']
  "status" char(1) [default: '0', note: '状态（0正常 1停用）']
  "del_flag" char(1) [default: '0', note: '删除标志（0代表存在 1代表删除）']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  Note: '租户套餐表'
}

Table "sys_user" {
  "user_id" bigint [pk, not null, note: '用户ID']
  "staff_id" varchar(20) [not null, note: 'StaffId']
  "staff_name" varchar(50) [not null, note: 'StaffName']
  "tenant_id" varchar(20) [default: '000000', note: '租户编号']
  "dept_id" bigint [default: NULL, note: '部门ID']
  "user_name" varchar(30) [not null, note: '用户账号']
  "user_type" varchar(10) [default: 'sys_user', note: '用户类型（sys_user系统用户）']
  "email" varchar(50) [default: '', note: '用户邮箱']
  "phonenumber" varchar(11) [default: '', note: '手机号码']
  "sex" char(1) [default: '0', note: '用户性别（0男 1女 2未知）']
  "avatar" bigint [default: NULL, note: '头像地址']
  "password" varchar(100) [default: '', note: '密码']
  "status" char(1) [default: '0', note: '帐号状态（1正常 0停用）']
  "team_leader" json [note: '对应Team leader']
  "team_manager" json [note: '对应Team Manager']
  "direct_leader" json [note: '直属领导']
  "create_type" tinyint [default: NULL, note: '创建方式（0-新增 1-excel导入）']
  "sync_status" tinyint [default: NULL, note: '同步状态 （1-同步成功）']
  "del_flag" varchar(1) [default: '0', note: '删除标志（0代表存在 1代表删除）']
  "login_ip" varchar(128) [default: '', note: '最后登录IP']
  "login_date" datetime [default: NULL, note: '最后登录时间']
  "create_dept" bigint [default: NULL, note: '创建部门']
  "create_by" bigint [default: NULL, note: '创建者']
  "create_time" datetime [default: NULL, note: '创建时间']
  "update_by" bigint [default: NULL, note: '更新者']
  "update_time" datetime [default: NULL, note: '更新时间']
  "remark" varchar(500) [default: NULL, note: '备注']
  Note: '用户信息表'
}

Table "sys_user_post" {
  "user_id" bigint [not null, note: '用户ID']
  "post_id" bigint [not null, note: '岗位ID']

  Indexes {
    (user_id, post_id) [pk, type: btree]
  }
  Note: '用户与岗位关联表'
}

Table "sys_user_role" {
  "user_id" bigint [not null, note: '用户ID']
  "role_id" bigint [not null, note: '角色ID']

  Indexes {
    (user_id, role_id) [pk, type: btree]
  }
  Note: '用户和角色关联表'
}

// Foreign Key Relationships

// Change Management Relationships
Ref: cm_change_delay.change_id > cm_change_info.id
Ref: cm_change_item.change_id > cm_change_info.id
Ref: cm_change_record.change_item_id > cm_change_item.id
Ref: cm_change_record.change_id > cm_change_info.id

// Network Freeze Relationships
Ref: cm_network_freeze_item.freeze_id > cm_network_freeze_info.id
Ref: cm_network_freeze_record.freeze_item_id > cm_network_freeze_item.id
Ref: cm_network_freeze_record.freeze_id > cm_network_freeze_info.id
Ref: cm_network_freeze_area.freeze_id > cm_network_freeze_info.id

// System User and Department Relationships
Ref: sys_user.dept_id > sys_dept.dept_id
Ref: sys_post.dept_id > sys_dept.dept_id
Ref: sys_dept.parent_id > sys_dept.dept_id

// Role and Permission Relationships
Ref: sys_role_dept.role_id > sys_role.role_id
Ref: sys_role_dept.dept_id > sys_dept.dept_id
Ref: sys_role_menu.role_id > sys_role.role_id
Ref: sys_role_menu.menu_id > sys_menu.menu_id
Ref: sys_user_post.user_id > sys_user.user_id
Ref: sys_user_post.post_id > sys_post.post_id
Ref: sys_user_role.user_id > sys_user.user_id
Ref: sys_user_role.role_id > sys_role.role_id

// Social and Tenant Relationships
Ref: sys_social.user_id > sys_user.user_id
Ref: sys_tenant.package_id > sys_tenant_package.package_id

// Dictionary Relationships
Ref: sys_dict_data.dict_type > sys_dict_type.dict_type

// Menu Hierarchy
Ref: sys_menu.parent_id > sys_menu.menu_id

// Business Logic Relationships Only (Excluding Audit Fields)
// Department leader relationship
Ref: sys_dept.leader > sys_user.user_id

// Team ID relationships (team_id -> sys_dept.dept_id)
Ref: cm_application_manage.team_id > sys_dept.dept_id
Ref: cm_change_info.team_id > sys_dept.dept_id
Ref: cm_change_item.team_id > sys_dept.dept_id
Ref: cm_change_record.team_id > sys_dept.dept_id
